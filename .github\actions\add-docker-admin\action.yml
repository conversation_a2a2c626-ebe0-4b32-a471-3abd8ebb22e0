name: 'Add docker admin'
description: 'Add docker admin'
runs:
  using: "composite"
  steps:
    - name: Add docker credentials from <PERSON><PERSON>
      uses: hashicorp/vault-action@v2.8.0
      shell: bash
      with:
        url: https://vault.ospgroup.io.vn
        token: ${{ secrets.VAULT_TOKEN }}
        secrets: |
          kv/data/ospcore1.0/ci OSP_REGISTRY | OSP_REGISTRY  ;
          kv/data/ospcore1.0/ci OSP_REGISTRY_USERNAME | OSP_REGISTRY_USERNAME ;
          kv/data/ospcore1.0/ci OSP_REGISTRY_PASSWORD | OSP_REGISTRY_PASSWORD
