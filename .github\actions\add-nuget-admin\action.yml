name: 'Add nuget admin'
description: 'Add nuget admin credentials from <PERSON><PERSON>'
inputs:
  vault-token:
    description: 'Vault token for authentication'
    required: true
runs:
  using: "composite"
  steps:
    - name: Add nuget credentials from <PERSON><PERSON>
      uses: hashicorp/vault-action@v2.8.0
      with:
        url: https://vault.ospgroup.io.vn
        token: ${{ inputs.vault-token }}
        secrets: |
          kv/data/ospcore1.0/ci OSP_NUGET_PACKAGE_URL | OSP_NUGET_PACKAGE_URL  ;
          kv/data/ospcore1.0/ci OSP_PACKAGE_USERNAME | OSP_PACKAGE_USERNAME ;
          kv/data/ospcore1.0/ci OSP_PACKAGE_PASSWORD | OSP_PACKAGE_PASSWORD
