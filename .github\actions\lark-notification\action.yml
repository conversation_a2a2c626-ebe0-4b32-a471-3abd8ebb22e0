name: 'Lark Notification'
description: 'Send notification to Lark webhook'
inputs:
  webhook-url:
    description: 'Lark webhook URL'
    required: true
  title:
    description: 'Notification title'
    required: true
  message:
    description: 'Notification message'
    required: true
  status:
    description: 'Build/Deploy status (success, failure, started)'
    required: true
  repository:
    description: 'Repository name'
    required: true
  branch:
    description: 'Branch name'
    required: true
  commit-sha:
    description: 'Commit SHA'
    required: true
  commit-message:
    description: 'Commit message'
    required: false
    default: ''
  commit-author:
    description: 'Commit author'
    required: false
    default: ''
  build-duration:
    description: 'Build duration'
    required: false
    default: ''
  workflow-url:
    description: 'GitHub workflow URL'
    required: false
    default: ''

runs:
  using: 'composite'
  steps:
    - name: Send Lark Notification
      shell: bash
      run: |
        # Determine color and Vietnamese status text based on status
        case "${{ inputs.status }}" in
          "success")
            color="green"
            emoji="✅"
            status_vn="Thành công"
            ;;
          "failure")
            color="red"
            emoji="❌"
            status_vn="Thất bại"
            ;;
          "started")
            color="blue"
            emoji="🚀"
            status_vn="Bắt đầu"
            ;;
          *)
            color="grey"
            emoji="ℹ️"
            status_vn="Thông tin"
            ;;
        esac

        # Handle merge commit messages - get previous commit if message starts with "Merge"
        commit_msg="${{ inputs.commit-message }}"
        if [[ "$commit_msg" == Merge* ]]; then
          # Try to get the previous commit message
          prev_commit_msg=$(git log --format=%B -n 1 HEAD~1 2>/dev/null | head -1 || echo "")
          if [ -n "$prev_commit_msg" ]; then
            commit_msg="$prev_commit_msg"
          fi
        fi

        # Build rich message content in Vietnamese
        message_content="${emoji} **${{ inputs.title }}** - ${status_vn}<br/><br/>"
        message_content+="📦 **Kho mã nguồn:** ${{ inputs.repository }}<br/>"
        message_content+="🌿 **Nhánh:** ${{ inputs.branch }}<br/>"
        message_content+="📝 **Commit:** [\`${{ inputs.commit-sha }}\`](https://github.com/${{ inputs.repository }}/commit/${{ inputs.commit-sha }})<br/>"

        if [ -n "${{ inputs.commit-author }}" ] && [ "${{ inputs.commit-author }}" != "N/A" ] && [ "${{ inputs.commit-author }}" != "" ]; then
          message_content+="👤 **Tác giả:** ${{ inputs.commit-author }}<br/>"
        fi

        if [ -n "$commit_msg" ] && [ "$commit_msg" != "N/A" ] && [ "$commit_msg" != "" ]; then
          message_content+="💬 **Thông điệp:** ${commit_msg}<br/>"
        fi

        if [ -n "${{ inputs.build-duration }}" ] && [ "${{ inputs.build-duration }}" != "N/A" ] && [ "${{ inputs.build-duration }}" != "" ]; then
          message_content+="⏱️ **Thời gian:** ${{ inputs.build-duration }}<br/>"
        fi

        if [ -n "${{ inputs.workflow-url }}" ] && [ "${{ inputs.workflow-url }}" != "" ]; then
          message_content+="🔗 **Chi tiết:** [Xem workflow](${{ inputs.workflow-url }})<br/>"
        fi

        # Escape special characters for JSON
        title_escaped=$(echo "${{ inputs.title }}" | sed 's/"/\\"/g')
        message_escaped=$(echo "$message_content" | sed 's/"/\\"/g')

        # Create JSON payload for Lark using variables
        json_payload="{
          \"msg_type\": \"interactive\",
          \"card\": {
            \"config\": {
              \"wide_screen_mode\": true,
              \"enable_forward\": true
            },
            \"elements\": [
              {
                \"tag\": \"div\",
                \"text\": {
                  \"content\": \"${message_escaped}\",
                  \"tag\": \"lark_md\"
                }
              }
            ],
            \"header\": {
              \"title\": {
                \"content\": \"${title_escaped}\",
                \"tag\": \"plain_text\"
              },
              \"template\": \"${color}\"
            }
          }
        }"

        # Send notification to Lark
        if [ -n "${{ inputs.webhook-url }}" ] && [ "${{ inputs.webhook-url }}" != "" ]; then
          echo "✅ Sending notification to Lark webhook..."
          curl -k -X POST "${{ inputs.webhook-url }}" \
            -H "Content-Type: application/json" \
            -d "$json_payload" \
            --fail --silent --show-error || echo "Failed to send Lark notification"
          echo "✅ Notification sent successfully!"
        else
          echo "❌ Lark webhook URL not configured, skipping notification"
        fi