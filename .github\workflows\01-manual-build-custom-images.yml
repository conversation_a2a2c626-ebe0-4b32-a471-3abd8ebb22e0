name: "01 - 🎛️ Manual Build Custom Docker Images"

on:
  workflow_dispatch:
    inputs:
      image_to_build:
        description: 'Chọn image cần build'
        required: true
        type: choice
        options:
          - all
          - osp-custom-runner
          - osp-custom-runner-nodejs-20
          - osp-custom-runner-dotnet-9
          - osp-custom-runner-java-21
          - sonarqube
        default: 'all'
      runner_version:
        description: 'GitHub Actions Runner version (cho osp-custom-runner)'
        required: false
        default: '2.328.0'
        type: string
      node_version:
        description: 'Node.js version (cho nodejs-20)'
        required: false
        default: '20'
        type: string
      pnpm_version:
        description: 'PNPM version (cho nodejs-20)'
        required: false
        default: '10'
        type: string
      dotnet_version:
        description: '.NET version (cho dotnet-9)'
        required: false
        default: '9.0'
        type: string
      java_version:
        description: 'Java version (cho java-21)'
        required: false
        default: '21'
        type: string
      maven_version:
        description: 'Maven version (cho java-21)'
        required: false
        default: '3.9.9'
        type: string
      sonarqube_version:
        description: 'SonarQube version (cho sonarqube)'
        required: false
        default: '10.6'
        type: string
      plugin_version:
        description: 'Community Branch Plugin version (cho sonarqube)'
        required: false
        default: '1.21.0'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

jobs:
  build-osp-custom-runner:
    name: 🏃 Build OSP Custom Runner
    if: ${{ inputs.image_to_build == 'all' || inputs.image_to_build == 'osp-custom-runner' }}
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/osp-custom-runner'
      image-name: 'osp-custom-runner'
      build-args: '{"RUNNER_VERSION": "${{ inputs.runner_version }}"}'
      force-rebuild: ${{ inputs.force_rebuild }}
      enable-test: false
      enable-security-scan: false
      push-to-registry: true
      create-git-tag: true
      notification-title: "📋 Manual Build - OSP Custom Runner"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-osp-custom-runner-nodejs20:
    name: 🏃 Build OSP Custom Runner Node.js 20
    if: ${{ inputs.image_to_build == 'all' || inputs.image_to_build == 'osp-custom-runner-nodejs-20' }}
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/nodejs-20'
      image-name: 'osp-custom-runner-nodejs-20'
      build-args: >-
        {
          "NODE_VERSION": "${{ inputs.node_version }}",
          "PNPM_VERSION": "${{ inputs.pnpm_version }}",
          "RUNNER_VERSION": "${{ inputs.runner_version }}"
        }
      force-rebuild: ${{ inputs.force_rebuild }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: true
      create-git-tag: true
      notification-title: "📋 Manual Build - OSP Custom Runner Node.js 20"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-osp-custom-runner-dotnet9:
    name: 🏃 Build OSP Custom Runner .NET 9
    if: ${{ inputs.image_to_build == 'all' || inputs.image_to_build == 'osp-custom-runner-dotnet-9' }}
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/dotnet-9'
      image-name: 'osp-custom-runner-dotnet-9'
      build-args: '{"DOTNET_VERSION": "${{ inputs.dotnet_version }}"}'
      force-rebuild: ${{ inputs.force_rebuild }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: true
      create-git-tag: true
      notification-title: "📋 Manual Build - OSP Custom Runner .NET 9"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-osp-custom-runner-java21:
    name: 🏃 Build OSP Custom Runner Java 21
    if: ${{ inputs.image_to_build == 'all' || inputs.image_to_build == 'osp-custom-runner-java-21' }}
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/java-21'
      image-name: 'osp-custom-runner-java-21'
      build-args: >-
        {
          "JAVA_VERSION": "${{ inputs.java_version }}",
          "MAVEN_VERSION": "${{ inputs.maven_version }}",
          "RUNNER_VERSION": "${{ inputs.runner_version }}"
        }
      force-rebuild: ${{ inputs.force_rebuild }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: true
      create-git-tag: true
      notification-title: "📋 Manual Build - OSP Custom Runner Java 21"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-sonarqube:
    name: 🔍 Build SonarQube
    if: ${{ inputs.image_to_build == 'all' || inputs.image_to_build == 'sonarqube' }}
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/sonarqube'
      image-name: 'sonarqube-community-branch'
      build-args: '{"SONARQUBE_VERSION": "${{ inputs.sonarqube_version }}", "PLUGIN_VERSION": "${{ inputs.plugin_version }}"}'
      force-rebuild: ${{ inputs.force_rebuild }}
      enable-test: false
      enable-security-scan: false
      push-to-registry: true
      create-git-tag: true
      notification-title: "📋 Manual Build - SonarQube"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-summary:
    name: 📊 Build Summary
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [build-osp-custom-runner, build-osp-custom-runner-nodejs20, build-osp-custom-runner-dotnet9, build-osp-custom-runner-java21, build-sonarqube]
    if: always()
    timeout-minutes: 5

    steps:
      - name: 📊 Generate Build Summary
        run: |
          echo "=== 📊 MANUAL BUILD SUMMARY ==="
          echo "Requested by: ${{ github.actor }}"
          echo "Build scope: ${{ inputs.image_to_build }}"
          echo "Force rebuild: ${{ inputs.force_rebuild }}"
          echo ""

          echo "=== 🎯 BUILD RESULTS ==="
          if [[ "${{ inputs.image_to_build }}" == "all" || "${{ inputs.image_to_build }}" == "osp-custom-runner" ]]; then
            echo "OSP Custom Runner: ${{ needs.build-osp-custom-runner.result }}"
            if [[ "${{ needs.build-osp-custom-runner.result }}" == "success" ]]; then
              echo "  ✅ Image: ${{ needs.build-osp-custom-runner.outputs.image-tag }}"
              echo "  📦 Version: ${{ needs.build-osp-custom-runner.outputs.image-version }}"
            fi
          fi

          if [[ "${{ inputs.image_to_build }}" == "all" || "${{ inputs.image_to_build }}" == "osp-custom-runner-nodejs-20" ]]; then
            echo "OSP Custom Runner Node.js 20: ${{ needs.build-osp-custom-runner-nodejs20.result }}"
            if [[ "${{ needs.build-osp-custom-runner-nodejs20.result }}" == "success" ]]; then
              echo "  ✅ Image: ${{ needs.build-osp-custom-runner-nodejs20.outputs.image-tag }}"
              echo "  📦 Version: ${{ needs.build-osp-custom-runner-nodejs20.outputs.image-version }}"
            fi
          fi

          if [[ "${{ inputs.image_to_build }}" == "all" || "${{ inputs.image_to_build }}" == "osp-custom-runner-dotnet-9" ]]; then
            echo "OSP Custom Runner .NET 9: ${{ needs.build-osp-custom-runner-dotnet9.result }}"
            if [[ "${{ needs.build-osp-custom-runner-dotnet9.result }}" == "success" ]]; then
              echo "  ✅ Image: ${{ needs.build-osp-custom-runner-dotnet9.outputs.image-tag }}"
              echo "  📦 Version: ${{ needs.build-osp-custom-runner-dotnet9.outputs.image-version }}"
            fi
          fi

          if [[ "${{ inputs.image_to_build }}" == "all" || "${{ inputs.image_to_build }}" == "osp-custom-runner-java-21" ]]; then
            echo "OSP Custom Runner Java 21: ${{ needs.build-osp-custom-runner-java21.result }}"
            if [[ "${{ needs.build-osp-custom-runner-java21.result }}" == "success" ]]; then
              echo "  ✅ Image: ${{ needs.build-osp-custom-runner-java21.outputs.image-tag }}"
              echo "  📦 Version: ${{ needs.build-osp-custom-runner-java21.outputs.image-version }}"
            fi
          fi

          if [[ "${{ inputs.image_to_build }}" == "all" || "${{ inputs.image_to_build }}" == "sonarqube" ]]; then
            echo "SonarQube: ${{ needs.build-sonarqube.result }}"
            if [[ "${{ needs.build-sonarqube.result }}" == "success" ]]; then
              echo "  ✅ Image: ${{ needs.build-sonarqube.outputs.image-tag }}"
              echo "  📦 Version: ${{ needs.build-sonarqube.outputs.image-version }}"
            fi
          fi

          echo ""
          if [[ "${{ needs.build-osp-custom-runner.result }}" != "failure" && "${{ needs.build-osp-custom-runner-nodejs20.result }}" != "failure" && "${{ needs.build-osp-custom-runner-dotnet9.result }}" != "failure" && "${{ needs.build-osp-custom-runner-java21.result }}" != "failure" && "${{ needs.build-sonarqube.result }}" != "failure" ]]; then
            echo "🎉 Manual build completed successfully!"
          else
            echo "❌ Some builds failed, please check the logs above."
          fi