name: "04 - 🔐 Keycloak Themes Auto Build"

on:
  push:
    paths:
      - 'custom-docker-images/keycloak-themes/**'
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'custom-docker-images/keycloak-themes/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      keycloak_version:
        description: 'Keycloak version'
        required: false
        default: '24.0.4'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

env:
  REGISTRY: dockerhub.ospgroup.vn
  IMAGE_NAME: keycloak-custom-themes

jobs:
  build-keycloak-themes:
    name: 🔧 Build Keycloak với Custom Themes
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/keycloak-themes'
      image-name: 'keycloak-custom-themes'
      build-args: "{\"KEYCLOAK_VERSION\": \"${{ github.event_name == 'workflow_dispatch' && inputs.keycloak_version || '24.0.4' }}\"}"
      force-rebuild: ${{ github.event_name == 'workflow_dispatch' && inputs.force_rebuild || false }}
      enable-test: true
      test-command: 'curl -f http://localhost:8080/health/ready'
      test-port: '8080'
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "Keycloak Themes Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  update-values:
    name: 📝 Update Keycloak Values
    needs: build-keycloak-themes
    runs-on: self-hosted
    if: github.event_name != 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔄 Update values.yaml
        run: |
          echo "=== 🔄 UPDATING KEYCLOAK VALUES.YAML ==="

          # Get the new image tag from previous job
          NEW_TAG="${{ needs.build-keycloak-themes.outputs.image-version }}"

          if [[ -z "$NEW_TAG" ]]; then
            echo "❌ Error: Could not get image version from build job"
            exit 1
          fi

          echo "New image tag: $NEW_TAG"

          # Path to values.yaml
          VALUES_FILE="platform-services/charts/keycloak/values.yaml"

          if [[ ! -f "$VALUES_FILE" ]]; then
            echo "❌ Error: values.yaml not found at $VALUES_FILE"
            exit 1
          fi

          # Update the tag in values.yaml
          # Look for the tag line under keycloak.image
          sed -i.bak "s|    tag: \".*\"  # Sẽ được update tự động bởi CI/CD|    tag: \"$NEW_TAG\"  # Sẽ được update tự động bởi CI/CD|" "$VALUES_FILE"

          echo "✅ Updated values.yaml with new tag: $NEW_TAG"

          # Show the updated section
          echo "📋 Updated values.yaml section:"
          grep -A 5 -B 5 "tag:" "$VALUES_FILE"

      - name: 📤 Commit and Push Changes
        run: |
          echo "=== 📤 COMMITTING CHANGES ==="

          # Check if there are changes to commit
          if git diff --quiet; then
            echo "ℹ️ No changes to commit"
            exit 0
          fi

          # Configure git
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          # Add and commit
          git add platform-services/charts/keycloak/values.yaml
          git commit -m "🔄 Update Keycloak image tag to ${{ needs.build-keycloak-themes.outputs.image-version }}

          Auto-updated by CI/CD after building custom Keycloak themes image.
          Image: ${{ needs.build-keycloak-themes.outputs.image-tag }}"

          # Push changes
          git push

          echo "✅ Successfully committed and pushed values.yaml update"