name: "03 - 🏃 OSP Custom Runner .NET 9 Auto Build"

on:
  push:
    paths:
      - 'custom-docker-images/dotnet-9/**'
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'custom-docker-images/dotnet-9/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      dotnet_version:
        description: '.NET version'
        required: false
        default: '9.0'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

jobs:
  build-osp-custom-runner-dotnet9:
    name: 🔧 Build OSP Custom Runner .NET 9
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/dotnet-9'
      image-name: 'osp-custom-runner-dotnet-9'
      build-args: "{\"DOTNET_VERSION\": \"${{ github.event_name == 'workflow_dispatch' && inputs.dotnet_version || '9.0' }}\"}"
      force-rebuild: ${{ github.event_name == 'workflow_dispatch' && inputs.force_rebuild || false }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "OSP Custom Runner .NET 9 Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}