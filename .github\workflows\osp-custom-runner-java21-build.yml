name: "05 - 🏃 OSP Custom Runner Java 21 Auto Build"

on:
  push:
    paths:
      - 'custom-docker-images/java-21/**'
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'custom-docker-images/java-21/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      java_version:
        description: 'Java version'
        required: false
        default: '21'
        type: string
      maven_version:
        description: 'Maven version'
        required: false
        default: '3.9.9'
        type: string
      runner_version:
        description: 'GitHub Actions Runner version'
        required: false
        default: '2.328.0'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

jobs:
  build-osp-custom-runner-java21:
    name: 🔧 Build OSP Custom Runner Java 21
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/java-21'
      image-name: 'osp-custom-runner-java-21'
      build-args: >-
        {
          "JAVA_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.java_version || '21' }}",
          "MAVEN_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.maven_version || '3.9.9' }}",
          "RUNNER_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.runner_version || '2.328.0' }}"
        }
      force-rebuild: ${{ github.event_name == 'workflow_dispatch' && inputs.force_rebuild || false }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "OSP Custom Runner Java 21 Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}