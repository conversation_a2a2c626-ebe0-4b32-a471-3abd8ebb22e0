name: "04 - 🏃 OSP Custom Runner Node.js 20 Auto Build"

on:
  push:
    paths:
      - 'custom-docker-images/nodejs-20/**'
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'custom-docker-images/nodejs-20/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      node_version:
        description: 'Node.js version'
        required: false
        default: '20'
        type: string
      pnpm_version:
        description: 'PNPM version'
        required: false
        default: '10'
        type: string
      runner_version:
        description: 'GitHub Actions Runner version'
        required: false
        default: '2.328.0'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

jobs:
  build-osp-custom-runner-nodejs20:
    name: 🔧 Build OSP Custom Runner Node.js 20
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/nodejs-20'
      image-name: 'osp-custom-runner-nodejs-20'
      build-args: >-
        {
          "NODE_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.node_version || '20' }}",
          "PNPM_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.pnpm_version || '10' }}",
          "RUNNER_VERSION": "${{ github.event_name == 'workflow_dispatch' && inputs.runner_version || '2.328.0' }}"
        }
      force-rebuild: ${{ github.event_name == 'workflow_dispatch' && inputs.force_rebuild || false }}
      enable-test: true
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "OSP Custom Runner Node.js 20 Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}