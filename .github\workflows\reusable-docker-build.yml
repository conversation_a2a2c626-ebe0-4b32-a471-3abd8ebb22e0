name: "🔧 Reusable Docker Build and Push"

on:
  workflow_call:
    inputs:
      dockerfile-path:
        description: 'Path to Dockerfile directory'
        required: true
        type: string
      image-name:
        description: 'Docker image name'
        required: true
        type: string
      build-args:
        description: 'Docker build arguments (JSON format)'
        required: false
        type: string
        default: '{}'
      runner-version:
        description: 'GitHub Actions Runner version (for custom runners)'
        required: false
        type: string
        default: '2.328.0'
      force-rebuild:
        description: 'Force rebuild without cache'
        required: false
        type: boolean
        default: false
      enable-test:
        description: 'Enable image testing'
        required: false
        type: boolean
        default: false
      test-command:
        description: 'Command to test the image'
        required: false
        type: string
        default: ''
      test-port:
        description: 'Port to test for health check'
        required: false
        type: string
        default: ''
      enable-security-scan:
        description: 'Enable security scanning with Trivy'
        required: false
        type: boolean
        default: false
      push-to-registry:
        description: 'Push to registry (false for PR builds)'
        required: false
        type: boolean
        default: true
      create-git-tag:
        description: 'Create git tag for version'
        required: false
        type: boolean
        default: true
      notification-title:
        description: 'Custom notification title'
        required: false
        type: string
        default: 'Docker Build'

    secrets:
      OSP_REGISTRY:
        description: 'Docker registry URL'
        required: true
      OSP_IMAGE_OWNER:
        description: 'Docker image owner/namespace'
        required: true
      OSP_REGISTRY_USERNAME:
        description: 'Registry username'
        required: false
      OSP_REGISTRY_PASSWORD:
        description: 'Registry password'
        required: false
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL for notifications'
        required: false

    outputs:
      image-version:
        description: 'Generated image version'
        value: ${{ jobs.build-and-push.outputs.image-version }}
      image-tag:
        description: 'Full image tag'
        value: ${{ jobs.build-and-push.outputs.image-tag }}
      latest-tag:
        description: 'Latest image tag'
        value: ${{ jobs.build-and-push.outputs.latest-tag }}

jobs:
  build-and-push:
    name: 🔧 Build and Push Docker Image
    runs-on: self-hosted
    timeout-minutes: 45
    permissions:
      contents: write
      packages: write

    outputs:
      image-version: ${{ steps.version.outputs.version }}
      image-tag: ${{ steps.version.outputs.image-tag }}
      latest-tag: ${{ steps.version.outputs.latest-tag }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🐳 Docker Info
        run: |
          echo "=== 🐳 DOCKER INFORMATION ==="
          docker --version
          docker info

      - name: 🏷️ Generate Version
        id: version
        run: |
          echo "=== 🏷️ GENERATING VERSION ==="

          # Get current date and time
          DATE_TIME=$(date +%Y%m%d-%H%M%S)

          # Get latest git tag or default to 1.0.0
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")
          echo "Latest git tag: $LATEST_TAG"

          # Extract version number (remove 'v' prefix if exists)
          VERSION_NUMBER=${LATEST_TAG#v}
          echo "Version number: $VERSION_NUMBER"

          # Split version into parts
          IFS='.' read -r -a VERSION_PARTS <<< "$VERSION_NUMBER"
          MAJOR=${VERSION_PARTS[0]:-1}
          MINOR=${VERSION_PARTS[1]:-0}
          PATCH=${VERSION_PARTS[2]:-0}

          # Auto-increment patch version
          PATCH=$((PATCH + 1))
          NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}"

          echo "New version: $NEW_VERSION"

          # Generate full image tag
          IMAGE_TAG="${{ secrets.OSP_REGISTRY }}/${{ secrets.OSP_IMAGE_OWNER }}/${{ inputs.image-name }}"
          BUILD_TAG="${IMAGE_TAG}:${NEW_VERSION}"
          LATEST_TAG="${IMAGE_TAG}:latest"
          BRANCH_TAG="${IMAGE_TAG}:${GITHUB_REF_NAME}-${GITHUB_SHA:0:7}-${DATE_TIME}"

          echo "Image tags:"
          echo "  Build: $BUILD_TAG"
          echo "  Latest: $LATEST_TAG"
          echo "  Branch: $BRANCH_TAG"

          # Export variables
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "image-tag=$BUILD_TAG" >> $GITHUB_OUTPUT
          echo "latest-tag=$LATEST_TAG" >> $GITHUB_OUTPUT
          echo "branch-tag=$BRANCH_TAG" >> $GITHUB_OUTPUT

          # Set environment variables for later steps
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV
          echo "BUILD_TAG=$BUILD_TAG" >> $GITHUB_ENV
          echo "LATEST_TAG=$LATEST_TAG" >> $GITHUB_ENV
          echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV

      - name: 🔧 Build Docker Image
        run: |
          echo "=== 🔧 BUILDING DOCKER IMAGE ==="
          cd ${{ inputs.dockerfile-path }}

          echo "Building with:"
          echo "  Image Name: ${{ inputs.image-name }}"
          echo "  Image Version: ${NEW_VERSION}"
          echo "  Build Tag: ${BUILD_TAG}"
          echo "  Latest Tag: ${LATEST_TAG}"
          echo "  Branch Tag: ${BRANCH_TAG}"
          echo "  Dockerfile Path: ${{ inputs.dockerfile-path }}"

          # Parse build args from JSON
          BUILD_ARGS=""
          if [[ "${{ inputs.build-args }}" != "{}" && -n "${{ inputs.build-args }}" ]]; then
            echo "Processing build arguments..."
            BUILD_ARGS=$(echo '${{ inputs.build-args }}' | jq -r 'to_entries[] | "--build-arg \(.key)=\(.value)"' | tr '\n' ' ')
            echo "Build args: $BUILD_ARGS"
          fi

          # Add runner version if specified
          if [[ -n "${{ inputs.runner-version }}" && "${{ inputs.runner-version }}" != "2.328.0" ]]; then
            BUILD_ARGS="$BUILD_ARGS --build-arg RUNNER_VERSION=${{ inputs.runner-version }}"
          fi

          # Build image with multiple tags
          echo "Building Docker image..."
          docker build \
            $BUILD_ARGS \
            --tag ${BUILD_TAG} \
            --tag ${LATEST_TAG} \
            --tag ${BRANCH_TAG} \
            ${{ inputs.force-rebuild == true && '--no-cache' || '' }} \
            .

          echo "✅ Docker image built successfully!"

          # Show built images
          echo "Built images:"
          docker images | grep "${{ inputs.image-name }}" | head -5

      - name: 🧪 Test Docker Image
        if: ${{ inputs.enable-test == true }}
        run: |
          echo "=== 🧪 TESTING DOCKER IMAGE ==="

          # Start container for testing
          CONTAINER_NAME="${{ inputs.image-name }}-test-${GITHUB_RUN_NUMBER}"

          if [[ -n "${{ inputs.test-port }}" ]]; then
            # Test with port exposure
            echo "Starting container with port ${{ inputs.test-port }}..."
            docker run --rm --name $CONTAINER_NAME -d -p ${{ inputs.test-port }}:${{ inputs.test-port }} ${BUILD_TAG}

            # Wait for service to start
            echo "Waiting for service to start..."
            for i in {1..30}; do
              if [[ -n "${{ inputs.test-command }}" ]]; then
                if eval "${{ inputs.test-command }}"; then
                  echo "✅ Service test successful!"
                  break
                fi
              else
                if curl -f http://localhost:${{ inputs.test-port }}/health 2>/dev/null || curl -f http://localhost:${{ inputs.test-port }} 2>/dev/null; then
                  echo "✅ Service health check successful!"
                  break
                fi
              fi
              echo "Waiting... (${i}/30)"
              sleep 10
            done

            # Stop test container
            docker stop $CONTAINER_NAME
          else
            # Simple container test
            echo "Running simple container test..."
            if [[ -n "${{ inputs.test-command }}" ]]; then
              docker run --rm --name $CONTAINER_NAME ${BUILD_TAG} ${{ inputs.test-command }}
            else
              docker run --rm --name $CONTAINER_NAME ${BUILD_TAG} echo "Container test passed"
            fi
          fi

          echo "✅ Docker image testing completed!"

      - name: 🔍 Security Scan with Trivy
        if: ${{ inputs.enable-security-scan == true }}
        run: |
          echo "=== 🔍 SECURITY SCANNING ==="

          # Install Trivy if not available
          if ! command -v trivy &> /dev/null; then
            echo "Installing Trivy..."
            curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /tmp/bin v0.48.3
            export PATH="/tmp/bin:$PATH"
          fi

          # Scan the built image
          echo "Scanning image: ${BUILD_TAG}"
          trivy image --format table --exit-code 0 ${BUILD_TAG}

          # Generate SARIF report if possible
          if trivy image --format sarif --output trivy-results.sarif ${BUILD_TAG}; then
            echo "✅ SARIF report generated: trivy-results.sarif"
          else
            echo "⚠️ Could not generate SARIF report"
          fi

      - name: 📦 Push Image to Registry
        if: ${{ inputs.push-to-registry == true && github.event_name != 'pull_request' }}
        run: |
          echo "=== 📦 PUSHING IMAGE TO REGISTRY ==="

          # Login to custom registry (if credentials are available)
          if [[ -n "${{ secrets.OSP_REGISTRY_USERNAME }}" && -n "${{ secrets.OSP_REGISTRY_PASSWORD }}" ]]; then
            echo "Logging in to Registry: ${{ secrets.OSP_REGISTRY }}..."
            echo '${{ secrets.OSP_REGISTRY_PASSWORD }}' | docker login ${{ secrets.OSP_REGISTRY }} --username "${{ secrets.OSP_REGISTRY_USERNAME }}" --password-stdin

            # Push all image tags to registry
            echo "Pushing images to registry..."
            docker push ${BUILD_TAG}
            docker push ${LATEST_TAG}
            docker push ${BRANCH_TAG}

            echo "✅ Images pushed successfully to ${{ secrets.OSP_REGISTRY }}!"
            echo "📦 Images available:"
            echo "   - ${BUILD_TAG}"
            echo "   - ${LATEST_TAG}"
            echo "   - ${BRANCH_TAG}"
          else
            echo "⚠️ No registry credentials found (OSP_REGISTRY_USERNAME/OSP_REGISTRY_PASSWORD), skipping push"
            echo "   Images built locally for testing:"
            echo "   - ${BUILD_TAG}"
            echo "   - ${LATEST_TAG}"
            echo "   - ${BRANCH_TAG}"
          fi

      - name: 🏷️ Create Git Tag
        if: ${{ inputs.create-git-tag == true && inputs.push-to-registry == true && github.event_name != 'pull_request' && github.ref == 'refs/heads/main' }}
        run: |
          echo "=== 🏷️ CREATING GIT TAG ==="

          # Configure git (use GitHub Actor)
          git config user.name "${GITHUB_ACTOR}"
          git config user.email "${GITHUB_ACTOR}@users.noreply.github.com"

          # Create and push tag
          NEW_TAG="v${NEW_VERSION}"
          echo "Creating git tag: $NEW_TAG"

          if git rev-parse "$NEW_TAG" >/dev/null 2>&1; then
            echo "Tag $NEW_TAG already exists, skipping..."
          else
            git tag -a "$NEW_TAG" -m "${{ inputs.image-name }} Docker Image v${NEW_VERSION}"
            git push origin "$NEW_TAG"
            echo "✅ Git tag $NEW_TAG created and pushed successfully!"
          fi

      - name: 🧹 Docker Cleanup
        if: always()
        run: |
          echo "=== 🧹 DOCKER CLEANUP ==="

          # Remove test containers if any
          docker ps -a | grep "${{ inputs.image-name }}-test" | awk '{print $1}' | xargs -r docker rm -f

          # Show disk usage before cleanup
          echo "Disk usage before cleanup:"
          docker system df

          # Clean up old images (keep recent ones)
          docker image prune -f

          # Show disk usage after cleanup
          echo "Disk usage after cleanup:"
          docker system df

      - name: 📊 Calculate Build Duration
        id: calc-duration
        if: always()
        run: |
          start_time="${{ github.event.created_at }}"
          end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

          # Convert to seconds for calculation
          start_epoch=$(date -d "$start_time" +%s 2>/dev/null || date -j -f "%Y-%m-%dT%H:%M:%SZ" "$start_time" +%s 2>/dev/null || echo "0")
          end_epoch=$(date +%s)

          if [ "$start_epoch" != "0" ]; then
            duration_seconds=$((end_epoch - start_epoch))
            duration_minutes=$((duration_seconds / 60))
            duration_remainder=$((duration_seconds % 60))

            if [ $duration_minutes -gt 0 ]; then
              duration="${duration_minutes}m ${duration_remainder}s"
            else
              duration="${duration_seconds}s"
            fi
          else
            duration=""
          fi

          echo "duration=$duration" >> $GITHUB_OUTPUT
          echo "Build duration: $duration"

      - name: ✅ Send Success Notification
        uses: ./.github/workflows/reusable-lark-notification.yml
        if: success()
        with:
          title: "${{ inputs.notification-title }}"
          message: "Build Docker image thành công! Image: ${{ inputs.image-name }}:${{ steps.version.outputs.version }}"
          status: "success"
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || '' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          build-duration: ${{ steps.calc-duration.outputs.duration }}

      - name: ❌ Send Failure Notification
        uses: ./.github/workflows/reusable-lark-notification.yml
        if: failure()
        with:
          title: "${{ inputs.notification-title }}"
          message: "Build Docker image thất bại! Image: ${{ inputs.image-name }}"
          status: "failure"
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || '' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          build-duration: ${{ steps.calc-duration.outputs.duration }}
          workflow-url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

      - name: 🎉 Build Summary
        if: always()
        run: |
          echo "=== 🎉 DOCKER BUILD SUMMARY ==="
          echo "Repository: ${{ github.repository }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Triggered by: ${{ github.event_name }}"
          echo "Image: ${{ inputs.image-name }}"
          echo "Version: ${NEW_VERSION}"
          echo ""

          echo "✅ Docker image build completed!"
          echo "📦 Image tags created:"
          echo "   - ${BUILD_TAG}"
          echo "   - ${LATEST_TAG}"
          echo "   - ${BRANCH_TAG}"