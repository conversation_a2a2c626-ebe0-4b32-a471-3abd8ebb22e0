name: Reusable Flexible Runner

on:
  workflow_call:
    inputs:
      runner_choice:
        description: 'Runner được chỉ định từ workflow_dispatch'
        required: false
        type: string
        default: ''
      job_name:
        description: 'Tên job sẽ hiển thị'
        required: false
        type: string
        default: 'Flexible Runner Job'
      setup_node:
        description: 'Có cần setup Node.js không'
        required: false
        type: boolean
        default: false
      node_version:
        description: 'Phiên bản Node.js'
        required: false
        type: string
        default: '20'
      setup_pnpm:
        description: 'Có cần setup PNPM không'
        required: false
        type: boolean
        default: false
      pnpm_version:
        description: 'Phiên bản PNPM'
        required: false
        type: string
        default: '10'
      working_directory:
        description: 'Thư mục làm việc'
        required: false
        type: string
        default: '.'
      run_command:
        description: 'Command cần chạy'
        required: false
        type: string
        default: 'echo "No command specified"'
      cache_dependencies:
        description: 'Có cache dependencies không'
        required: false
        type: boolean
        default: true
      install_dependencies:
        description: '<PERSON>ó cài dependencies không'
        required: false
        type: string
        default: 'none' # none, frontend
    secrets:
      LARK_CHAT_GROUP_NOTIFICATION:
        description: 'Lark webhook URL (nếu cần)'
        required: false
    outputs:
      runner_used:
        description: 'Runner đã được sử dụng'
        value: ${{ jobs['flexible-runner'].outputs.runner_used }}
      job_result:
        description: 'Kết quả job'
        value: ${{ jobs['flexible-runner'].outputs.job_result }}

jobs:
  flexible-runner:
    name: ${{ inputs.job_name }}

    # Logic chọn runner linh hoạt:
    # 1. Ưu tiên input runner_choice từ workflow_dispatch
    # 2. Nếu không có, dùng repository variable DEFAULT_RUNNER_LABEL
    # 3. Fallback cuối cùng: ubuntu-latest
    runs-on: ${{ inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}

    outputs:
      runner_used: ${{ steps['runner-info'].outputs.runner_used }}
      job_result: ${{ steps['main-job'].outcome }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Display Runner Information
        id: runner-info
        run: |
          echo "🎉 Job is running on runner: ${{ runner.os }}-${{ runner.arch }}"
          echo "---"
          echo "🔍 Debug Information:"
          echo "Manual choice (from input): ${{ inputs.runner_choice }}"
          echo "Configured runner (from var): ${{ vars.DEFAULT_RUNNER_LABEL }}"
          echo "Final runner used: ${{ runner.os }}"
          echo "runner_used=${{ runner.os }}" >> $GITHUB_OUTPUT


      # Conditional PNPM Setup
      - name: Install PNPM ${{ inputs.pnpm_version }}
        if: inputs.setup_pnpm == true
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm_version }}
          run_install: false

      # Conditional Node Setup with caching
      - name: Set up Node.js ${{ inputs.node_version }}
        if: inputs.setup_node == true
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node_version }}
          cache: ${{ inputs.setup_pnpm && inputs.cache_dependencies && 'pnpm' || '' }}
          cache-dependency-path: ${{ inputs.setup_pnpm && inputs.cache_dependencies && 'src/frontend/pnpm-lock.yaml' || '' }}


      # Verify Environment Versions
      - name: Verify environment versions
        if: inputs.setup_node == true
        run: |
          echo "=== Environment Versions ==="
          SETUP_NODE="${{ inputs.setup_node }}"
          SETUP_PNPM="${{ inputs.setup_pnpm }}"
          if [[ "$SETUP_NODE" == "true" ]]; then
            echo "Node version:"
            node -v
          fi
          if [[ "$SETUP_PNPM" == "true" ]]; then
            echo "PNPM version:"
            pnpm -v
          fi
          echo "==========================="

      # Install Frontend Dependencies
      - name: Install Frontend Dependencies
        if: inputs.install_dependencies == 'frontend' || inputs.install_dependencies == 'both'
        working-directory: src/frontend
        run: |
          echo "📦 Installing Frontend dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            echo "🔍 Found pnpm-lock.yaml, trying --frozen-lockfile..."
            if pnpm install --frozen-lockfile; then
              echo "✅ Dependencies installed successfully with pnpm-lock.yaml!"
            else
              echo "⚠️ Frozen lockfile failed, falling back to normal install..."
              pnpm install
              echo "✅ Dependencies installed successfully with fallback!"
            fi
          else
            echo "⚠️ No pnpm-lock.yaml found, installing normally..."
            pnpm install
            echo "✅ Dependencies installed successfully!"
          fi


      # Main Job Execution
      - name: Execute Main Command
        id: main-job
        working-directory: ${{ inputs.working_directory }}
        run: |
          echo "🚀 Executing main command..."
          echo "Command: ${{ inputs.run_command }}"
          echo "Working directory: ${{ inputs.working_directory }}"
          echo "---"
          ${{ inputs.run_command }}