name: Reusable Frontend CD Release

on:
  workflow_call:
    inputs:
      frontend_path:
        description: 'Path to frontend source code'
        required: false
        type: string
        default: 'src/frontend'
      node_version:
        description: 'Node.js version'
        required: false
        type: string
        default: '20'
      pnpm_version:
        description: 'PNPM version'
        required: false
        type: string
        default: '10'
      tag_name:
        description: 'Tag name for package (e.g., v1.0.0)'
        required: true
        type: string
      target_branch:
        description: 'Target branch'
        required: false
        type: string
      force_publish:
        description: 'Force publish even if package exists'
        required: false
        type: boolean
        default: false
      npm_registry_url:
        description: 'NPM registry URL'
        required: false
        type: string
        default: 'https://package.ospgroup.io.vn/repository/npm-hosted/'
      enable_tests:
        description: 'Run tests before publishing'
        required: false
        type: boolean
        default: true
      enable_type_check:
        description: 'Run type check before publishing'
        required: false
        type: boolean
        default: true
      enable_lint:
        description: 'Run linting before publishing'
        required: false
        type: boolean
        default: true
      enable_notifications:
        description: 'Enable Lark notifications'
        required: false
        type: boolean
        default: true
    secrets:
      VAULT_TOKEN:
        description: 'Vault token for authentication'
        required: true
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL for notifications'
        required: false

permissions:
  contents: read
  packages: write

jobs:
  validate-tag:
    name: 'Validate Tag và Version'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      package-version: ${{ steps.version.outputs.package-version }}
      is-valid: ${{ steps.version.outputs.is-valid }}
    steps:
      - name: Validate và normalize version
        id: version
        run: |
          TAG_NAME="${{ inputs.tag_name }}"
          echo "🏷️ Input tag: $TAG_NAME"

          # Remove 'v' prefix if exists
          CLEAN_VERSION=${TAG_NAME#v}
          echo "🔧 Clean version: $CLEAN_VERSION"

          # Validate semantic version format
          if [[ ! $CLEAN_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[-a-zA-Z0-9]+)*)?$ ]]; then
            echo "❌ Invalid version format: $CLEAN_VERSION"
            echo "Version phải có format: 1.0.0 hoặc 1.0.0-beta"
            echo "is-valid=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          echo "✅ Valid version: $CLEAN_VERSION"
          echo "package-version=$CLEAN_VERSION" >> $GITHUB_OUTPUT
          echo "is-valid=true" >> $GITHUB_OUTPUT

  build-and-test:
    name: 'Build và Test NPM Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag]
    if: needs.validate-tag.outputs.is-valid == 'true'
    outputs:
      package-name: ${{ steps.package.outputs.package-name }}
      package-count: ${{ steps.package.outputs.package-count }}
      packages-path: ${{ steps.package.outputs.packages-path }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm_version }}
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node_version }}

      - name: Import NPM credentials from Vault
        uses: ospgroupvn/k8s-deployment/.github/actions/add-npm-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Configure NPM registry
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔧 Configuring NPM registry..."
          pnpm config set registry ${{ inputs.npm_registry_url }}

          # Extract host and path from registry URL (remove protocol)
          REGISTRY_HOST_PATH=$(echo "${{ inputs.npm_registry_url }}" | sed 's|https\?://||' | sed 's|/$||')

          # Create .npmrc for authentication
          cat > .npmrc << EOF
          registry=${{ inputs.npm_registry_url }}
          //${REGISTRY_HOST_PATH}:_auth=$(echo -n "\${OSP_PACKAGE_USERNAME}:\${OSP_PACKAGE_PASSWORD}" | base64 | tr -d '\n')
          //${REGISTRY_HOST_PATH}:always-auth=true
          EOF

      - name: Update package.json version
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔄 Updating package.json version to ${{ needs.validate-tag.outputs.package-version }}..."
          pnpm version ${{ needs.validate-tag.outputs.package-version }} --no-git-tag-version

      - name: Install dependencies
        run: |
          cd ${{ inputs.frontend_path }}
          echo "📦 Installing dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            pnpm install --frozen-lockfile
          else
            pnpm install
          fi

      - name: Run type check
        if: inputs.enable_type_check
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔍 Running type check..."
          pnpm run type-check

      - name: Run linting
        if: inputs.enable_lint
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🧹 Running linting..."
          pnpm run lint

      - name: Run tests
        if: inputs.enable_tests
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🧪 Running tests..."
          pnpm run test

      - name: Build package
        id: package
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔨 Building package..."
          pnpm run build

          # Extract package name from package.json
          if [ -f "package.json" ]; then
            PACKAGE_NAME=$(node -p "require('./package.json').name")
            echo "📦 Package: $PACKAGE_NAME"
          else
            PACKAGE_NAME="unknown"
          fi

          # Check if build was successful
          if [ -d "dist" ]; then
            echo "✅ Build successful - dist directory created"
            package_count=1
          else
            echo "❌ Build failed - no dist directory found"
            exit 1
          fi

          echo "package-name=$PACKAGE_NAME" >> $GITHUB_OUTPUT
          echo "package-count=$package_count" >> $GITHUB_OUTPUT
          echo "packages-path=./dist" >> $GITHUB_OUTPUT

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: npm-package-${{ needs.validate-tag.outputs.package-version }}
          path: ${{ inputs.frontend_path }}/dist
          retention-days: 30

  publish-packages:
    name: 'Publish NPM Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm_version }}
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node_version }}

      - name: Import NPM credentials from Vault
        uses: ospgroupvn/k8s-deployment/.github/actions/add-npm-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Configure NPM registry
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔧 Configuring NPM registry for publishing..."
          pnpm config set registry ${{ inputs.npm_registry_url }}

          # Extract host and path from registry URL (remove protocol)
          REGISTRY_HOST_PATH=$(echo "${{ inputs.npm_registry_url }}" | sed 's|https\?://||' | sed 's|/$||')

          # Create .npmrc for authentication
          cat > .npmrc << EOF
          registry=${{ inputs.npm_registry_url }}
          //${REGISTRY_HOST_PATH}:_auth=$(echo -n "${OSP_PACKAGE_USERNAME}:${OSP_PACKAGE_PASSWORD}" | base64 | tr -d '\n')
          //${REGISTRY_HOST_PATH}:always-auth=true
          EOF

      - name: Update package.json version
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔄 Updating package.json version to ${{ needs.validate-tag.outputs.package-version }}..."
          pnpm version ${{ needs.validate-tag.outputs.package-version }} --no-git-tag-version

      - name: Install dependencies and build
        run: |
          cd ${{ inputs.frontend_path }}
          echo "📦 Installing dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            pnpm install --frozen-lockfile
          else
            pnpm install
          fi

          echo "🔨 Building package..."
          pnpm run build

      - name: Check if package already exists
        id: check-existing
        if: ${{ !inputs.force_publish }}
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔍 Checking if package already exists..."
          PACKAGE_NAME=$(node -p "require('./package.json').name")
          VERSION="${{ needs.validate-tag.outputs.package-version }}"

          # Try to get package info from registry
          if pnpm view "$PACKAGE_NAME@$VERSION" version 2>/dev/null; then
            echo "⚠️ Package $PACKAGE_NAME@$VERSION already exists"
            echo "skip-publish=true" >> $GITHUB_OUTPUT
          else
            echo "✅ Package $PACKAGE_NAME@$VERSION does not exist, proceeding with publish"
            echo "skip-publish=false" >> $GITHUB_OUTPUT
          fi

      - name: Publish package
        if: inputs.force_publish || steps.check-existing.outputs.skip-publish != 'true'
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🚀 Publishing NPM package..."
          if [[ "${{ inputs.force_publish }}" == "true" ]]; then
            echo "⚡ Force publish enabled - skipping existence check"
          fi
          pnpm publish --no-git-checks

  notify-success:
    name: 'Thông báo thành công'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: success() && inputs.enable_notifications
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send success notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "✅ NPM Package CD thành công - ${{ needs.build-and-test.outputs.package-name }} v${{ needs.validate-tag.outputs.package-version }}"
          message: |
            ✅ **NPM Package đã được publish thành công!**

            📦 **Package**: ${{ needs.build-and-test.outputs.package-name }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count }} package(s)
            🟢 **Node Version**: ${{ inputs.node_version }}
            📦 **PNPM Version**: ${{ inputs.pnpm_version }}
            📚 **Registry**: ${{ inputs.npm_registry_url }}
          status: 'success'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  notify-failure:
    name: 'Thông báo thất bại'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: failure() && inputs.enable_notifications
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send failure notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "❌ NPM Package CD thất bại - ${{ needs.build-and-test.outputs.package-name || 'Unknown' }} v${{ needs.validate-tag.outputs.package-version || 'N/A' }}"
          message: |
            💥 **NPM Package CD đã thất bại!**

            📦 **Package**: ${{ needs.build-and-test.outputs.package-name || 'Không xác định' }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version || 'N/A' }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count || '0' }} package(s)
            🟢 **Node Version**: ${{ inputs.node_version }}
            📦 **PNPM Version**: ${{ inputs.pnpm_version }}

            🔍 **Kiểm tra logs để biết thêm chi tiết lỗi**
          status: 'failure'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}