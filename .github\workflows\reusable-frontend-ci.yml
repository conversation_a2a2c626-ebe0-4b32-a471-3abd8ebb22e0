name: Reusable Frontend CI Build

on:
  workflow_call:
    inputs:
      frontend_path:
        description: 'Path to frontend source code'
        required: false
        type: string
        default: 'src/frontend'
      node_version:
        description: 'Node.js version'
        required: false
        type: string
        default: '20'
      pnpm_version:
        description: 'PNPM version'
        required: false
        type: string
        default: '10'
      skip_draft_pr:
        description: 'Skip draft PRs'
        required: false
        type: boolean
        default: true
      build_command:
        description: 'Build command to run'
        required: false
        type: string
        default: 'pnpm build'

permissions:
  contents: read
  pull-requests: read

jobs:
  check-conditions:
    name: Check Build Conditions
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: Check if should build
        id: check
        run: |
          set -euo pipefail
          echo "🔍 Kiểm tra điều kiện build..."

          # Skip draft PRs if enabled
          if [[ "${{ inputs.skip_draft_pr }}" == "true" && "${{ github.event.pull_request.draft }}" == "true" ]]; then
            echo "⏭️ Bỏ qua draft PR"
            echo "should-build=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Check for skip labels
          if [[ "${{ contains(github.event.pull_request.labels.*.name, 'skip-ci') }}" == "true" ]]; then
            echo "⏭️ Phát hiện label 'skip-ci'"
            echo "should-build=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Điều kiện build đã được đáp ứng"
          echo "should-build=true" >> $GITHUB_OUTPUT

  detect-changes:
    name: Detect Changes
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions]
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      frontend-changed: ${{ steps.filter.outputs.frontend }}
      ci-changed: ${{ steps.filter.outputs.ci }}
      docs-changed: ${{ steps.filter.outputs.docs }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Check file changes
        id: filter
        run: |
          set -euo pipefail
          echo "🔍 Analyzing changed files..."

          # Get list of changed files
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            CHANGED_FILES=$(git diff --name-only HEAD^ HEAD)
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # For manual triggers, assume frontend changed
            echo "frontend=true" >> $GITHUB_OUTPUT
            echo "ci=false" >> $GITHUB_OUTPUT
            echo "docs=false" >> $GITHUB_OUTPUT
            echo "✅ Manual trigger - assuming frontend changes"
            exit 0
          else
            CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})
          fi

          echo "Changed files:"
          echo "$CHANGED_FILES"
          echo ""

          # Check for frontend changes
          if echo "$CHANGED_FILES" | grep -qE '^(${{ inputs.frontend_path }}/|package\.json|pnpm-lock\.yaml|tsconfig(\..*)?.json|turbo\.json|\.npmrc|\.eslintrc(\..*)?\.|next\.config\.js|next-env\.d\.ts|public/|styles/)'; then
            echo "frontend=true" >> $GITHUB_OUTPUT
            echo "✅ Frontend changes detected"
          else
            echo "frontend=false" >> $GITHUB_OUTPUT
            echo "⏭️ No frontend changes"
          fi

          # Check for CI changes
          if echo "$CHANGED_FILES" | grep -qE '^\\.github/workflows/'; then
            echo "ci=true" >> $GITHUB_OUTPUT
            echo "✅ CI changes detected"
          else
            echo "ci=false" >> $GITHUB_OUTPUT
            echo "⏭️ No CI changes"
          fi

          # Check for docs changes
          if echo "$CHANGED_FILES" | grep -qE '^docs/'; then
            echo "docs=true" >> $GITHUB_OUTPUT
            echo "✅ Docs changes detected"
          else
            echo "docs=false" >> $GITHUB_OUTPUT
            echo "⏭️ No docs changes"
          fi

  build-frontend:
    name: Build Frontend
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.frontend-changed == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install PNPM
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm_version }}
          run_install: false

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node_version }}

      - name: Install dependencies
        run: |
          cd ${{ inputs.frontend_path }}
          echo "📦 Installing Frontend dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            echo "🔍 Found pnpm-lock.yaml, trying --frozen-lockfile..."
            if pnpm install --frozen-lockfile; then
              echo "✅ Dependencies installed successfully with pnpm-lock.yaml!"
            else
              echo "⚠️ Frozen lockfile failed, falling back to normal install..."
              pnpm install
              echo "✅ Dependencies installed successfully with fallback!"
            fi
          else
            echo "⚠️ No pnpm-lock.yaml found, installing normally..."
            pnpm install
            echo "✅ Dependencies installed successfully!"
          fi

      - name: Build frontend
        run: |
          cd ${{ inputs.frontend_path }}
          echo "🔨 Building Frontend..."
          ${{ inputs.build_command }}
          echo "✅ Frontend build completed successfully!"

  build-status:
    name: Build Status Summary
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-frontend]
    if: always() && needs.check-conditions.outputs.should-build == 'true'
    steps:
      - name: Smart Build Summary
        run: |
          echo "📊 Path-Based Selective Build Results:"
          echo ""
          echo "🔍 File Changes Detection:"
          echo "  Frontend: ${{ needs.detect-changes.outputs['frontend-changed'] }}"
          echo "  CI: ${{ needs.detect-changes.outputs['ci-changed'] }}"
          echo "  Docs: ${{ needs.detect-changes.outputs['docs-changed'] }}"
          echo ""

          # Frontend status
          FRONTEND_SHOULD_BUILD="false"
          if [[ "${{ needs.detect-changes.outputs['frontend-changed'] }}" == "true" ]]; then
            FRONTEND_SHOULD_BUILD="true"
            echo "🔨 Frontend Build: ${{ needs.build-frontend.result }}"
          else
            echo "⏭️ Frontend Build: Skipped (no frontend changes detected)"
          fi

          echo ""

          # Check overall success
          OVERALL_SUCCESS="true"

          if [[ "$FRONTEND_SHOULD_BUILD" == "true" && "${{ needs.build-frontend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Frontend build was required but failed!"
          fi

          if [[ "$OVERALL_SUCCESS" == "true" ]]; then
            echo "🎉 All required builds completed successfully!"
            echo "💡 Path-based selective building optimized CI time and resources!"
          else
            echo "💥 One or more required builds failed!"
            exit 1
          fi

  no-changes-notification:
    name: No Changes Notification
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.frontend-changed == 'false' &&
      needs.detect-changes.outputs.ci-changed == 'false' &&
      needs.detect-changes.outputs.docs-changed == 'false'
    steps:
      - name: Notify No Relevant Changes
        run: |
          echo "🔍 Path-Based Analysis: No Relevant Changes Detected"
          echo ""
          echo "📁 Changed paths analysis showed no files in:"
          echo "  ❌ ${{ inputs.frontend_path }}/**"
          echo "  ❌ .github/workflows/**"
          echo "  ❌ docs/**"
          echo ""
          echo "⏭️ Frontend build skipped"
          echo "💡 This optimization saved significant CI time and resources!"
          echo "✅ Workflow completed successfully (selective skip)"