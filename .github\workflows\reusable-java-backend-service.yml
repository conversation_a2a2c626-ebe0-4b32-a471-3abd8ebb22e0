name: 'Reusable Java Backend Service CI/CD'

on:
  workflow_call:
    inputs:
      service-name:
        description: 'Tên service (vd: osp-master-data-service)'
        required: true
        type: string
      src-directory:
        description: 'Thư mục chứa source code Java'
        required: false
        type: string
        default: 'src/backend'
      java-version:
        description: 'Phiên bản Java'
        required: false
        type: string
        default: '21'
      maven-version:
        description: 'Phiên bản Maven'
        required: false
        type: string
        default: '3.9.4'
      build-profile:
        description: 'Maven profile để build'
        required: false
        type: string
        default: 'default'
      enable-tests:
        description: 'Có chạy unit test hay không'
        required: false
        type: boolean
        default: true
      enable-docker-build:
        description: 'Có build Docker image hay không'
        required: false
        type: boolean
        default: true
      docker-registry:
        description: 'Docker registry URL'
        required: false
        type: string
        default: 'dockerhub.ospgroup.vn'
      docker-namespace:
        description: 'Docker namespace/owner'
        required: false
        type: string
        default: 'osp-public'
      enable-notifications:
        description: '<PERSON><PERSON> gửi thông báo <PERSON>rk hay không'
        required: false
        type: boolean
        default: true
      skip-draft-pr:
        description: 'Bỏ qua draft PRs'
        required: false
        type: boolean
        default: true
      force-build:
        description: 'Buộc build mà không kiểm tra thay đổi'
        required: false
        type: boolean
        default: false

    secrets:
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL'
        required: false
      VAULT_TOKEN:
        description: 'Vault token for secrets'
        required: true

permissions:
  contents: read
  packages: write
  pull-requests: read

jobs:
  check-conditions:
    name: 'Kiểm tra điều kiện build'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: Kiểm tra điều kiện
        id: check
        run: |
          echo "🔍 Kiểm tra điều kiện build..."
          
          # Skip draft PRs if enabled
          if [[ "${{ inputs.skip-draft-pr }}" == "true" && "${{ github.event.pull_request.draft }}" == "true" ]]; then
            echo "⏭️ Bỏ qua draft PR"
            echo "should-build=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # Check for skip labels
          if [[ "${{ contains(github.event.pull_request.labels.*.name, 'skip-ci') }}" == "true" ]]; then
            echo "⏭️ Phát hiện label 'skip-ci'"
            echo "should-build=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          echo "✅ Điều kiện build đã được đáp ứng"
          echo "should-build=true" >> $GITHUB_OUTPUT

  detect-changes:
    name: 'Phát hiện thay đổi'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions]
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      backend-changed: ${{ steps.filter.outputs.backend }}
      ci-changed: ${{ steps.filter.outputs.ci }}
      docker-changed: ${{ steps.filter.outputs.docker }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Kiểm tra file thay đổi
        id: filter
        run: |
          echo "🔍 Phân tích các file đã thay đổi..."
          
          # Get list of changed files
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            CHANGED_FILES=$(git diff --name-only HEAD^ HEAD)
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "backend=true" >> $GITHUB_OUTPUT
            echo "ci=false" >> $GITHUB_OUTPUT
            echo "docker=false" >> $GITHUB_OUTPUT
            echo "✅ Manual trigger - assuming backend changes"
            exit 0
          else
            CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})
          fi
          
          echo "Changed files:"
          echo "$CHANGED_FILES"
          echo ""
          
          # Check for backend changes (Java files)
          if echo "$CHANGED_FILES" | grep -qE '\.(java|xml|properties|yml|yaml)$|^${{ inputs.src-directory }}/|pom\.xml'; then
            echo "backend=true" >> $GITHUB_OUTPUT
            echo "✅ Backend changes detected"
          else
            echo "backend=false" >> $GITHUB_OUTPUT
            echo "⏭️ No backend changes"
          fi
          
          # Check for CI changes
          if echo "$CHANGED_FILES" | grep -qE '^\\.github/workflows/'; then
            echo "ci=true" >> $GITHUB_OUTPUT
            echo "✅ CI changes detected"
          else
            echo "ci=false" >> $GITHUB_OUTPUT
            echo "⏭️ No CI changes"
          fi
          
          # Check for Docker changes
          if echo "$CHANGED_FILES" | grep -qE '^Dockerfile|^docker-compose|^\\.dockerignore'; then
            echo "docker=true" >> $GITHUB_OUTPUT
            echo "✅ Docker changes detected"
          else
            echo "docker=false" >> $GITHUB_OUTPUT
            echo "⏭️ No Docker changes"
          fi

  build-and-test:
    name: 'Build và Test Java Service'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      (needs.detect-changes.outputs.backend-changed == 'true' || inputs.force-build == true)
    outputs:
      build-success: ${{ steps.build.outputs.success }}
      test-success: ${{ steps.test.outputs.success }}
      jar-file: ${{ steps.build.outputs.jar-file }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java-version }}
          distribution: 'temurin'

      - name: Setup Maven
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java-version }}
          distribution: 'temurin'
          cache: maven

      - name: Verify Maven installation
        run: |
          echo "☕ Java version:"
          java -version
          echo ""
          echo "📦 Maven version:"
          mvn -version

      - name: Build with Maven
        id: build
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔨 Building Java project with Maven..."
          
          # Clean and compile
          if mvn clean compile -B -q; then
            echo "✅ Compilation successful"
          else
            echo "❌ Compilation failed"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          # Package
          if mvn package -DskipTests -B -q; then
            echo "✅ Packaging successful"
            
            # Find the generated JAR file
            JAR_FILE=$(find target -name "*.jar" -not -name "*-sources.jar" -not -name "*-javadoc.jar" | head -1)
            if [ -n "$JAR_FILE" ]; then
              echo "📦 Generated JAR: $JAR_FILE"
              echo "jar-file=$JAR_FILE" >> $GITHUB_OUTPUT
            fi
            
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Packaging failed"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

      - name: Run tests
        id: test
        if: inputs.enable-tests == true
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🧪 Running Java tests..."
          
          if mvn test -B; then
            echo "✅ Tests passed"
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Tests failed"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

      - name: Generate test report
        if: inputs.enable-tests == true && (success() || failure())
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "📊 Generating test report..."
          if [ -d "target/surefire-reports" ]; then
            echo "Test results found:"
            ls -la target/surefire-reports/
          else
            echo "No test results found"
          fi

  docker-build:
    name: 'Build Docker Image'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-and-test]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      inputs.enable-docker-build == true &&
      needs.build-and-test.outputs.build-success == 'true' &&
      (needs.detect-changes.outputs.backend-changed == 'true' || 
       needs.detect-changes.outputs.docker-changed == 'true' || 
       inputs.force-build == true)
    outputs:
      image-tag: ${{ steps.docker.outputs.image-tag }}
      image-version: ${{ steps.docker.outputs.image-version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Generate version
        id: version
        run: |
          DATE_TIME=$(date +%Y%m%d-%H%M%S)
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")
          VERSION_NUMBER=${LATEST_TAG#v}
          
          IFS='.' read -r -a VERSION_PARTS <<< "$VERSION_NUMBER"
          MAJOR=${VERSION_PARTS[0]:-1}
          MINOR=${VERSION_PARTS[1]:-0}
          PATCH=${VERSION_PARTS[2]:-0}
          
          PATCH=$((PATCH + 1))
          NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}"
          
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "Generated version: $NEW_VERSION"

      - name: Import Docker credentials
        uses: ospgroupvn/k8s-deployment/.github/actions/add-docker-admin@main

      - name: Build Docker image
        id: docker
        run: |
          IMAGE_NAME="${{ env.OSP_REGISTRY }}/${{ inputs.docker-namespace }}/${{ inputs.service-name }}"
          IMAGE_TAG="${IMAGE_NAME}:${{ steps.version.outputs.version }}"
          LATEST_TAG="${IMAGE_NAME}:latest"

          echo "🐳 Building Docker image..."
          echo "Image: $IMAGE_TAG"

          if [ -f "Dockerfile" ]; then
            docker build -t "$IMAGE_TAG" -t "$LATEST_TAG" .
            echo "✅ Docker image built successfully"
            echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
            echo "image-version=${{ steps.version.outputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "⚠️ No Dockerfile found, skipping Docker build"
          fi

      - name: Push Docker image
        if: steps.docker.outputs.image-tag != '' && github.event_name != 'pull_request'
        run: |
          if [[ -n "${{ env.OSP_REGISTRY_USERNAME }}" && -n "${{ env.OSP_REGISTRY_PASSWORD }}" ]]; then
            echo "🚀 Pushing Docker image..."
            echo '${{ env.OSP_REGISTRY_PASSWORD }}' | docker login ${{ env.OSP_REGISTRY }} --username "${{ env.OSP_REGISTRY_USERNAME }}" --password-stdin

            docker push ${{ steps.docker.outputs.image-tag }}
            docker push "${{ env.OSP_REGISTRY }}/${{ inputs.docker-namespace }}/${{ inputs.service-name }}:latest"

            echo "✅ Docker image pushed successfully"
          else
            echo "⚠️ No registry credentials found, skipping push"
          fi

  notify-success:
    name: 'Thông báo thành công'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-and-test, docker-build]
    if: |
      always() && 
      needs.check-conditions.outputs.should-build == 'true' &&
      inputs.enable-notifications == true &&
      (needs.build-and-test.result == 'success' || needs.build-and-test.result == 'skipped') &&
      (needs.docker-build.result == 'success' || needs.docker-build.result == 'skipped')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send success notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "✅ Java Backend Service CI/CD thành công - ${{ inputs.service-name }}"
          message: |
            ✅ **Java Backend Service đã được build thành công!**
            
            🔧 **Service**: ${{ inputs.service-name }}
            ☕ **Java Version**: ${{ inputs.java-version }}
            📦 **Maven Profile**: ${{ inputs.build-profile }}
            🧪 **Tests**: ${{ inputs.enable-tests && 'Enabled' || 'Disabled' }}
            🐳 **Docker**: ${{ inputs.enable-docker-build && 'Enabled' || 'Disabled' }}
            ${{ needs.docker-build.outputs.image-tag && format('📦 **Image**: {0}', needs.docker-build.outputs.image-tag) || '' }}
            ${{ needs.build-and-test.outputs.jar-file && format('📄 **JAR**: {0}', needs.build-and-test.outputs.jar-file) || '' }}
          status: 'success'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Manual trigger' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  notify-failure:
    name: 'Thông báo thất bại'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-and-test, docker-build]
    if: |
      always() && 
      needs.check-conditions.outputs.should-build == 'true' &&
      inputs.enable-notifications == true &&
      (needs.build-and-test.result == 'failure' || needs.docker-build.result == 'failure')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send failure notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "❌ Java Backend Service CI/CD thất bại - ${{ inputs.service-name }}"
          message: |
            💥 **Java Backend Service CI/CD đã thất bại!**
            
            🔧 **Service**: ${{ inputs.service-name }}
            ☕ **Java Version**: ${{ inputs.java-version }}
            📦 **Maven Profile**: ${{ inputs.build-profile }}
            
            🔍 **Kiểm tra logs để biết thêm chi tiết lỗi**
          status: 'failure'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Manual trigger' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  no-changes-notification:
    name: 'Thông báo không có thay đổi'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.backend-changed == 'false' &&
      needs.detect-changes.outputs.ci-changed == 'false' &&
      needs.detect-changes.outputs.docker-changed == 'false' &&
      inputs.force-build == false
    steps:
      - name: Thông báo không có thay đổi
        run: |
          echo "🔍 Phân tích thay đổi: Không có thay đổi liên quan"
          echo ""
          echo "📁 Không có thay đổi trong:"
          echo "  ❌ Java source code (*.java, *.xml, *.properties, pom.xml)"
          echo "  ❌ CI/CD workflows"
          echo "  ❌ Docker files"
          echo ""
          echo "⏭️ Build được bỏ qua"
          echo "💡 Tối ưu hóa này giúp tiết kiệm thời gian và tài nguyên CI!"
          echo "✅ Workflow hoàn thành thành công (selective skip)"
