name: "📢 Reusable Lark Notification"

on:
  workflow_call:
    inputs:
      title:
        description: 'Notification title'
        required: true
        type: string
      message:
        description: 'Notification message'
        required: true
        type: string
      status:
        description: 'Build/Deploy status (success, failure, started, info)'
        required: true
        type: string
      repository:
        description: 'Repository name'
        required: false
        type: string
        default: ''
      branch:
        description: 'Branch name'
        required: false
        type: string
        default: ''
      commit-sha:
        description: 'Commit SHA'
        required: false
        type: string
        default: ''
      commit-message:
        description: 'Commit message'
        required: false
        type: string
        default: ''
      commit-author:
        description: 'Commit author'
        required: false
        type: string
        default: ''
      build-duration:
        description: 'Build duration'
        required: false
        type: string
        default: ''
      workflow-url:
        description: 'GitHub workflow URL'
        required: false
        type: string
        default: ''
      custom-fields:
        description: 'Additional custom fields (JSON format)'
        required: false
        type: string
        default: '{}'

    secrets:
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL for notifications'
        required: true

jobs:
  send-notification:
    name: 📢 Send Lark Notification
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    timeout-minutes: 5

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: 📢 Send Lark Notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: ${{ inputs.title }}
          message: ${{ inputs.message }}
          status: ${{ inputs.status }}
          repository: ${{ inputs.repository || github.repository }}
          branch: ${{ inputs.branch || github.ref_name }}
          commit-sha: ${{ inputs.commit-sha || github.sha }}
          commit-message: ${{ inputs.commit-message }}
          commit-author: ${{ inputs.commit-author }}
          build-duration: ${{ inputs.build-duration }}
          workflow-url: ${{ inputs.workflow-url || format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}