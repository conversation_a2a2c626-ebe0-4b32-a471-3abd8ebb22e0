name: 'Common .NET Library CI Build'

env:
  # Global environment variables for consistent NuGet paths (using absolute paths)
  NUGET_PACKAGES: /home/<USER>/.nuget/packages
  NUGET_HTTP_CACHE_PATH: /home/<USER>/.local/share/NuGet/http-cache
  NUGET_PLUGINS_CACHE_PATH: /home/<USER>/.local/share/NuGet/plugins-cache
  DOTNET_CLI_TELEMETRY_OPTOUT: 1
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  DOTNET_NOLOGO: 1

on:
  workflow_call:
    inputs:
      src-directory:
        description: 'Thư mục chứa source code (mặc định: src)'
        required: false
        type: string
        default: 'src'
      dotnet-version:
        description: 'Phiên bản .NET SDK (mặc định: 8.0.x)'
        required: false
        type: string
        default: '8.0.x'
      build-configuration:
        description: 'Cấu hình build (mặc định: Release)'
        required: false
        type: string
        default: 'Release'
      lark-webhook-url:
        description: 'URL webhook Lark để gửi thông báo'
        required: false
        type: string
        default: ''
      enable-tests:
        description: 'Có chạy unit test hay không (mặc định: true)'
        required: false
        type: boolean
        default: true
      additional-build-args:
        description: 'Tham số bổ sung cho lệnh dotnet build'
        required: false
        type: string
        default: ''
      force_build:
        description: 'Buộc build mà không kiểm tra thay đổi'
        required: false
        type: boolean
        default: false
      branch:
        description: 'Nhánh cần build (mặc định: nhánh hiện tại)'
        required: false
        type: string
        default: ''

permissions:
  contents: read
  pull-requests: read

jobs:
  detect-changes:
    name: 'Kiểm tra thay đổi code'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      has-changes: ${{ steps.changes.outputs.src }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          ref: ${{ inputs.branch || github.sha }}

      - name: Detect changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - '${{ inputs.src-directory }}/**'
              - '*.sln'
              - '**/*.csproj'
              - '**/*.cs'

  build:
    name: 'Build .NET Library'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: detect-changes
    if: needs.detect-changes.outputs.has-changes == 'true' || inputs.force_build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch || github.sha }}

      - name: Fix NuGet permissions
        run: |
          echo "🔧 Fixing NuGet permissions for CI environment..."

          # Clear any existing problematic cache
          echo "Clearing existing NuGet caches..."
          rm -rf ~/.nuget/packages/* || true
          rm -rf ~/.local/share/NuGet/http-cache/* || true
          rm -rf ~/.local/share/NuGet/v3-cache/* || true
          rm -rf ~/.local/share/NuGet/plugins-cache/* || true

          # Tạo các thư mục NuGet cần thiết
          mkdir -p ~/.nuget/packages
          mkdir -p ~/.local/share/NuGet/http-cache
          mkdir -p ~/.local/share/NuGet/v3-cache
          mkdir -p ~/.local/share/NuGet/plugins-cache
          mkdir -p ~/.dotnet/NuGetFallbackFolder
          mkdir -p ~/.templateengine

          # Set permissions cho user hiện tại
          chmod -R 755 ~/.nuget || true
          chmod -R 755 ~/.local/share/NuGet || true
          chmod -R 755 ~/.dotnet || true
          chmod -R 755 ~/.templateengine || true

          # Nếu có script fix-permissions, chạy nó với sudo
          if [ -f "/fix-permissions.sh" ]; then
            echo "Running system-wide permission fix..."
            sudo /fix-permissions.sh || echo "System permission fix failed, continuing..."
          fi

          # Set environment variables
          echo "NUGET_PACKAGES=$HOME/.nuget/packages" >> $GITHUB_ENV
          echo "NUGET_HTTP_CACHE_PATH=$HOME/.local/share/NuGet/http-cache" >> $GITHUB_ENV
          echo "NUGET_PLUGINS_CACHE_PATH=$HOME/.local/share/NuGet/plugins-cache" >> $GITHUB_ENV

          # Verify permissions and show current user
          echo "✅ NuGet directories created and permissions set"
          echo "Current user: $(whoami)"
          echo "Home directory: $HOME"
          ls -la ~/.nuget/ || echo "~/.nuget not accessible"
          ls -la ~/.local/share/NuGet/ || echo "~/.local/share/NuGet not accessible"

      - name: Restore dependencies
        working-directory: ./
        run: dotnet restore
        env:
          NUGET_PACKAGES: /home/<USER>/.nuget/packages
          NUGET_HTTP_CACHE_PATH: /home/<USER>/.local/share/NuGet/http-cache
          NUGET_PLUGINS_CACHE_PATH: /home/<USER>/.local/share/NuGet/plugins-cache

      - name: Discover .csproj files
        id: discover
        run: |
          echo "🔍 Tìm kiếm các file .csproj trong thư mục ${{ inputs.src-directory }}..."
          projects=$(find ${{ inputs.src-directory }} -name "*.csproj" -type f | tr '\n' ' ')
          if [ -z "$projects" ]; then
            echo "❌ Không tìm thấy file .csproj nào trong thư mục ${{ inputs.src-directory }}"
            exit 1
          fi
          echo "✅ Tìm thấy các project: $projects"
          echo "projects=$projects" >> $GITHUB_OUTPUT

      - name: Build all projects
        id: build
        run: |
          build_start_time=$(date +%s)
          success_count=0
          total_count=0
          failed_projects=""
          
          echo "🔨 Bắt đầu build các project..."
          
          for project in ${{ steps.discover.outputs.projects }}; do
            echo "📦 Building project: $project"
            total_count=$((total_count + 1))
            
            if NUGET_PACKAGES=/home/<USER>/.nuget/packages NUGET_HTTP_CACHE_PATH=/home/<USER>/.local/share/NuGet/http-cache NUGET_PLUGINS_CACHE_PATH=/home/<USER>/.local/share/NuGet/plugins-cache dotnet build "$project" --configuration ${{ inputs.build-configuration }} --no-restore ${{ inputs.additional-build-args }}; then
              echo "✅ Build thành công: $project"
              success_count=$((success_count + 1))
            else
              echo "❌ Build thất bại: $project"
              failed_projects="$failed_projects $project"
            fi
          done
          
          build_end_time=$(date +%s)
          build_duration=$((build_end_time - build_start_time))
          
          echo "📊 Kết quả build:"
          echo "   - Tổng số project: $total_count"
          echo "   - Build thành công: $success_count"
          echo "   - Build thất bại: $((total_count - success_count))"
          echo "   - Thời gian build: ${build_duration}s"
          
          echo "success_count=$success_count" >> $GITHUB_OUTPUT
          echo "total_count=$total_count" >> $GITHUB_OUTPUT
          echo "failed_projects=$failed_projects" >> $GITHUB_OUTPUT
          echo "build_duration=${build_duration}s" >> $GITHUB_OUTPUT
          
          if [ $success_count -ne $total_count ]; then
            echo "❌ Một số project build thất bại: $failed_projects"
            exit 1
          fi
          
          echo "✅ Tất cả project đã build thành công!"

      - name: Run tests
        if: inputs.enable-tests == true
        run: |
          echo "🧪 Chạy unit tests..."
          test_projects=$(find ${{ inputs.src-directory }} -name "*Test*.csproj" -o -name "*.Test.csproj" -o -name "*.Tests.csproj" | tr '\n' ' ')
          
          if [ -n "$test_projects" ]; then
            echo "✅ Tìm thấy các test project: $test_projects"
            for test_project in $test_projects; do
              echo "🧪 Running tests for: $test_project"
              NUGET_PACKAGES=/home/<USER>/.nuget/packages NUGET_HTTP_CACHE_PATH=/home/<USER>/.local/share/NuGet/http-cache NUGET_PLUGINS_CACHE_PATH=/home/<USER>/.local/share/NuGet/plugins-cache dotnet test "$test_project" --configuration ${{ inputs.build-configuration }} --no-build --verbosity normal
            done
          else
            echo "ℹ️ Không tìm thấy test project nào"
          fi
  no-changes:
    name: 'Không có thay đổi code'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: detect-changes
    if: needs.detect-changes.outputs.has-changes == 'false' && !inputs.force_build
    
    steps:
      - name: Skip build
        run: |
          echo "ℹ️ Không có thay đổi trong thư mục ${{ inputs.src-directory }}, bỏ qua build."
          echo "✅ Workflow hoàn thành mà không cần build."
