name: 'Reusable .NET NuGet Package CD'

env:
  # Global environment variables for consistent NuGet paths
  NUGET_PACKAGES: /home/<USER>/.nuget/packages
  NUGET_HTTP_CACHE_PATH: /home/<USER>/.local/share/NuGet/http-cache
  NUGET_PLUGINS_CACHE_PATH: /home/<USER>/.local/share/NuGet/plugins-cache
  DOTNET_CLI_TELEMETRY_OPTOUT: 1
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  DOTNET_NOLOGO: 1

on:
  workflow_call:
    inputs:
      src-directory:
        description: 'Thư mục chứa source code (mặc định: src)'
        required: false
        type: string
        default: 'src'
      dotnet-version:
        description: 'Phiên bản .NET SDK (mặc định: 8.0.x)'
        required: false
        type: string
        default: '8.0.x'
      build-configuration:
        description: 'Cấu hình build (mặc định: Release)'
        required: false
        type: string
        default: 'Release'
      package-version:
        description: 'Phiên bản package (sẽ lấy từ tag nếu không được cung cấp)'
        required: false
        type: string
        default: ''
      enable-tests:
        description: 'Có chạy unit test trước khi package hay không'
        required: false
        type: boolean
        default: true
      skip-duplicate:
        description: 'Bỏ qua nếu package version đã tồn tại'
        required: false
        type: boolean
        default: true
      additional-build-args:
        description: 'Tham số bổ sung cho lệnh dotnet build'
        required: false
        type: string
        default: ''
      force_build:
        description: 'Buộc build mà không kiểm tra thay đổi (dành cho CD)'
        required: false
        type: boolean
        default: false

permissions:
  contents: read
  packages: write

jobs:
  validate-tag:
    name: 'Kiểm tra tag và phiên bản'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      package-version: ${{ steps.version.outputs.version }}
      is-valid-tag: ${{ steps.version.outputs.is-valid }}
      tag-name: ${{ steps.version.outputs.tag-name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate tag and extract version
        id: version
        run: |
          echo "🔍 Debug info:"
          echo "  github.ref_type: '${{ github.ref_type }}'"
          echo "  github.ref_name: '${{ github.ref_name }}'"
          echo "  inputs.package-version: '${{ inputs.package-version }}'"
          echo ""

          # Lấy tag name từ ref
          if [[ "${{ github.ref_type }}" == "tag" ]]; then
            TAG_NAME="${{ github.ref_name }}"
            echo "tag-name=$TAG_NAME" >> $GITHUB_OUTPUT

            # Kiểm tra format tag (v1.0.0, 1.0.0, v1.0.0-beta, etc.)
            if [[ $TAG_NAME =~ ^v?([0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?)$ ]]; then
              VERSION="${BASH_REMATCH[1]}"
              echo "✅ Valid tag format: $TAG_NAME -> Version: $VERSION"
              echo "version=$VERSION" >> $GITHUB_OUTPUT
              echo "is-valid=true" >> $GITHUB_OUTPUT
            else
              echo "❌ Invalid tag format: $TAG_NAME"
              echo "Tag phải có format: v1.0.0, 1.0.0, hoặc v1.0.0-beta"
              echo "is-valid=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          elif [[ -n "${{ inputs.package-version }}" ]]; then
            TAG_NAME="${{ inputs.package-version }}"
            echo "tag-name=$TAG_NAME" >> $GITHUB_OUTPUT

            # Validate tag format for manual input
            if [[ $TAG_NAME =~ ^v?([0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?)$ ]]; then
              VERSION="${BASH_REMATCH[1]}"
              echo "✅ Valid manual tag format: $TAG_NAME -> Version: $VERSION"
              echo "version=$VERSION" >> $GITHUB_OUTPUT
              echo "is-valid=true" >> $GITHUB_OUTPUT
            else
              echo "❌ Invalid manual tag format: $TAG_NAME"
              echo "Tag phải có format: v1.0.0, 1.0.0, hoặc v1.0.0-beta"
              echo "is-valid=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          else
            echo "❌ Không tìm thấy tag hoặc package version"
            echo "is-valid=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Debug output - no variable dependencies
          echo ""
          echo "📋 Validate-tag job completed successfully"

  build-and-test:
    name: 'Build và Test NuGet Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: validate-tag
    if: needs.validate-tag.outputs.is-valid-tag == 'true'
    outputs:
      package-names: ${{ steps.pack.outputs.package-names }}
      package-count: ${{ steps.pack.outputs.package-count }}

    steps:
      - name: Debug build inputs
        run: |
          echo "🔧 Build job debug info:"
          echo "  needs.validate-tag.outputs.is-valid-tag: '${{ needs.validate-tag.outputs.is-valid-tag }}'"
          echo "  needs.validate-tag.outputs.package-version: '${{ needs.validate-tag.outputs.package-version }}'"
          echo "  needs.validate-tag.outputs.tag-name: '${{ needs.validate-tag.outputs.tag-name }}'"
          echo "  job condition result: ${{ needs.validate-tag.outputs.is-valid-tag == 'true' }}"
          echo "  force_build input: '${{ inputs.force_build }}'"
          echo "  inputs.package-version: '${{ inputs.package-version }}'"
          echo ""

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Fix NuGet permissions
        run: |
          echo "🔧 Setting up NuGet environment..."

          # Clear any existing problematic cache
          rm -rf ~/.nuget/packages/* || true
          rm -rf ~/.local/share/NuGet/http-cache/* || true
          rm -rf ~/.local/share/NuGet/v3-cache/* || true
          rm -rf ~/.local/share/NuGet/plugins-cache/* || true

          # Tạo các thư mục NuGet cần thiết
          mkdir -p ~/.nuget/packages
          mkdir -p ~/.local/share/NuGet/http-cache
          mkdir -p ~/.local/share/NuGet/v3-cache
          mkdir -p ~/.local/share/NuGet/plugins-cache
          mkdir -p ~/.dotnet/NuGetFallbackFolder

          # Set permissions
          chmod -R 755 ~/.nuget || true
          chmod -R 755 ~/.local/share/NuGet || true
          chmod -R 755 ~/.dotnet || true

          echo "✅ NuGet environment ready"

      - name: Restore dependencies
        run: dotnet restore
        working-directory: ${{ inputs.src-directory }}

      - name: Build solution
        run: |
          echo "🔨 Building solution with version ${{ needs.validate-tag.outputs.package-version }}..."
          dotnet build \
            --configuration ${{ inputs.build-configuration }} \
            --no-restore \
            -p:Version=${{ needs.validate-tag.outputs.package-version }} \
            -p:AssemblyVersion=${{ needs.validate-tag.outputs.package-version }} \
            -p:FileVersion=${{ needs.validate-tag.outputs.package-version }} \
            ${{ inputs.additional-build-args }}
        working-directory: ${{ inputs.src-directory }}

      - name: Run tests
        if: inputs.enable-tests == true
        run: |
          echo "🧪 Chạy unit tests..."
          test_projects=$(find ${{ inputs.src-directory }} -name "*Test*.csproj" -o -name "*.Test.csproj" -o -name "*.Tests.csproj" | tr '\n' ' ')

          if [ -n "$test_projects" ]; then
            echo "✅ Found test projects: $test_projects"
            for test_project in $test_projects; do
              echo "🧪 Running tests for: $test_project"
              dotnet test "$test_project" \
                --configuration ${{ inputs.build-configuration }} \
                --no-build \
                --verbosity normal \
                --logger trx \
                --results-directory TestResults
            done
          else
            echo "ℹ️ No test projects found"
          fi

      - name: Create NuGet packages
        id: pack
        run: |
          echo "📦 Creating NuGet packages..."

          # Tìm tất cả các project có thể đóng gói (không phải test projects)
          packable_projects=$(find ${{ inputs.src-directory }} -name "*.csproj" -not -path "*Test*" -not -path "*test*" | tr '\n' ' ')

          if [ -z "$packable_projects" ]; then
            echo "❌ Không tìm thấy project nào có thể đóng gói"
            exit 1
          fi

          echo "📦 Found packable projects: $packable_projects"

          # Tạo thư mục output
          mkdir -p ./packages

          # Đóng gói từng project
          package_count=0
          package_names=""
          for project in $packable_projects; do
            echo "📦 Packing project: $project"

            dotnet pack "$project" \
              --configuration ${{ inputs.build-configuration }} \
              --no-build \
              --output ./packages \
              -p:PackageVersion=${{ needs.validate-tag.outputs.package-version }} \
              -p:Version=${{ needs.validate-tag.outputs.package-version }}

            if [ $? -eq 0 ]; then
              package_count=$((package_count + 1))
              # Extract package name from csproj file
              package_name=$(basename "$project" .csproj)
              if [ -z "$package_names" ]; then
                package_names="$package_name"
              else
                package_names="$package_names, $package_name"
              fi
              echo "✅ Successfully packed: $project -> $package_name"
            else
              echo "❌ Failed to pack: $project"
              exit 1
            fi
          done

          # List created packages
          echo "📋 Created packages:"
          ls -la ./packages/*.nupkg

          echo "package-count=$package_count" >> $GITHUB_OUTPUT
          echo "package-names=$package_names" >> $GITHUB_OUTPUT
          echo "packages-path=./packages" >> $GITHUB_OUTPUT

      - name: Upload packages as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: nuget-packages-${{ needs.validate-tag.outputs.package-version }}
          path: ./packages/*.nupkg
          retention-days: 30

  publish-packages:
    name: 'Publish NuGet Packages'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test]

    steps:
      - name: Download packages
        uses: actions/download-artifact@v4
        with:
          name: nuget-packages-${{ needs.validate-tag.outputs.package-version }}
          path: ./packages

      - name: Verify dotnet CLI availability
        run: |
          echo "🔍 Checking dotnet CLI availability..."
          dotnet --version || echo "❌ dotnet CLI not available"
          dotnet nuget --help > /dev/null || echo "❌ dotnet nuget command not available"
          echo "✅ dotnet CLI ready for NuGet operations"
      
      - name: Import Secrets Nuget Admin
        uses: ospgroupvn/k8s-deployment/.github/actions/add-nuget-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Configure NuGet source
        run: |
          echo "🔧 Configuring NuGet source..."

          # Create local NuGet config if it doesn't exist
          if [ ! -f "nuget.config" ]; then
            echo "<?xml version=\"1.0\" encoding=\"utf-8\"?>" > nuget.config
            echo "<configuration>" >> nuget.config
            echo "  <packageSources>" >> nuget.config
            echo "    <clear />" >> nuget.config
            echo "    <add key=\"nuget.org\" value=\"https://api.nuget.org/v3/index.json\" protocolVersion=\"3\" />" >> nuget.config
            echo "  </packageSources>" >> nuget.config
            echo "  <packageSourceCredentials>" >> nuget.config
            echo "  </packageSourceCredentials>" >> nuget.config
            echo "</configuration>" >> nuget.config
          fi

          # Xóa source nếu đã tồn tại (using local config)
          dotnet nuget remove source "OSPNuGetRegistry" --configfile nuget.config 2>/dev/null || true

          # Thêm source mới (using local config)
          dotnet nuget add source "$OSP_NUGET_PACKAGE_URL" \
            --name "OSPNuGetRegistry" \
            --username "$OSP_PACKAGE_USERNAME" \
            --password "$OSP_PACKAGE_PASSWORD" \
            --store-password-in-clear-text \
            --configfile nuget.config

          echo "✅ NuGet source configured with local config"

      - name: Check for existing packages
        if: inputs.skip-duplicate == true
        id: check-existing
        run: |
          echo "🔍 Checking for existing packages..."
          echo "⚠️ Skipping duplicate check - will rely on dotnet push --skip-duplicate"
          echo "skip-publish=false" >> $GITHUB_OUTPUT

      - name: Publish packages
        if: steps.check-existing.outputs.skip-publish != 'true'
        run: |
          echo "🚀 Publishing NuGet packages..."

          package_files=(./packages/*.nupkg)
          published_count=0
          failed_count=0

          for package_file in "${package_files[@]}"; do
            if [[ -f "$package_file" ]]; then
              echo "📤 Publishing: $package_file"

              if dotnet nuget push "$package_file" \
                --source "OSPNuGetRegistry" \
                --skip-duplicate \
                --no-symbols; then
                echo "✅ Successfully published: $package_file"
                published_count=$((published_count + 1))
              else
                echo "❌ Failed to publish: $package_file"
                failed_count=$((failed_count + 1))
              fi
            fi
          done

          echo "📊 Publish results:"
          echo "  - Published: $published_count packages"
          echo "  - Failed: $failed_count packages"

          if [ $failed_count -gt 0 ]; then
            echo "❌ Some packages failed to publish"
            exit 1
          fi

          echo "✅ All packages published successfully!"

      - name: Skip publish notification
        if: steps.check-existing.outputs.skip-publish == 'true'
        run: |
          echo "⏭️ Skipping publish - packages already exist"

  notify-success:
    name: 'Thông báo thành công'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: success()

    steps:
      - name: Checkout code (for notification action)
        uses: actions/checkout@v4

      - name: Import Secrets Nuget Admin
        uses: ospgroupvn/k8s-deployment/.github/actions/add-nuget-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Send success notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: '✅ NuGet Package CD thành công - ${{ needs.build-and-test.outputs.package-names }} v${{ needs.validate-tag.outputs.package-version }}'
          message: |
            ✅ **NuGet Package đã được publish thành công!**

            📦 **Packages**: ${{ needs.build-and-test.outputs.package-names }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count }} package(s)
            🏷️ **Tag**: ${{ needs.validate-tag.outputs.tag-name }}
            🔧 **.NET Version**: ${{ inputs.dotnet-version }}
            🎯 **Configuration**: ${{ inputs.build-configuration }}
            📚 **Registry**: $OSP_NUGET_PACKAGE_URL
          status: 'success'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  notify-failure:
    name: 'Thông báo thất bại'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: failure()

    steps:
      - name: Checkout code (for notification action)
        uses: actions/checkout@v4

      - name: Send failure notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "❌ NuGet Package CD thất bại - ${{ needs.build-and-test.outputs.package-names || 'Unknown' }} v${{ needs.validate-tag.outputs.package-version || 'N/A' }}"
          message: |
            💥 **NuGet Package CD đã thất bại!**

            📦 **Packages**: ${{ needs.build-and-test.outputs.package-names || 'Không xác định' }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version || 'N/A' }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count || '0' }} package(s)
            🏷️ **Tag**: ${{ needs.validate-tag.outputs.tag-name || github.ref_name }}
            🔧 **.NET Version**: ${{ inputs.dotnet-version }}
            🎯 **Configuration**: ${{ inputs.build-configuration }}

            🔍 **Kiểm tra logs để biết thêm chi tiết lỗi**
          status: 'failure'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
