name: 'Common Java Library CI Build'

env:
  # Global environment variables for consistent Maven paths
  MAVEN_OPTS: '-Dmaven.repo.local=/home/<USER>/.m2/repository'
  MAVEN_CLI_OPTS: '--batch-mode --errors --fail-at-end --show-version'

on:
  workflow_call:
    inputs:
      src-directory:
        description: 'Thư mục chứa source code (mặc định: .)'
        required: false
        type: string
        default: '.'
      java-version:
        description: 'Phiên bản Java (mặc định: 21)'
        required: false
        type: string
        default: '21'
      java-distribution:
        description: 'Phân phối Java (mặc định: temurin)'
        required: false
        type: string
        default: 'temurin'
      build-configuration:
        description: 'Cấu hình build (mặc định: Release)'
        required: false
        type: string
        default: 'Release'
      lark-webhook-url:
        description: 'URL webhook Lark để gửi thông báo'
        required: false
        type: string
        default: ''
      enable-tests:
        description: 'Có chạy unit test hay không (mặc định: true)'
        required: false
        type: boolean
        default: true
      additional-build-args:
        description: '<PERSON><PERSON> số bổ sung cho lệnh mvn build'
        required: false
        type: string
        default: ''
      force_build:
        description: 'Buộc build mà không kiểm tra thay đổi'
        required: false
        type: boolean
        default: false
      branch:
        description: 'Nhánh cần build (mặc định: nhánh hiện tại)'
        required: false
        type: string
        default: ''
      build_reason:
        description: 'Lý do trigger build thủ công'
        required: false
        type: string
        default: 'Manual trigger'

permissions:
  contents: read
  pull-requests: read

jobs:
  # Kiểm tra điều kiện để bỏ qua draft PR
  check-conditions:
    name: Kiểm tra Điều kiện Build
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: Kiểm tra có nên build không
        id: check
        run: |
          set -euo pipefail
          echo "🔍 Kiểm tra điều kiện build..."
          echo "Event: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Base Ref: ${{ github.base_ref }}"

          # CI build chạy trên PR events và manual workflow_dispatch
          # Điều này tránh duplicate builds khi merge PR vào main/develop

          # Xử lý manual trigger trước
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "🚀 Manual workflow trigger detected"
            echo "Build reason: ${{ inputs.build_reason }}"
            echo "Force build: ${{ inputs.force_build }}"
            echo "Enable tests: ${{ inputs.enable-tests }}"
            echo "should-build=true" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Xử lý PR events
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "🔀 Đã phát hiện Pull Request event"

            # Kiểm tra sự tồn tại của PR object
            if [[ -z "${{ github.event.pull_request.number }}" ]]; then
              echo "❌ Event type là 'pull_request' nhưng không tìm thấy dữ liệu PR."
              echo "Điều này có thể do action không được hỗ trợ (ví dụ: 'closed')."
              echo "Bỏ qua build để tránh lỗi."
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi

            echo "Trạng thái Draft PR: ${{ github.event.pull_request.draft }}"
            echo "Branch đích: ${{ github.event.pull_request.base.ref }}"

            # Kiểm tra draft status trước
            if [[ "${{ github.event.pull_request.draft }}" == "true" ]]; then
              echo "📝 Phát hiện Draft PR - bỏ qua build"
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi

            # Kiểm tra target branch
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            echo "🎯 Phân tích branch đích: $TARGET_BRANCH"

            if [[ "$TARGET_BRANCH" == "main" || "$TARGET_BRANCH" == "develop" ]]; then
              echo "✅ PR hướng tới branch được bảo vệ ($TARGET_BRANCH) - bắt buộc chạy build"
              echo "should-build=true" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "🔍 PR hướng tới branch khác ($TARGET_BRANCH) - kiểm tra label 'ci-build'"

              # Kiểm tra label ci-build
              LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
              echo "Labels: $LABELS"

              if echo "$LABELS" | grep -q "ci-build"; then
                echo "🏷️ Label 'ci-build' được tìm thấy - sẽ chạy build"
                echo "should-build=true" >> $GITHUB_OUTPUT
              else
                echo "⏭️ Không có label 'ci-build' - bỏ qua build"
                echo "should-build=false" >> $GITHUB_OUTPUT
              fi
              exit 0
            fi
          fi

          # Chỉ chạy trên PR events và workflow_dispatch, các event khác bỏ qua
          echo "⚠️ Event type không được hỗ trợ: ${{ github.event_name }}"
          echo "⏭️ CI Build chỉ được kích hoạt cho:"
          echo "  - pull_request events: [opened, synchronize, reopened, ready_for_review, labeled, unlabeled]"
          echo "  - workflow_dispatch: Manual trigger"
          echo "should-build=false" >> $GITHUB_OUTPUT

  skip-build-notification:
    name: Thông báo Bỏ qua Build
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'false'

    steps:
      - name: Thông báo Bỏ qua Build
        run: |
          echo "⏭️ CI Build được bỏ qua"
          echo ""
          echo "📋 Điều kiện build:"
          echo "✅ PR vào main/develop: Luôn chạy build"
          echo "🏷️ PR vào branch khác: Cần label 'ci-build'"
          echo "📝 Draft PR: Luôn bỏ qua"
          echo "🚀 Manual trigger: Luôn chạy build"
          echo ""
          echo "💡 Gợi ý:"
          echo "- Thêm label 'ci-build' vào PR để kích hoạt build"
          echo "- Chuyển PR sang 'Ready for Review' nếu đang ở draft"
          echo "- Sử dụng 'Run workflow' để trigger build thủ công"
          echo "✅ Workflow hoàn thành thành công (đã bỏ qua)"

  detect-changes:
    name: 'Kiểm tra thay đổi code'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      has-changes: ${{ steps.set-outputs.outputs.has-changes }}
      ci-changed: ${{ steps.set-outputs.outputs.ci-changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          ref: ${{ inputs.branch || github.sha }}

      - name: Detect changes
        uses: dorny/paths-filter@v3
        id: changes
        # Chỉ chạy path detection cho PR events, manual trigger sẽ force build
        if: github.event_name == 'pull_request'
        with:
          filters: |
            backend:
              - '${{ inputs.src-directory }}/**'
              - '**/*.java'
              - '**/pom.xml'
              - '**/mvnw'
              - '**/mvnw.cmd'
              - '**/application.yml'
              - '**/application.properties'
            ci:
              - '.github/workflows/**'

      - name: Set final outputs
        id: set-outputs
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "🚀 Manual trigger - forcing backend=true, ci=false"
            echo "has-changes=true" >> $GITHUB_OUTPUT
            echo "ci-changed=false" >> $GITHUB_OUTPUT
          else
            # Sử dụng conditional để tránh lỗi khi step không chạy
            if [[ "${{ steps.changes.outcome }}" == "success" ]]; then
              BACKEND_CHANGED="${{ steps.changes.outputs.backend }}"
              CI_CHANGED="${{ steps.changes.outputs.ci }}"
              echo "Backend changes detected: $BACKEND_CHANGED"
              echo "CI changes detected: $CI_CHANGED"
              echo "has-changes=$BACKEND_CHANGED" >> $GITHUB_OUTPUT
              echo "ci-changed=$CI_CHANGED" >> $GITHUB_OUTPUT
            else
              echo "Changes detection was skipped, defaulting to false"
              echo "has-changes=false" >> $GITHUB_OUTPUT
              echo "ci-changed=false" >> $GITHUB_OUTPUT
            fi
          fi

  build:
    name: 'Build Java Library'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      (needs.detect-changes.outputs.has-changes == 'true' || github.event_name == 'workflow_dispatch' || inputs.force_build)
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch || github.sha }}

      - name: Set up Java ${{ inputs.java-version }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java-version }}
          distribution: ${{ inputs.java-distribution }}
          cache: 'maven'

      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Verify Java and Maven versions
        run: |
          echo "=== Environment Information ==="
          echo "Java version:"
          java -version
          echo "Maven version:"
          mvn -version
          echo "Working directory: $(pwd)"
          echo "================================"

      - name: Discover Maven projects
        id: discover
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔍 Tìm kiếm các file pom.xml trong thư mục ${{ inputs.src-directory }}..."
          projects=$(find . -name "pom.xml" -type f | head -10 | tr '\n' ' ')
          if [ -z "$projects" ]; then
            echo "❌ Không tìm thấy file pom.xml nào trong thư mục ${{ inputs.src-directory }}"
            exit 1
          fi
          echo "✅ Tìm thấy các project: $projects"
          echo "projects=$projects" >> $GITHUB_OUTPUT

      - name: Compile all projects
        id: compile
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔨 Bắt đầu compile các Maven project..."
          compile_start_time=$(date +%s)
          
          mvn $MAVEN_CLI_OPTS clean compile ${{ inputs.additional-build-args }}
          
          compile_end_time=$(date +%s)
          compile_duration=$((compile_end_time - compile_start_time))
          echo "✅ Compile hoàn thành trong ${compile_duration}s"
          echo "compile-duration=${compile_duration}" >> $GITHUB_OUTPUT

      - name: Run tests
        if: inputs.enable-tests == true
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🧪 Chạy unit tests..."
          test_start_time=$(date +%s)
          
          mvn $MAVEN_CLI_OPTS test ${{ inputs.additional-build-args }}
          
          test_end_time=$(date +%s)
          test_duration=$((test_end_time - test_start_time))
          echo "✅ Tests hoàn thành trong ${test_duration}s"

      - name: Package projects
        id: package
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "📦 Packaging Maven projects..."
          package_start_time=$(date +%s)
          
          mvn $MAVEN_CLI_OPTS package -DskipTests ${{ inputs.additional-build-args }}
          
          # Count created packages
          package_count=$(find . -name "*.jar" -not -path "*/target/test-classes/*" | wc -l)
          echo "📋 Created $package_count package(s)"
          
          package_end_time=$(date +%s)
          package_duration=$((package_end_time - package_start_time))
          echo "✅ Packaging hoàn thành trong ${package_duration}s"
          
          echo "package-count=$package_count" >> $GITHUB_OUTPUT
          echo "package-duration=${package_duration}" >> $GITHUB_OUTPUT

      - name: Build summary
        run: |
          echo "📊 Build Summary:"
          echo "  ✅ Compile duration: ${{ steps.compile.outputs.compile-duration }}s"
          echo "  🧪 Tests: ${{ inputs.enable-tests && 'Enabled' || 'Skipped' }}"
          echo "  📦 Packages created: ${{ steps.package.outputs.package-count }}"
          echo "  📦 Package duration: ${{ steps.package.outputs.package-duration }}s"
          echo "  🎯 Java version: ${{ inputs.java-version }}"
          echo "  📁 Source directory: ${{ inputs.src-directory }}"

  # Lark notification job (success)
  notify-success:
    name: 'Thông báo thành công'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build]
    if: success() && inputs.lark-webhook-url != ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Send success notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ inputs.lark-webhook-url }}
          title: "Java Library CI Build"
          message: "Build completed successfully"
          status: "success"
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'N/A' }}
          commit-author: ${{ github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  no-changes:
    name: 'Không có thay đổi code'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.has-changes == 'false' && 
      !inputs.force_build &&
      github.event_name != 'workflow_dispatch'
    
    steps:
      - name: Skip build
        run: |
          echo "ℹ️ Không có thay đổi trong thư mục ${{ inputs.src-directory }}, bỏ qua build."
          echo "✅ Workflow hoàn thành mà không cần build."

  # Lark notification job (failure)
  notify-failure:
    name: 'Thông báo thất bại'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build]
    if: always() && failure() && inputs.lark-webhook-url != ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Send failure notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ inputs.lark-webhook-url }}
          title: "Java Library CI Build"
          message: "Build process failed"
          status: "failure"
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'N/A' }}
          commit-author: ${{ github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
