name: 'Reusable Maven Package CD'

on:
  workflow_call:
    inputs:
      src-directory:
        description: 'Thư mục chứa source code (mặc định: .)'
        required: false
        type: string
        default: '.'
      java-version:
        description: 'Phiên bản Java (mặc định: 21)'
        required: false
        type: string
        default: '21'
      build-configuration:
        description: 'Build configuration (mặc định: Release)'
        required: false
        type: string
        default: 'Release'
      package-version:
        description: 'Version cho package (ví dụ: v1.0.0)'
        required: true
        type: string
      maven-registry-url:
        description: 'URL của Maven registry'
        required: false
        type: string
        default: 'https://package.ospgroup.io.vn/repository/maven-releases/'
      enable-tests:
        description: '<PERSON><PERSON> chạy tests không (mặc định: true)'
        required: false
        type: boolean
        default: true
      skip-duplicate:
        description: 'Bỏ qua nếu package đã tồn tại (mặc định: true)'
        required: false
        type: boolean
        default: true
      force_build:
        description: 'Bắt buộc build ngay cả khi không có thay đổi'
        required: false
        type: boolean
        default: false
      additional-build-args:
        description: 'Tham số bổ sung cho Maven build'
        required: false
        type: string
        default: ''
    secrets:
      VAULT_TOKEN:
        description: 'Vault token for authentication'
        required: true
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL cho notifications'
        required: false

permissions:
  contents: read
  packages: write

jobs:
  validate-tag:
    name: 'Validate Tag và Version'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      package-version: ${{ steps.version.outputs.package-version }}
      is-valid: ${{ steps.version.outputs.is-valid }}
    steps:
      - name: Validate và normalize version
        id: version
        run: |
          TAG_NAME="${{ inputs.package-version }}"
          echo "🏷️ Input tag: $TAG_NAME"
          
          # Remove 'v' prefix if exists
          CLEAN_VERSION=${TAG_NAME#v}
          echo "📦 Clean version: $CLEAN_VERSION"
          
          # Validate semantic version format
          if [[ ! $CLEAN_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?$ ]]; then
            echo "❌ Invalid version format: $CLEAN_VERSION"
            echo "Version phải có format: 1.0.0 hoặc 1.0.0-beta"
            echo "is-valid=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          echo "✅ Valid version: $CLEAN_VERSION"
          echo "package-version=$CLEAN_VERSION" >> $GITHUB_OUTPUT
          echo "is-valid=true" >> $GITHUB_OUTPUT

  build-and-test:
    name: 'Build và Test Maven Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: validate-tag
    if: needs.validate-tag.outputs.is-valid == 'true'
    outputs:
      package-names: ${{ steps.package.outputs.package-names }}
      package-count: ${{ steps.package.outputs.package-count }}
      packages-path: ${{ steps.package.outputs.packages-path }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java-version }}
          distribution: 'temurin'
          cache: 'maven'

      - name: Import Maven credentials from Vault
        uses: ospgroupvn/k8s-deployment/.github/actions/add-maven-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Configure Maven settings
        run: |
          mkdir -p ~/.m2
          cat > ~/.m2/settings.xml << EOF
          <?xml version="1.0" encoding="UTF-8"?>
          <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                    http://maven.apache.org/xsd/settings-1.0.0.xsd">
            <servers>
              <server>
                <id>osp-maven-releases</id>
                <username>\${OSP_PACKAGE_USERNAME}</username>
                <password>\${OSP_PACKAGE_PASSWORD}</password>
              </server>
            </servers>
          </settings>
          EOF

      - name: Update pom.xml version
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔄 Updating pom.xml version to ${{ needs.validate-tag.outputs.package-version }}..."
          mvn versions:set -DnewVersion=${{ needs.validate-tag.outputs.package-version }} -DgenerateBackupPoms=false

      - name: Compile project
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔨 Compiling Maven project..."
          mvn clean compile ${{ inputs.additional-build-args }}

      - name: Run tests
        if: inputs.enable-tests
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🧪 Running tests..."
          mvn test ${{ inputs.additional-build-args }}

      - name: Package project
        id: package
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "📦 Packaging Maven project..."
          mvn package -DskipTests ${{ inputs.additional-build-args }}

          # Extract artifact info from pom.xml
          if [ -f "pom.xml" ]; then
            ARTIFACT_ID=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout)
            GROUP_ID=$(mvn help:evaluate -Dexpression=project.groupId -q -DforceStdout)
            PACKAGE_NAME="${GROUP_ID}:${ARTIFACT_ID}"
            echo "📦 Package: $PACKAGE_NAME"
          else
            PACKAGE_NAME="unknown"
          fi

          # Count created packages
          package_count=$(find . -name "*.jar" -not -path "*/target/test-classes/*" | wc -l)
          echo "📋 Created $package_count package(s)"

          echo "package-names=$PACKAGE_NAME" >> $GITHUB_OUTPUT
          echo "package-count=$package_count" >> $GITHUB_OUTPUT
          echo "packages-path=./target" >> $GITHUB_OUTPUT

      - name: Upload packages as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: maven-packages-${{ needs.validate-tag.outputs.package-version }}
          path: ${{ inputs.src-directory }}/target/*.jar
          retention-days: 30

  publish-packages:
    name: 'Publish Maven Packages'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test]
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java-version }}
          distribution: 'temurin'
          cache: 'maven'

      - name: Import Maven credentials from Vault
        uses: ospgroupvn/k8s-deployment/.github/actions/add-maven-admin@main
        with:
          vault-token: ${{ secrets.VAULT_TOKEN }}

      - name: Configure Maven settings
        run: |
          mkdir -p ~/.m2
          cat > ~/.m2/settings.xml << EOF
          <?xml version="1.0" encoding="UTF-8"?>
          <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                    http://maven.apache.org/xsd/settings-1.0.0.xsd">
            <servers>
              <server>
                <id>osp-maven-releases</id>
                <username>\${OSP_PACKAGE_USERNAME}</username>
                <password>\${OSP_PACKAGE_PASSWORD}</password>
              </server>
            </servers>
          </settings>
          EOF

      - name: Update pom.xml version and add distribution management
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔄 Updating pom.xml for deployment..."
          mvn versions:set -DnewVersion=${{ needs.validate-tag.outputs.package-version }} -DgenerateBackupPoms=false
          
          # Add distribution management if not exists
          if ! grep -q "<distributionManagement>" pom.xml; then
            echo "📝 Adding distribution management to pom.xml..."
            sed -i '/<\/project>/i\
            <distributionManagement>\
              <repository>\
                <id>osp-maven-releases</id>\
                <url>${{ inputs.maven-registry-url }}</url>\
              </repository>\
            </distributionManagement>' pom.xml
          fi

      - name: Check if package already exists
        id: check-existing
        if: inputs.skip-duplicate
        run: |
          echo "🔍 Checking if package already exists..."
          # This is a simplified check - in practice you might want to check the actual repository
          echo "skip-publish=false" >> $GITHUB_OUTPUT

      - name: Deploy to Maven repository
        if: steps.check-existing.outputs.skip-publish != 'true'
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🚀 Deploying to Maven repository..."
          mvn deploy -DskipTests ${{ inputs.additional-build-args }}

  notify-success:
    name: 'Thông báo thành công'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: success()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send success notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "✅ Maven Package CD thành công - ${{ needs.build-and-test.outputs.package-names }} v${{ needs.validate-tag.outputs.package-version }}"
          message: |
            ✅ **Maven Package đã được publish thành công!**

            📦 **Package**: ${{ needs.build-and-test.outputs.package-names }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count }} artifact(s)
            ☕ **Java Version**: ${{ inputs.java-version }}
            🎯 **Configuration**: ${{ inputs.build-configuration }}
            📚 **Registry**: ${{ inputs.maven-registry-url }}
          status: 'success'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}

  notify-failure:
    name: 'Thông báo thất bại'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: failure()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send failure notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "❌ Maven Package CD thất bại - ${{ needs.build-and-test.outputs.package-names || 'Unknown' }} v${{ needs.validate-tag.outputs.package-version || 'N/A' }}"
          message: |
            💥 **Maven Package CD đã thất bại!**

            📦 **Package**: ${{ needs.build-and-test.outputs.package-names || 'Không xác định' }}
            🔢 **Version**: ${{ needs.validate-tag.outputs.package-version || 'N/A' }}
            📊 **Số lượng**: ${{ needs.build-and-test.outputs.package-count || '0' }} artifact(s)
            ☕ **Java Version**: ${{ inputs.java-version }}
            🎯 **Configuration**: ${{ inputs.build-configuration }}

            🔍 **Kiểm tra logs để biết thêm chi tiết lỗi**
          status: 'failure'
          repository: ${{ github.repository }}
          branch: ${{ github.ref_name }}
          commit-sha: ${{ github.sha }}
          commit-message: ${{ github.event.head_commit.message || 'Tagged release' }}
          commit-author: ${{ github.event.head_commit.author.name || github.actor }}
          workflow-url: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
