name: 'Reusable NPM Package CD'

on:
  workflow_call:
    inputs:
      src-directory:
        description: 'Thư mục chứa source code (mặc định: src/frontend)'
        required: false
        type: string
        default: 'src/frontend'
      node-version:
        description: '<PERSON>ên bản Node.js (mặc định: 20)'
        required: false
        type: string
        default: '20'
      pnpm-version:
        description: 'Phiên bản PNPM (mặc định: 10)'
        required: false
        type: string
        default: '10'
      build-configuration:
        description: 'Build configuration (mặc định: production)'
        required: false
        type: string
        default: 'production'
      package-version:
        description: 'Version cho package (ví dụ: v1.0.0)'
        required: true
        type: string
      npm-registry-url:
        description: 'URL của NPM registry'
        required: false
        type: string
        default: 'https://package.ospgroup.io.vn/repository/npm-hosted/'
      enable-tests:
        description: '<PERSON><PERSON> chạy tests không (mặc định: true)'
        required: false
        type: boolean
        default: true
      skip-duplicate:
        description: 'Bỏ qua nếu package đã tồn tại (mặc định: true)'
        required: false
        type: boolean
        default: true
      force_build:
        description: '<PERSON><PERSON><PERSON> bu<PERSON><PERSON> build ngay cả khi không có thay đổi'
        required: false
        type: boolean
        default: false
      additional-build-args:
        description: 'Tham số bổ sung cho build'
        required: false
        type: string
        default: ''
      enable-notifications:
        description: 'Có gửi thông báo Lark hay không'
        required: false
        type: boolean
        default: true
    secrets:
      OSP_PACKAGE_USERNAME:
        description: 'Username cho NPM registry'
        required: true
      OSP_PACKAGE_PASSWORD:
        description: 'Password cho NPM registry'
        required: true
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL cho notifications'
        required: false

permissions:
  contents: read
  packages: write

jobs:
  validate-tag:
    name: 'Validate Tag và Version'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      package-version: ${{ steps.version.outputs.package-version }}
      is-valid: ${{ steps.version.outputs.is-valid }}
    steps:
      - name: Validate và normalize version
        id: version
        run: |
          TAG_NAME="${{ inputs.package-version }}"
          echo "🏷️ Input tag: $TAG_NAME"
          
          # Remove 'v' prefix if exists
          CLEAN_VERSION=${TAG_NAME#v}
          echo "📦 Clean version: $CLEAN_VERSION"
          
          # Validate semantic version format
          if [[ ! $CLEAN_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?$ ]]; then
            echo "❌ Invalid version format: $CLEAN_VERSION"
            echo "Version phải có format: 1.0.0 hoặc 1.0.0-beta"
            echo "is-valid=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          echo "✅ Valid version: $CLEAN_VERSION"
          echo "package-version=$CLEAN_VERSION" >> $GITHUB_OUTPUT
          echo "is-valid=true" >> $GITHUB_OUTPUT

  build-and-test:
    name: 'Build và Test NPM Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: validate-tag
    if: needs.validate-tag.outputs.is-valid == 'true'
    outputs:
      package-count: ${{ steps.package.outputs.package-count }}
      packages-path: ${{ steps.package.outputs.packages-path }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm-version }}
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'pnpm'
          cache-dependency-path: ${{ inputs.src-directory }}/pnpm-lock.yaml

      - name: Configure NPM registry
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔧 Configuring NPM registry..."
          pnpm config set registry ${{ inputs.npm-registry-url }}
          
          # Create .npmrc for authentication
          cat > .npmrc << EOF
          registry=${{ inputs.npm-registry-url }}
          //package.ospgroup.io.vn/repository/npm-hosted/:_auth=$(echo -n "${{ secrets.OSP_PACKAGE_USERNAME }}:${{ secrets.OSP_PACKAGE_PASSWORD }}" | base64)
          //package.ospgroup.io.vn/repository/npm-hosted/:always-auth=true
          EOF

      - name: Update package.json version
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔄 Updating package.json version to ${{ needs.validate-tag.outputs.package-version }}..."
          pnpm version ${{ needs.validate-tag.outputs.package-version }} --no-git-tag-version

      - name: Install dependencies
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "📦 Installing dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            pnpm install --frozen-lockfile
          else
            pnpm install
          fi

      - name: Run type check
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔍 Running type check..."
          pnpm run type-check

      - name: Run linting
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🧹 Running linting..."
          pnpm run lint

      - name: Run tests
        if: inputs.enable-tests
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🧪 Running tests..."
          pnpm run test

      - name: Build package
        id: package
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔨 Building package..."
          pnpm run build ${{ inputs.additional-build-args }}
          
          # Check if build was successful
          if [ -d "dist" ]; then
            echo "✅ Build successful - dist directory created"
            package_count=1
          else
            echo "❌ Build failed - no dist directory found"
            exit 1
          fi
          
          echo "package-count=$package_count" >> $GITHUB_OUTPUT
          echo "packages-path=./dist" >> $GITHUB_OUTPUT

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: npm-package-${{ needs.validate-tag.outputs.package-version }}
          path: ${{ inputs.src-directory }}/dist
          retention-days: 30

  publish-packages:
    name: 'Publish NPM Package'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test]
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm-version }}
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'pnpm'
          cache-dependency-path: ${{ inputs.src-directory }}/pnpm-lock.yaml

      - name: Configure NPM registry
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔧 Configuring NPM registry for publishing..."
          pnpm config set registry ${{ inputs.npm-registry-url }}
          
          # Create .npmrc for authentication
          cat > .npmrc << EOF
          registry=${{ inputs.npm-registry-url }}
          //package.ospgroup.io.vn/repository/npm-hosted/:_auth=$(echo -n "${{ secrets.OSP_PACKAGE_USERNAME }}:${{ secrets.OSP_PACKAGE_PASSWORD }}" | base64)
          //package.ospgroup.io.vn/repository/npm-hosted/:always-auth=true
          EOF

      - name: Update package.json version
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔄 Updating package.json version to ${{ needs.validate-tag.outputs.package-version }}..."
          pnpm version ${{ needs.validate-tag.outputs.package-version }} --no-git-tag-version

      - name: Install dependencies and build
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "📦 Installing dependencies..."
          if [ -f "pnpm-lock.yaml" ]; then
            pnpm install --frozen-lockfile
          else
            pnpm install
          fi
          
          echo "🔨 Building package..."
          pnpm run build ${{ inputs.additional-build-args }}

      - name: Check if package already exists
        id: check-existing
        if: inputs.skip-duplicate
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🔍 Checking if package already exists..."
          PACKAGE_NAME=$(node -p "require('./package.json').name")
          VERSION="${{ needs.validate-tag.outputs.package-version }}"
          
          # Try to get package info from registry
          if pnpm view "$PACKAGE_NAME@$VERSION" version 2>/dev/null; then
            echo "⚠️ Package $PACKAGE_NAME@$VERSION already exists"
            echo "skip-publish=true" >> $GITHUB_OUTPUT
          else
            echo "✅ Package $PACKAGE_NAME@$VERSION does not exist, proceeding with publish"
            echo "skip-publish=false" >> $GITHUB_OUTPUT
          fi

      - name: Publish package
        if: steps.check-existing.outputs.skip-publish != 'true'
        working-directory: ${{ inputs.src-directory }}
        run: |
          echo "🚀 Publishing NPM package..."
          pnpm publish --no-git-checks

  notify-result:
    name: 'Thông báo kết quả'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [validate-tag, build-and-test, publish-packages]
    if: always() && inputs.enable-notifications && secrets.LARK_WEBHOOK_URL
    steps:
      - name: Send Lark notification
        uses: ospgroupvn/k8s-deployment/.github/actions/lark-notification@main
        with:
          webhook-url: ${{ secrets.LARK_WEBHOOK_URL }}
          title: "NPM Package Release"
          status: ${{ job.status }}
          version: ${{ needs.validate-tag.outputs.package-version }}
          package-count: ${{ needs.build-and-test.outputs.package-count }}
