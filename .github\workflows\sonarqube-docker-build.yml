name: "03 - 🔍 SonarQube Auto Build"

on:
  push:
    paths:
      - 'custom-docker-images/sonarqube/**'
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'custom-docker-images/sonarqube/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      sonarqube_version:
        description: 'SonarQube version'
        required: false
        default: '10.6'
        type: string
      plugin_version:
        description: 'Community Branch Plugin version'
        required: false
        default: '1.21.0'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

env:
  REGISTRY: dockerhub.ospgroup.vn
  IMAGE_NAME: sonarqube-community-branch

jobs:
  build-sonarqube:
    name: 🔧 Build SonarQube
    uses: ./.github/workflows/reusable-docker-build.yml
    with:
      dockerfile-path: 'custom-docker-images/sonarqube'
      image-name: 'sonarqube-community-branch'
      build-args: "{\"SONARQUBE_VERSION\": \"${{ github.event_name == 'workflow_dispatch' && inputs.sonarqube_version || '10.6' }}\", \"PLUGIN_VERSION\": \"${{ github.event_name == 'workflow_dispatch' && inputs.plugin_version || '1.21.0' }}\"}"
      force-rebuild: ${{ github.event_name == 'workflow_dispatch' && inputs.force_rebuild || false }}
      enable-test: false
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "SonarQube Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}