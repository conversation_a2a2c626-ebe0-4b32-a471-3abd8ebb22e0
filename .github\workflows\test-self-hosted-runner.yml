name: "04 - 🧪 Test Self-Hosted Runner"

# Trigger conditions
on:
  # Manual trigger
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Loại test muốn chạy'
        required: false
        default: 'basic'
        type: choice
        options:
          - basic
          - docker
          - kubernetes
          - full
      
  # Auto trigger on push to main (để test tự động)
  push:
    branches: [main]
    paths:
      - '.github/workflows/test-self-hosted-runner.yml'
      - 'scripts/portainer-github-runner/**'
  
  # Pull request trigger
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/test-self-hosted-runner.yml'

# Environment variables
env:
  TEST_TIMESTAMP: ${{ github.run_number }}-${{ github.run_attempt }}

jobs:
  # =============================================================================
  # Job 1: Basic System Info Test
  # =============================================================================
  basic-test:
    name: 🔍 Basic System Information
    runs-on: [self-hosted, linux]
    timeout-minutes: 10
    
    steps:
      - name: 📋 Checkout code
        uses: actions/checkout@v4
        
      - name: 🖥️ System Information
        run: |
          echo "=== 🖥️ SYSTEM INFORMATION ==="
          echo "Hostname: $(hostname)"
          echo "User: $(whoami)"
          echo "Working directory: $(pwd)"
          echo "Home directory: $HOME"
          echo "Runner OS: $RUNNER_OS"
          echo "Runner Architecture: $RUNNER_ARCH"
          echo "Runner Name: $RUNNER_NAME"
          echo ""
          
          echo "=== 💾 SYSTEM RESOURCES ==="
          echo "CPU Info:"
          nproc --all
          lscpu | grep -E '^Thread|^Core|^Socket|^CPU\('
          echo ""
          
          echo "Memory Info:"
          free -h
          echo ""
          
          echo "Disk Usage:"
          df -h /
          echo ""
          
          echo "Load Average:"
          uptime
          
      - name: 🔧 Available Tools
        run: |
          echo "=== 🔧 AVAILABLE TOOLS ==="
          echo "Git: $(git --version 2>/dev/null || echo 'Not installed')"
          echo "Docker: $(docker --version 2>/dev/null || echo 'Not installed')"
          echo "Docker Compose: $(docker-compose --version 2>/dev/null || echo 'Not installed')"
          echo "Kubectl: $(kubectl version --client --short 2>/dev/null || echo 'Not installed')"
          echo "Helm: $(helm version --short 2>/dev/null || echo 'Not installed')"
          echo "Curl: $(curl --version | head -n1 2>/dev/null || echo 'Not installed')"
          echo "Wget: $(wget --version | head -n1 2>/dev/null || echo 'Not installed')"
          echo "Jq: $(jq --version 2>/dev/null || echo 'Not installed')"
          echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
          echo "Python: $(python3 --version 2>/dev/null || echo 'Not installed')"
          
      - name: 🌐 Network Connectivity Test
        run: |
          echo "=== 🌐 NETWORK CONNECTIVITY ==="
          echo "Testing GitHub connectivity..."
          curl -I https://github.com || echo "GitHub connection failed"
          echo ""
          
          echo "Testing Docker Hub connectivity..."
          curl -I https://hub.docker.com || echo "Docker Hub connection failed"
          echo ""
          
          echo "DNS Resolution:"
          nslookup github.com || echo "DNS resolution failed"

  # =============================================================================
  # Job 2: Docker Test (chỉ chạy khi có Docker)
  # =============================================================================
  docker-test:
    name: 🐳 Docker Functionality Test  
    runs-on: [self-hosted, linux]
    if: ${{ github.event.inputs.test_type == 'docker' || github.event.inputs.test_type == 'full' || github.event.inputs.test_type == '' }}
    timeout-minutes: 15
    
    steps:
      - name: 📋 Checkout code
        uses: actions/checkout@v4
        
      - name: 🐳 Docker Version & Info
        run: |
          echo "=== 🐳 DOCKER INFORMATION ==="
          docker --version
          docker info
          
      - name: 🧪 Docker Hello World Test
        run: |
          echo "=== 🧪 TESTING DOCKER FUNCTIONALITY ==="
          docker run --rm hello-world
          
      - name: 🔧 Docker Build Test
        run: |
          echo "=== 🔧 TESTING DOCKER BUILD ==="
          # Tạo một Dockerfile đơn giản
          cat > Dockerfile.test << 'EOF'
          FROM alpine:latest
          RUN echo "OSP Group Self-Hosted Runner Test" > /test.txt
          CMD cat /test.txt
          EOF
          
          # Build image
          docker build -f Dockerfile.test -t runner-test:${{ env.TEST_TIMESTAMP }} .
          
          # Run container
          docker run --rm runner-test:${{ env.TEST_TIMESTAMP }}
          
          # Cleanup
          docker rmi runner-test:${{ env.TEST_TIMESTAMP }}
          rm Dockerfile.test
          
      - name: 🧹 Docker System Cleanup
        run: |
          echo "=== 🧹 DOCKER CLEANUP ==="
          docker system df
          # Cleanup unused images/containers
          docker system prune -f
          docker system df

  # =============================================================================  
  # Job 3: Kubernetes Tools Test (nếu có kubectl)
  # =============================================================================
  kubernetes-test:
    name: ☸️ Kubernetes Tools Test
    runs-on: [self-hosted, linux]
    if: ${{ github.event.inputs.test_type == 'kubernetes' || github.event.inputs.test_type == 'full' }}
    timeout-minutes: 10
    
    steps:
      - name: 📋 Checkout code
        uses: actions/checkout@v4
        
      - name: ☸️ Kubectl & Helm Versions
        run: |
          echo "=== ☸️ KUBERNETES TOOLS ==="
          if command -v kubectl &> /dev/null; then
            echo "Kubectl version:"
            kubectl version --client --output=yaml
            echo ""
            
            echo "Testing kubeconfig access..."
            export KUBECONFIG=$(pwd)/.kube/config
            if [[ -f ".kube/config" ]]; then
              echo "Kubeconfig found, testing connection..."
              kubectl cluster-info --request-timeout=10s || echo "Cluster connection failed"
              kubectl get nodes --request-timeout=10s || echo "Cannot get nodes"
            else
              echo "No kubeconfig found at .kube/config"
            fi
          else
            echo "Kubectl not installed"
          fi
          
          echo ""
          if command -v helm &> /dev/null; then
            echo "Helm version:"
            helm version
          else
            echo "Helm not installed"
          fi

  # =============================================================================
  # Job 4: Performance & Stress Test
  # =============================================================================
  performance-test:
    name: ⚡ Performance Test
    runs-on: [self-hosted, linux]
    if: ${{ github.event.inputs.test_type == 'full' }}
    timeout-minutes: 20
    
    steps:
      - name: 📋 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔥 CPU Stress Test
        run: |
          echo "=== 🔥 CPU STRESS TEST ==="
          # Chạy stress test trong 30 giây
          timeout 30s dd if=/dev/zero of=/dev/null bs=1M count=1000 || echo "CPU test completed"
          
      - name: 💾 Memory Test  
        run: |
          echo "=== 💾 MEMORY TEST ==="
          # Tạo file 100MB để test memory
          dd if=/dev/zero of=/tmp/memtest bs=1M count=100
          ls -lh /tmp/memtest
          rm /tmp/memtest
          
      - name: 💽 Disk I/O Test
        run: |
          echo "=== 💽 DISK I/O TEST ==="
          # Test ghi 500MB
          time dd if=/dev/zero of=/tmp/disktest bs=1M count=500
          # Test đọc
          time dd if=/tmp/disktest of=/dev/null bs=1M
          # Cleanup
          rm /tmp/disktest
          
      - name: 🌐 Network Speed Test
        run: |
          echo "=== 🌐 NETWORK SPEED TEST ==="
          # Test download từ GitHub (file nhỏ)
          time curl -o /tmp/test-download.zip -L https://github.com/kubernetes/kubernetes/archive/refs/tags/v1.28.0.zip
          ls -lh /tmp/test-download.zip
          rm /tmp/test-download.zip

  # =============================================================================
  # Job 5: Security & Permissions Test
  # =============================================================================
  security-test:
    name: 🔒 Security & Permissions Test
    runs-on: [self-hosted, linux]
    if: ${{ github.event.inputs.test_type == 'full' }}
    timeout-minutes: 10
    
    steps:
      - name: 📋 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔒 User & Group Information
        run: |
          echo "=== 🔒 SECURITY INFORMATION ==="
          echo "Current user: $(whoami)"
          echo "User ID: $(id)"
          echo "Groups: $(groups)"
          echo "Sudo access: $(sudo -n true && echo 'Yes' || echo 'No')"
          
      - name: 📁 File System Permissions
        run: |
          echo "=== 📁 FILE SYSTEM PERMISSIONS ==="
          echo "Home directory permissions:"
          ls -la $HOME
          echo ""
          
          echo "Working directory permissions:"
          ls -la .
          echo ""
          
          echo "Docker socket permissions:"
          ls -la /var/run/docker.sock
          
      - name: 🔐 Environment Variables (Safe ones)
        run: |
          echo "=== 🔐 ENVIRONMENT VARIABLES ==="
          echo "PATH: $PATH"
          echo "HOME: $HOME"
          echo "GITHUB_WORKSPACE: $GITHUB_WORKSPACE"
          echo "RUNNER_WORKSPACE: $RUNNER_WORKSPACE"
          echo "GITHUB_REPOSITORY: $GITHUB_REPOSITORY"
          echo "GITHUB_REF: $GITHUB_REF"

  # =============================================================================
  # Job 6: Final Report
  # =============================================================================
  test-report:
    name: 📊 Test Report
    runs-on: [self-hosted, linux]
    needs: [basic-test, docker-test, kubernetes-test, performance-test, security-test]
    if: always()
    timeout-minutes: 5
    
    steps:
      - name: 📊 Generate Test Report
        run: |
          echo "=== 📊 SELF-HOSTED RUNNER TEST REPORT ==="
          echo "Test run: #${{ github.run_number }}"
          echo "Timestamp: $(date)"
          echo "Repository: ${{ github.repository }}"
          echo "Ref: ${{ github.ref }}"
          echo "Triggered by: ${{ github.event_name }}"
          echo ""
          
          echo "=== 📋 JOB RESULTS ==="
          echo "Basic Test: ${{ needs.basic-test.result }}"
          echo "Docker Test: ${{ needs.docker-test.result }}"
          echo "Kubernetes Test: ${{ needs.kubernetes-test.result }}"
          echo "Performance Test: ${{ needs.performance-test.result }}"
          echo "Security Test: ${{ needs.security-test.result }}"
          echo ""
          
          if [[ "${{ needs.basic-test.result }}" == "success" && "${{ needs.docker-test.result }}" != "failure" ]]; then
            echo "✅ Runner đang hoạt động tốt!"
            echo "🚀 Sẵn sàng để chạy các CI/CD workflows!"
          else
            echo "❌ Có vấn đề với runner, cần kiểm tra lại."
          fi
          
      - name: 🎉 Success Message
        if: ${{ needs.basic-test.result == 'success' && needs.docker-test.result != 'failure' }}
        run: |
          echo ""
          echo "🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉"
          echo "🎉  OSP GROUP SELF-HOSTED RUNNER   🎉"
          echo "🎉       HOẠT ĐỘNG HOÀN HẢO!      🎉"
          echo "🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉"