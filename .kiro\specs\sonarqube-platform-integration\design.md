# Design Document

## Overview

Thiết kế tích hợp SonarQube Community Branch Plugin vào platform-services namespace sử dụng Helm chart version 10.6 với custom image. SonarQube sẽ kết nối với PostgreSQL database hiện có và được expose qua Gateway API với domain sonarqube.local. Toàn bộ deployment được quản lý bởi ArgoCD sử dụng app-of-apps pattern.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "External Access"
        Client[Developer/User]
        Domain[sonarqube.local:80]
    end
    
    subgraph "Kubernetes Cluster"
        subgraph "bootstrap namespace"
            ArgoCD[ArgoCD]
            Gateway[Traefik Gateway]
        end
        
        subgraph "platform-services namespace"
            HTTPRoute[SonarQube HTTPRoute]
            SonarQube[SonarQube Pod]
            SonarQubeService[SonarQube Service]
            PostgreSQL[PostgreSQL Service]
            Secrets[SonarQube Secrets]
        end
    end
    
    subgraph "External Registry"
        CustomImage[dockerhub.ospgroup.vn/osp-public/sonarqube-community-branch:1.0.1]
    end
    
    Client --> Domain
    Domain --> Gateway
    Gateway --> HTTPRoute
    HTTPRoute --> SonarQubeService
    SonarQubeService --> SonarQube
    SonarQube --> PostgreSQL
    SonarQube --> Secrets
    ArgoCD --> SonarQube
    SonarQube --> CustomImage
```

### Component Interaction Flow

1. **ArgoCD Deployment Flow:**
   - Parent application `platform-services` detects new SonarQube application manifest
   - ArgoCD creates child application `sonarqube` 
   - Child application deploys Helm chart with custom values
   - HTTPRoute is created for external access

2. **Runtime Flow:**
   - User accesses `http://sonarqube.local/`
   - Traefik Gateway routes to SonarQube HTTPRoute
   - HTTPRoute forwards to SonarQube Service
   - SonarQube connects to PostgreSQL for data persistence

## Components and Interfaces

### 1. Helm Chart Configuration

**Location:** `platform-services/charts/sonarqube/`

**Components:**
- **Chart.yaml:** Defines SonarQube Helm chart dependency version 10.6
- **values.yaml:** Custom values override for OSP-specific configuration

**Key Configurations:**
```yaml
# Custom image override
image:
  repository: dockerhub.ospgroup.vn/osp-public/sonarqube-community-branch
  tag: "1.0.1"

# External PostgreSQL configuration
postgresql:
  enabled: false

jdbcOverwrite:
  enable: true
  jdbcUrl: "*******************************************************************************"
  jdbcUsername: "sonarqube"
  jdbcSecretName: "sonarqube-database"
  jdbcSecretPasswordKey: "password"

# Service configuration
service:
  type: ClusterIP
  port: 9000

# Resource management
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "4Gi"
    cpu: "2000m"
```

### 2. ArgoCD Application

**Location:** `platform-services/platform/sonarqube-app.yaml`

**Interface:**
- **Input:** Git repository changes
- **Output:** Kubernetes resources in platform-services namespace
- **Dependencies:** PostgreSQL service, Secrets

**Configuration:**
```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sonarqube
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/sonarqube
    targetRevision: main
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
```

### 3. HTTPRoute Configuration

**Location:** `platform-services/platform/sonarqube-httproute.yaml`

**Interface:**
- **Input:** HTTP requests to sonarqube.local
- **Output:** Routed traffic to SonarQube service
- **Dependencies:** Traefik Gateway

**Configuration:**
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: sonarqube-httproute
  namespace: platform-services
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
  hostnames:
  - sonarqube.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: sonarqube-sonarqube
      port: 9000
```

### 4. Database Integration

**Database Setup:**
- **Host:** postgresql.platform-services.svc.cluster.local
- **Port:** 5432
- **Admin Credentials:** postgres/postgres123
- **SonarQube Database:** sonarqube
- **SonarQube User:** sonarqube (with full permissions on sonarqube database)

**Secret Management:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: sonarqube-database
  namespace: platform-services
type: Opaque
data:
  password: <base64-encoded-sonarqube-user-password>
```

### 5. Security Configuration

**Secrets Required:**
1. **sonarqube-database:** Database credentials for SonarQube user
2. **sonarqube-admin:** (Optional) Admin credentials for SonarQube

**Network Security:**
- Service type: ClusterIP (internal only)
- External access only via Gateway API
- Database connections within cluster network

## Data Models

### Database Schema

**PostgreSQL Database Structure:**
```sql
-- Database: sonarqube
-- Owner: sonarqube
-- Encoding: UTF8

-- SonarQube will create its own tables automatically
-- Key tables include:
-- - projects
-- - issues  
-- - rules
-- - users
-- - user_tokens
-- - properties
```

### Configuration Data Model

**Helm Values Structure:**
```yaml
sonarqube:
  image:
    repository: string
    tag: string
  
  postgresql:
    enabled: boolean
  
  jdbcOverwrite:
    enable: boolean
    jdbcUrl: string
    jdbcUsername: string
    jdbcSecretName: string
    jdbcSecretPasswordKey: string
  
  service:
    type: string
    port: number
  
  resources:
    requests:
      memory: string
      cpu: string
    limits:
      memory: string
      cpu: string
  
  persistence:
    enabled: boolean
    size: string
    storageClass: string
```

## Error Handling

### Database Connection Errors

**Scenario:** SonarQube cannot connect to PostgreSQL
**Handling:**
- Kubernetes will restart pod automatically via liveness probe
- Check database credentials in secrets
- Verify PostgreSQL service availability
- Review network policies if connection fails

**Recovery Steps:**
1. Verify PostgreSQL service is running
2. Check database credentials in secret
3. Ensure sonarqube database and user exist
4. Review SonarQube logs for specific error messages

### Image Pull Errors

**Scenario:** Cannot pull custom SonarQube image
**Handling:**
- Verify image registry accessibility
- Check image tag exists
- Ensure proper image pull secrets if required

**Recovery Steps:**
1. Test image pull manually: `docker pull dockerhub.ospgroup.vn/osp-public/sonarqube-community-branch:1.0.1`
2. Verify registry credentials if private
3. Check network connectivity to registry

### Gateway/Routing Errors

**Scenario:** Cannot access SonarQube via sonarqube.local
**Handling:**
- Verify HTTPRoute configuration
- Check Gateway status
- Ensure DNS resolution for sonarqube.local

**Recovery Steps:**
1. Check HTTPRoute status: `kubectl get httproute -n platform-services`
2. Verify Gateway is ready: `kubectl get gateway -n bootstrap`
3. Test service connectivity: `kubectl port-forward svc/sonarqube-sonarqube 9000:9000 -n platform-services`

### Resource Constraints

**Scenario:** SonarQube pod fails due to insufficient resources
**Handling:**
- Monitor resource usage
- Adjust resource requests/limits
- Consider node capacity

**Recovery Steps:**
1. Check pod status: `kubectl describe pod -l app=sonarqube -n platform-services`
2. Review resource requests vs node capacity
3. Adjust values.yaml resource configuration if needed

## Testing Strategy

### Unit Testing

**Database Connection Test:**
```bash
# Test PostgreSQL connectivity
kubectl run postgres-test --rm -i --tty --image postgres:13 -- psql -h postgresql.platform-services.svc.cluster.local -U postgres -d postgres
```

**Image Pull Test:**
```bash
# Test custom image accessibility
kubectl run sonarqube-test --rm -i --tty --image dockerhub.ospgroup.vn/osp-public/sonarqube-community-branch:1.0.1 -- /bin/bash
```

### Integration Testing

**End-to-End Access Test:**
1. Deploy SonarQube via ArgoCD
2. Wait for pod to be ready
3. Test HTTP access via curl:
```bash
curl -I http://sonarqube.local/
# Expected: HTTP 200 with SonarQube headers
```

**Database Integration Test:**
1. Access SonarQube admin interface
2. Verify database connection in System Info
3. Create test project and verify data persistence

### Performance Testing

**Resource Usage Monitoring:**
```bash
# Monitor SonarQube resource usage
kubectl top pod -l app=sonarqube -n platform-services

# Check database connections
kubectl exec -it postgresql-0 -n platform-services -- psql -U postgres -c "SELECT * FROM pg_stat_activity WHERE datname='sonarqube';"
```

**Load Testing:**
- Use SonarQube scanner on sample projects
- Monitor response times and resource consumption
- Verify system stability under normal load

### Monitoring and Alerting

**Health Checks:**
- Liveness probe: `/api/system/status`
- Readiness probe: `/api/system/status`
- Startup probe: `/` (initial delay for startup)

**Metrics to Monitor:**
- Pod CPU/Memory usage
- Database connection count
- HTTP response times
- Error rates in logs

**Alerting Conditions:**
- Pod restart frequency > 3 per hour
- Memory usage > 80% of limit
- Database connection failures
- HTTP 5xx error rate > 5%