# LLM Configuration Guide

## Tổng quan

OSP Agent hỗ trợ nhiều LLM providers và cho phép cấu hình linh hoạt thông qua environment variables. Hệ thống mặc định sử dụng **OpenRouter** với model **Claude 3.5 Sonnet** để có hiệu suất tốt nhất.

## Cấu hình Environment Variables

### 1. LLM Provider Selection

```bash
# Chọn provider (mặc định: openrouter)
LLM_PROVIDER=openrouter
```

**Supported providers:**
- `openai` - OpenAI GPT models
- `gemini` - Google Gemini models  
- `openrouter` - OpenRouter (khuyến nghị - nhiều model, gi<PERSON> tốt)

### 2. Model Selection

```bash
# Chọn model cụ thể (tùy chọn - sẽ dùng default nếu không chỉ định)
LLM_MODEL_NAME=anthropic/claude-3.5-sonnet
```

### 3. API Keys

```bash
# OpenAI (bắt buộ<PERSON> nếu LLM_PROVIDER=openai)
OPENAI_API_KEY=your_openai_api_key

# Google Gemini (bắt buộc nếu LLM_PROVIDER=gemini)
GOOGLE_API_KEY=your_google_api_key
GEMINI_API_KEY=your_gemini_api_key

# OpenRouter (bắt buộc nếu LLM_PROVIDER=openrouter)
OPENROUTER_API_KEY=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
```

## Supported Models

### OpenAI Models
- `gpt-4o-mini` (default cho OpenAI)
- `gpt-4o`

### Google Gemini Models
- `gemini-2.5-flash` (default cho Gemini)
- `gemini-2.5-pro`

### OpenRouter Models (Khuyến nghị)

#### Claude Models (Anthropic)
- `anthropic/claude-3.5-sonnet` ⭐ **Default - Khuyến nghị**
- `anthropic/claude-3.5-haiku`

#### GPT Models via OpenRouter
- `openai/gpt-4o`
- `openai/gpt-4o-mini`

#### Gemini via OpenRouter
- `google/gemini-2.5-flash`
- `google/gemini-2.5-pro`

#### Other Popular Models
- `qwen/qwen-2.5-72b-instruct`
- `meta-llama/llama-3.1-70b-instruct`

#### Free Models (Testing)
- `google/gemini-2.5-flash-lite-preview-06-17`

## Configuration Examples

### 1. Production Setup (Khuyến nghị)
```bash
LLM_PROVIDER=openrouter
LLM_MODEL_NAME=anthropic/claude-3.5-sonnet
OPENROUTER_API_KEY=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
```

### 2. OpenAI Setup
```bash
LLM_PROVIDER=openai
LLM_MODEL_NAME=gpt-4o-mini
OPENAI_API_KEY=your_openai_api_key
```

### 3. Gemini Setup
```bash
LLM_PROVIDER=gemini
LLM_MODEL_NAME=gemini-2.5-flash
GEMINI_API_KEY=your_gemini_api_key
```

### 4. Cost-Effective Setup
```bash
LLM_PROVIDER=openrouter
LLM_MODEL_NAME=google/gemini-2.5-flash-lite-preview-06-17
OPENROUTER_API_KEY=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
```

## Default Behavior

Nếu không cấu hình environment variables:

1. **LLM_PROVIDER**: Mặc định `openrouter`
2. **LLM_MODEL_NAME**: Tự động chọn dựa trên provider:
   - OpenAI → `gpt-4o-mini`
   - Gemini → `gemini-2.5-flash`
   - OpenRouter → `anthropic/claude-3.5-sonnet`

## Agent-Specific Configuration

Tất cả agents (OSPApp, TaskAgent, FallbackAgent) đều sử dụng cùng cấu hình LLM từ environment variables. Điều này đảm bảo tính nhất quán và dễ quản lý.

## Deployment Configuration

### Docker Compose
```yaml
environment:
  - LLM_PROVIDER=openrouter
  - LLM_MODEL_NAME=anthropic/claude-3.5-sonnet
  - OPENROUTER_API_KEY=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
```

### Kubernetes
```yaml
env:
- name: LLM_PROVIDER
  value: "openrouter"
- name: LLM_MODEL_NAME
  value: "anthropic/claude-3.5-sonnet"
- name: OPENROUTER_API_KEY
  value: "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"
```

## Troubleshooting

### Common Issues

1. **Invalid LLM_PROVIDER**: Hệ thống sẽ fallback về `openrouter`
2. **Invalid LLM_MODEL_NAME**: Hệ thống sẽ dùng default model cho provider
3. **Missing API Key**: Agent sẽ fail khi initialize với error message rõ ràng

### Logs to Monitor

```
🧠 Thiết lập OpenRouter LLM: anthropic/claude-3.5-sonnet
✅ TaskAgent đã sẵn sàng!
```

### Testing Configuration

Sau khi thay đổi cấu hình, restart application và kiểm tra logs để đảm bảo:
1. Provider được load đúng
2. Model được khởi tạo thành công
3. Không có error về API keys

## Best Practices

1. **Sử dụng OpenRouter**: Giá tốt, nhiều model, reliable
2. **Claude 3.5 Sonnet**: Balance tốt giữa performance và cost
3. **Environment Variables**: Luôn cấu hình qua env vars, không hardcode
4. **API Key Security**: Không commit API keys vào git
5. **Monitoring**: Theo dõi logs để phát hiện issues sớm

## Migration từ Cấu hình Cũ

Nếu bạn đang sử dụng hardcoded LLM configuration trong code:

### Before (Cũ)
```python
self.task_agent = AgentTask(LLMProvider.GEMINI, ModelName.GEMINI_2_5_FLASH)
```

### After (Mới)
```python
self.task_agent = AgentTask()  # Sẽ đọc từ environment variables
```

Và thêm vào `.env`:
```bash
LLM_PROVIDER=openrouter
LLM_MODEL_NAME=anthropic/claude-3.5-sonnet
OPENROUTER_API_KEY=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
```
