# Bootstrap Services

Th<PERSON> mục này chứa các dịch vụ nền tảng cần thiết cho Kubernetes cluster, được triển khai trong namespace `bootstrap`.

## Cấu trúc thư mục

```
bootstrap/
├── README.md                 # File này
├── common/                   # Cấu hình chung
│   ├── namespace.yaml       # Định nghĩa namespace bootstrap
│   └── install.sh           # Script cài đặt tất cả dịch vụ
├── vault/                   # HashiCorp Vault
│   ├── Chart.yaml          # Helm chart metadata
│   ├── values.yaml         # Cấu hình Vault
│   └── charts/             # Helm chart dependencies
├── traefik/                # Traefik Gateway
│   ├── Chart.yaml          # Helm chart metadata
│   ├── values.yaml         # Cấu hình Traefik
│   └── charts/             # Helm chart dependencies
└── argocd/                 # ArgoCD
    ├── Chart.yaml          # Helm chart metadata
    ├── values.yaml         # Cấu hình ArgoCD
    └── charts/             # Helm chart dependencies
```

## Thứ tự cài đặt

1. **Vault** - Quản lý secrets
2. **Traefik** - Gateway và Load Balancer
3. **ArgoCD** - GitOps continuous deployment

## Cài đặt

```bash
# Cài đặt tất cả dịch vụ
cd bootstrap
./common/install.sh

# Hoặc cài đặt từng dịch vụ
helm install vault ./vault -n bootstrap
helm install traefik ./traefik -n bootstrap
helm install argocd ./argocd -n bootstrap
```

## Truy cập dịch vụ

Sau khi cài đặt, xem file `docs/bootstrap-services-report.md` để biết thông tin truy cập chi tiết.
