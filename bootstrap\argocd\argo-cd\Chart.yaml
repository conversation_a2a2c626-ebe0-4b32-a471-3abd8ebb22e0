annotations:
  artifacthub.io/changes: |
    - kind: changed
      description: Bump dex to v2.44.0
  artifacthub.io/signKey: |
    fingerprint: 2B8F22F57260EFA67BE1C5824B11F800CD9D2252
    url: https://argoproj.github.io/argo-helm/pgp_keys.asc
apiVersion: v2
appVersion: v3.1.1
dependencies:
- condition: redis-ha.enabled
  name: redis-ha
  repository: https://dandydeveloper.github.io/charts/
  version: 4.33.7
description: A Helm chart for Argo CD, a declarative, GitOps continuous delivery tool
  for Kubernetes.
home: https://github.com/argoproj/argo-helm
icon: https://argo-cd.readthedocs.io/en/stable/assets/logo.png
keywords:
- argoproj
- argocd
- gitops
kubeVersion: '>=1.25.0-0'
maintainers:
- name: argoproj
  url: https://argoproj.github.io/
name: argo-cd
sources:
- https://github.com/argoproj/argo-helm/tree/main/charts/argo-cd
- https://github.com/argoproj/argo-cd
version: 8.3.3
