{{- if and .Values.auth (not .Values.existingSecret) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "redis-ha.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
type: Opaque
data:
  {{ .Values.authKey }}: {{ .Values.redisPassword | b64enc | quote }}
{{- end -}}
