apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "redis-ha.fullname" . }}-health-configmap
  namespace: {{ .Release.Namespace | quote }}
  labels:
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app: {{ template "redis-ha.fullname" . }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
data:
  redis_liveness.sh: |
{{- include "redis_liveness.sh" . }}
  redis_readiness.sh: |
{{- include "redis_readiness.sh" . }}
  sentinel_liveness.sh: |
{{- include "sentinel_liveness.sh" . }}
