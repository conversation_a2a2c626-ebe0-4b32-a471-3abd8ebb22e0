{{- if .Values.podDisruptionBudget -}}
apiVersion: {{ template "redis-ha.podDisruptionBudget.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "redis-ha.fullname" . }}-pdb
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  selector:
    matchLabels:
      release: {{ .Release.Name }}
      app: {{ template "redis-ha.name" . }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end -}}
