{{- if not .Values.restore.existingSecret }}


{{- $regexRestoreS3 := "^s3://.+|^S3://.+" -}}
{{- $regexRestoreSSH := "^.+@.+:.+" -}}

{{- if or (regexFind $regexRestoreSSH (toString .Values.restore.ssh.source)) (regexFind $regexRestoreS3 (toString .Values.restore.s3.source)) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "redis-ha.fullname" . }}-secret
  namespace: {{ .Release.Namespace | quote }}
  labels:
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app: {{ template "redis-ha.fullname" . }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
type: Opaque
data:
{{- if regexFind $regexRestoreSSH (toString .Values.restore.ssh.source) }}
  SSH_KEY: "{{ .Values.restore.ssh.key | b64enc }}"
{{- end }}
{{- if regexFind $regexRestoreS3 (toString .Values.restore.s3.source) }}
  AWS_SECRET_ACCESS_KEY: "{{ .Values.restore.s3.secret_key | b64enc }}"
  AWS_ACCESS_KEY_ID: "{{ .Values.restore.s3.access_key | b64enc }}"
{{- end }}
{{- end }}

{{- end }}