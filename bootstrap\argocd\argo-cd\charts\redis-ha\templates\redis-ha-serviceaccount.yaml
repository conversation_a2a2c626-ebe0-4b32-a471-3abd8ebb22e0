{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "redis-ha.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app: {{ template "redis-ha.fullname" . }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
{{- if .Values.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.serviceAccount.annotations | indent 4 }}
{{- end }}
{{- if or .Values.auth .Values.sentinel.auth }}
secrets:
{{- end }}
{{- if .Values.auth }}
- name: {{ default (include "redis-ha.fullname" .) .Values.existingSecret }}
{{- end }}
{{- if .Values.sentinel.auth }}
- name: {{ default (printf "%s-sentinel" (include "redis-ha.fullname" .)) .Values.sentinel.existingSecret }}
{{- end }}
{{- if .Values.imagePullSecrets }}
imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 0 }}
{{- end }}
{{- end }}
