{{- if .Values.haproxy.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "redis-ha.fullname" . }}-haproxy
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    component: {{ template "redis-ha.fullname" . }}-haproxy
{{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
{{- end }}
{{- range $key, $value := .Values.haproxy.service.labels }}
    {{ $key }}: {{ $value | quote }}
{{- end }}
  annotations:
  {{- if .Values.haproxy.service.annotations }}
{{ toYaml .Values.haproxy.service.annotations | indent 4 }}
  {{- end }}
spec:
  type: {{ default "ClusterIP" .Values.haproxy.service.type }}
  {{- if and (eq .Values.haproxy.service.type "LoadBalancer") .Values.haproxy.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.haproxy.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.haproxy.service.type "LoadBalancer") .Values.haproxy.service.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.haproxy.service.externalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.haproxy.service.type "LoadBalancer") .Values.haproxy.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{ toYaml .Values.haproxy.service.loadBalancerSourceRanges | nindent 2 }}
  {{- end }}
  {{- if .Values.haproxy.service.externalIPs }}
  externalIPs:
    {{- range $key, $value := .Values.haproxy.service.externalIPs }}
    - {{ $value }}
    {{- end }}
  {{- end }}
  ports:
  - name: tcp-haproxy
    port: {{ .Values.haproxy.servicePort }}
    protocol: TCP
    targetPort: redis
  {{- if and (eq .Values.haproxy.service.type "NodePort") .Values.haproxy.service.nodePort }}
    nodePort: {{ .Values.haproxy.service.nodePort }}
  {{- end }}
{{- if .Values.haproxy.readOnly.enabled }}
  - name: tcp-haproxyreadonly
    port: {{ .Values.haproxy.readOnly.port }}
    protocol: TCP
    targetPort: {{ .Values.haproxy.readOnly.port }}
{{- end }}
{{- if .Values.haproxy.metrics.enabled }}
  - name: {{ .Values.haproxy.metrics.portName }}
    port: {{ .Values.haproxy.metrics.port }}
    protocol: TCP
    targetPort: metrics-port
{{- end }}
  selector:
    release: {{ .Release.Name }}
    app: {{ template "redis-ha.name" . }}-haproxy
{{- end }}
