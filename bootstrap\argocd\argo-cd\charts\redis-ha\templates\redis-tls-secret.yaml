{{- if and .Values.redis.tlsPort (not .Values.tls.secretName) -}}
apiVersion: v1
kind: Secret
metadata:
  metadata:
  name: {{ template "redis-ha.fullname" . }}-tls-secret
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
type: Opaque
data:
  {{- if .Values.tls.caCertFile }}
  {{ .Values.tls.caCertFile }}: {{ .Files.Get "certs/ca.crt" | b64enc }}
  {{- end }}
  {{- if .Values.tls.certFile }}
  {{ .Values.tls.certFile }}: {{ .Files.Get "certs/redis.crt" | b64enc }}
  {{- end }}
  {{- if .Values.tls.keyFile }}
  {{ .Values.tls.keyFile }}: {{ .Files.Get "certs/redis.key" | b64enc }}
  {{- end }}
  {{- if .Values.tls.dhParamsFile }}
  {{ .Values.tls.dhParamsFile }}: {{ .Files.Get "certs/redis.dh" | b64enc }}
  {{- end }}
{{- end }}
