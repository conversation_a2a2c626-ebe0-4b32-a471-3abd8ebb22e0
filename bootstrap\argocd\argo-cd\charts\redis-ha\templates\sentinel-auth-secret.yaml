{{- if and .Values.sentinel.auth (not .Values.sentinel.existingSecret) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "redis-ha.fullname" . }}-sentinel
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
type: Opaque
data:
  {{ .Values.sentinel.authKey }}: {{ .Values.sentinel.password | b64enc | quote }}
{{- end -}}