{{- if .Values.haproxy.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ template "redis-ha.fullname" . }}-service-test
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  nodeSelector:
{{ toYaml .Values.nodeSelector | indent 4 }}
  tolerations:
{{ toYaml .Values.tolerations | indent 4 }}
  containers:
  - name: "{{ .Release.Name }}-service-test"
    image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
    command:
      - sh
      - -c
      - redis-cli -h {{ template "redis-ha.fullname" . }}-haproxy -p {{ .Values.redis.port }} info server
    resources: {{ toYaml .Values.haproxy.tests.resources | nindent 6 }}
    securityContext: {{- include "compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 6 }}
  {{- if .Values.imagePullSecrets }}
  imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 4 }}
  {{- end }}
  restartPolicy: Never
{{- end }}
