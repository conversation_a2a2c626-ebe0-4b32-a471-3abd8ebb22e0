{{- if .Values.notifications.logLevel }}
DEPRECATED option notifications.logLevel - Use `configs.params."notificationscontroller.log.level"`
{{- end }}
{{- if .Values.notifications.logFormat }}
DEPRECATED option notifications.logFormat - Use `configs.params."notificationscontroller.log.format"`
{{- end }}
{{- if .Values.dex.logLevel }}
DEPRECATED option dex.logLevel - Use `configs.params."dexserver.log.level"`
{{- end }}
{{- if .Values.dex.logFormat }}
DEPRECATED option dex.logFormat - Use `configs.params."dexserver.log.format"`
{{- end }}
In order to access the server UI you have the following options:

{{ $rootpath := default "" (index .Values "configs" "params" "server.rootpath") -}}
1. kubectl port-forward service/{{ include "argo-cd.fullname" . }}-server -n {{ include  "argo-cd.namespace" . }} 8080:443
{{ if $rootpath }}
    and then open the browser on http://localhost:8080/{{ $rootpath }} and accept the certificate
{{ else }}
    and then open the browser on http://localhost:8080 and accept the certificate
{{ end }}
2. enable ingress in the values file `server.ingress.enabled` and either
      - Add the annotation for ssl passthrough: https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#option-1-ssl-passthrough
      - Set the `configs.params."server.insecure"` in the values file and terminate SSL at your ingress: https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#option-2-multiple-ingress-objects-and-hosts


{{ if eq (toString (index .Values.configs.cm "admin.enabled")) "true" -}}
After reaching the UI the first time you can login with username: admin and the random password generated during the installation. You can find the password by running:

kubectl -n {{ include  "argo-cd.namespace" . }} get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

(You should delete the initial secret afterwards as suggested by the Getting Started Guide: https://argo-cd.readthedocs.io/en/stable/getting_started/#4-login-using-the-cli)
{{ else if or (index .Values.configs.cm "dex.config") (index .Values.configs.cm "oidc.config") -}}
After reaching the UI the first time you can login using Dex or OIDC.
{{ else -}}
After reaching the UI the first time you cannot login with username and password since you've disabled it. You should enable admin back or configure Dex via `configs.cm.dex.config` or OIDC via `configs.cm.oidc.config`.
{{ end -}}
