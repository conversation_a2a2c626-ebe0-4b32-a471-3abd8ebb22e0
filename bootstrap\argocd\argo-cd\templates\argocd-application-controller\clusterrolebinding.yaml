{{- if .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "argo-cd.controller.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "argo-cd.controller.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.controller.serviceAccountName" . }}
  namespace: {{ include "argo-cd.namespace" . }}
{{- end }}
