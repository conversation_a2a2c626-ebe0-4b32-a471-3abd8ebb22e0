{{- if .Values.applicationSet.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "argo-cd.applicationSet.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
    {{- with .Values.applicationSet.ingress.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.applicationSet.ingress.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.applicationSet.ingress.ingressClassName }}
  ingressClassName: {{ . }}
  {{- end }}
  rules:
    - host: {{ .Values.applicationSet.ingress.hostname | default .Values.global.domain }}
      http:
        paths:
          {{- with .Values.applicationSet.ingress.extraPaths }}
            {{- tpl (toYaml .) $ | nindent 10 }}
          {{- end }}
          - path: {{ .Values.applicationSet.ingress.path }}
            pathType: {{ .Values.applicationSet.ingress.pathType }}
            backend:
              service:
                name: {{ include "argo-cd.applicationSet.fullname" . }}
                port:
                  number: {{ .Values.applicationSet.service.port }}
    {{- range .Values.applicationSet.ingress.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default $.Values.applicationSet.ingress.path .path }}
            pathType: {{ default $.Values.applicationSet.ingress.pathType .pathType }}
            backend:
              service:
                name: {{ include "argo-cd.applicationSet.fullname" $ }}
                port:
                  number: {{ $.Values.applicationSet.service.port }}
    {{- end }}
    {{- with .Values.applicationSet.ingress.extraRules }}
      {{- tpl (toYaml .) $ | nindent 4 }}
    {{- end }}
  {{- if or .Values.applicationSet.ingress.tls .Values.applicationSet.ingress.extraTls }}
  tls:
    {{- if .Values.applicationSet.ingress.tls }}
    - hosts:
      - {{ .Values.applicationSet.ingress.hostname | default .Values.global.domain }}
      secretName: argocd-applicationset-controller-tls
    {{- end }}
    {{- with .Values.applicationSet.ingress.extraTls }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
