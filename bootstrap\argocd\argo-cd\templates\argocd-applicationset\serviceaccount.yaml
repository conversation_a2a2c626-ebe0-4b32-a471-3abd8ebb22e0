{{- if .Values.applicationSet.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.applicationSet.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.applicationSet.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.applicationSet.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
    {{- with .Values.applicationSet.serviceAccount.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
