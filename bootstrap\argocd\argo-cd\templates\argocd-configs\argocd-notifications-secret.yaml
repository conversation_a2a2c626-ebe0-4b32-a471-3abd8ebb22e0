{{- if and .Values.notifications.enabled .Values.notifications.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.notifications.secret.name }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
    {{- with .Values.notifications.secret.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.notifications.secret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
stringData:
  {{- with .Values.notifications.secret.items }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
