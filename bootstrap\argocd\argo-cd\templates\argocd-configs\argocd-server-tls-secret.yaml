{{- if and .Values.server.certificateSecret.enabled (not .Values.server.certificate.enabled) }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-server-tls
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "server-tls") | nindent 4 }}
    {{- with .Values.server.certificateSecret.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.certificateSecret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ .Values.server.certificateSecret.crt | b64enc | quote }}
  tls.key: {{ .Values.server.certificateSecret.key | b64enc | quote }}
{{- end }}
