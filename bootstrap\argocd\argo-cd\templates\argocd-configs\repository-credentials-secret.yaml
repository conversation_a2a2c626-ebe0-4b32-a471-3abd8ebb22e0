{{- range $repo_cred_key, $repo_cred_value := .Values.configs.credentialTemplates }}
---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-repo-creds-{{ $repo_cred_key }}
  namespace: {{ include  "argo-cd.namespace" $ | quote }}
  labels:
    argocd.argoproj.io/secret-type: repo-creds
    {{- include "argo-cd.labels" (dict "context" $) | nindent 4 }}
  {{- with $.Values.configs.credentialTemplatesAnnotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
data:
  {{- range $key, $value := $repo_cred_value }}
  {{ $key }}: {{ $value | toString | b64enc }}
  {{- end }}
{{- end }}
