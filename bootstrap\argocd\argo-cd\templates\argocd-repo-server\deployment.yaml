apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.repoServer.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ template "argo-cd.repoServer.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
    {{- with (mergeOverwrite (deepCopy .Values.global.deploymentLabels) .Values.repoServer.deploymentLabels) }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- with include "argo-cd.strategy" (mergeOverwrite (deepCopy .Values.global.deploymentStrategy) .Values.repoServer.deploymentStrategy) }}
  strategy:
    {{- trim . | nindent 4 }}
  {{- end }}
  {{- if not .Values.repoServer.autoscaling.enabled }}
  replicas: {{ .Values.repoServer.replicas }}
  {{- end }}
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.repoServer.name) | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/cmd-params: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cmd-params-cm.yaml") . | sha256sum }}
        {{- if .Values.repoServer.certificateSecret.enabled }}
        checksum/repo-server-tls: {{ include (print $.Template.BasePath "/argocd-configs/argocd-repo-server-tls-secret.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.configs.cm.create }}
        checksum/cm: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cm.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.configs.cmp.create }}
        checksum/cmp-cm: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cmp-cm.yaml") . | sha256sum }}
        {{- end }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.repoServer.podAnnotations) }}
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- end }}
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.repoServer.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.repoServer.runtimeClassName | default .Values.global.runtimeClassName }}
      runtimeClassName: {{ . }}
      {{- end }}
      {{- with .Values.repoServer.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.repoServer.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- if .Values.repoServer.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.repoServer.terminationGracePeriodSeconds }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.repoServer.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.repoServer.automountServiceAccountToken }}
      containers:
      - name: {{ .Values.repoServer.name }}
        image: {{ default .Values.global.image.repository .Values.repoServer.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.repoServer.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.repoServer.image.imagePullPolicy }}
        args:
        - /usr/local/bin/argocd-repo-server
        - --port={{ .Values.repoServer.containerPorts.server }}
        - --metrics-port={{ .Values.repoServer.containerPorts.metrics }}
        {{- with .Values.repoServer.extraArgs }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        env:
          {{- with (concat .Values.global.env .Values.repoServer.env) }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- if .Values.openshift.enabled }}
          - name: USER_NAME
            value: argocd
          {{- end }}
          - name: ARGOCD_REPO_SERVER_NAME
            value: {{ template "argo-cd.repoServer.fullname" . }}
          - name: ARGOCD_RECONCILIATION_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: argocd-cm
                key: timeout.reconciliation
                optional: true
          - name: ARGOCD_REPO_SERVER_LOGFORMAT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.log.format
                optional: true
          - name: ARGOCD_REPO_SERVER_LOGLEVEL
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.log.level
                optional: true
          - name: ARGOCD_LOG_FORMAT_TIMESTAMP
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: log.format.timestamp
                optional: true
          - name: ARGOCD_REPO_SERVER_PARALLELISM_LIMIT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.parallelism.limit
                optional: true
          - name: ARGOCD_REPO_SERVER_LISTEN_ADDRESS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.listen.address
                optional: true
          - name: ARGOCD_REPO_SERVER_LISTEN_METRICS_ADDRESS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.metrics.listen.address
                optional: true
          - name: ARGOCD_REPO_SERVER_DISABLE_TLS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.disable.tls
                optional: true
          - name: ARGOCD_TLS_MIN_VERSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.tls.minversion
                optional: true
          - name: ARGOCD_TLS_MAX_VERSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.tls.maxversion
                optional: true
          - name: ARGOCD_TLS_CIPHERS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.tls.ciphers
                optional: true
          - name: ARGOCD_REPO_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.repo.cache.expiration
                optional: true
          - name: REDIS_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.server
                optional: true
          - name: REDIS_COMPRESSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.compression
                optional: true
          - name: REDISDB
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.db
                optional: true
          - name: REDIS_USERNAME
            valueFrom:
              secretKeyRef:
                {{- include "argo-cd.redisUsernameSecretRef" . | nindent 16 }}
          - name: REDIS_PASSWORD
            valueFrom:
              secretKeyRef:
                {{- include "argo-cd.redisPasswordSecretRef" . | nindent 16 }}
          - name: REDIS_SENTINEL_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-sentinel-username
                optional: true
          - name: REDIS_SENTINEL_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-sentinel-password
                optional: true
          - name: ARGOCD_DEFAULT_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.default.cache.expiration
                optional: true
          - name: ARGOCD_REPO_SERVER_OTLP_ADDRESS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.address
                optional: true
          - name: ARGOCD_REPO_SERVER_OTLP_INSECURE
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.insecure
                optional: true
          - name: ARGOCD_REPO_SERVER_OTLP_HEADERS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.headers
                optional: true
          - name: ARGOCD_REPO_SERVER_OTLP_ATTRS
            valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: otlp.attrs
                  optional: true
          - name: ARGOCD_REPO_SERVER_MAX_COMBINED_DIRECTORY_MANIFESTS_SIZE
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.max.combined.directory.manifests.size
                optional: true
          - name: ARGOCD_REPO_SERVER_PLUGIN_TAR_EXCLUSIONS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.plugin.tar.exclusions
                optional: true
          - name: ARGOCD_REPO_SERVER_PLUGIN_USE_MANIFEST_GENERATE_PATHS
            valueFrom:
              configMapKeyRef:
                key: reposerver.plugin.use.manifest.generate.paths
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_ALLOW_OUT_OF_BOUNDS_SYMLINKS
            valueFrom:
              configMapKeyRef:
                key: reposerver.allow.oob.symlinks
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_STREAMED_MANIFEST_MAX_TAR_SIZE
            valueFrom:
              configMapKeyRef:
                key: reposerver.streamed.manifest.max.tar.size
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_STREAMED_MANIFEST_MAX_EXTRACTED_SIZE
            valueFrom:
              configMapKeyRef:
                key: reposerver.streamed.manifest.max.extracted.size
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_HELM_MANIFEST_MAX_EXTRACTED_SIZE
            valueFrom:
              configMapKeyRef:
                key: reposerver.helm.manifest.max.extracted.size
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_DISABLE_HELM_MANIFEST_MAX_EXTRACTED_SIZE
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: reposerver.disable.helm.manifest.max.extracted.size
                optional: true
          - name: ARGOCD_GIT_MODULES_ENABLED
            valueFrom:
              configMapKeyRef:
                key: reposerver.enable.git.submodule
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_GIT_LS_REMOTE_PARALLELISM_LIMIT
            valueFrom:
              configMapKeyRef:
                key: reposerver.git.lsremote.parallelism.limit
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_GIT_REQUEST_TIMEOUT
            valueFrom:
              configMapKeyRef:
                key: reposerver.git.request.timeout
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_OCI_MANIFEST_MAX_EXTRACTED_SIZE
            valueFrom:
              configMapKeyRef:
                key: reposerver.oci.manifest.max.extracted.size
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_DISABLE_OCI_MANIFEST_MAX_EXTRACTED_SIZE
            valueFrom:
              configMapKeyRef:
                key: reposerver.disable.oci.manifest.max.extracted.size
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_OCI_LAYER_MEDIA_TYPES
            valueFrom:
              configMapKeyRef:
                key: reposerver.oci.layer.media.types
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REVISION_CACHE_LOCK_TIMEOUT
            valueFrom:
              configMapKeyRef:
                key: reposerver.revision.cache.lock.timeout
                name: argocd-cmd-params-cm
                optional: true
          - name: ARGOCD_REPO_SERVER_INCLUDE_HIDDEN_DIRECTORIES
            valueFrom:
              configMapKeyRef:
                key: reposerver.include.hidden.directories
                name: argocd-cmd-params-cm
                optional: true
          {{- if .Values.repoServer.useEphemeralHelmWorkingDir }}
          - name: HELM_CACHE_HOME
            value: /helm-working-dir
          - name: HELM_CONFIG_HOME
            value: /helm-working-dir
          - name: HELM_DATA_HOME
            value: /helm-working-dir
          {{- end }}
        {{- with .Values.repoServer.envFrom }}
        envFrom:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        volumeMounts:
        {{- if .Values.repoServer.volumeMounts }}
          {{- toYaml .Values.repoServer.volumeMounts | nindent 8 }}
        {{- end }}
        - mountPath: /app/config/ssh
          name: ssh-known-hosts
        - mountPath: /app/config/tls
          name: tls-certs
        - mountPath: /app/config/gpg/source
          name: gpg-keys
        - mountPath: /app/config/gpg/keys
          name: gpg-keyring
        - mountPath: /app/config/reposerver/tls
          name: argocd-repo-server-tls
        {{- if .Values.repoServer.useEphemeralHelmWorkingDir }}
        - mountPath: /helm-working-dir
          name: helm-working-dir
        {{- end }}
        - mountPath: /home/<USER>/cmp-server/plugins
          name: plugins
        - mountPath: /tmp
          name: tmp
        ports:
        - name: repo-server
          containerPort: {{ .Values.repoServer.containerPorts.server }}
          protocol: TCP
        - name: metrics
          containerPort: {{ .Values.repoServer.containerPorts.metrics }}
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz?full=true
            port: metrics
          initialDelaySeconds: {{ .Values.repoServer.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.repoServer.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.repoServer.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.repoServer.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.repoServer.livenessProbe.failureThreshold }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: metrics
          initialDelaySeconds: {{ .Values.repoServer.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.repoServer.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.repoServer.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.repoServer.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.repoServer.readinessProbe.failureThreshold }}
        resources:
          {{- toYaml .Values.repoServer.resources | nindent 10 }}
        {{- with .Values.repoServer.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.repoServer.lifecycle }}
        lifecycle:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.repoServer.extraContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      initContainers:
      - command:
        - /bin/cp
        - -n
        - /usr/local/bin/argocd
        - /var/run/argocd/argocd-cmp-server
        image: {{ default .Values.global.image.repository .Values.repoServer.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.repoServer.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.repoServer.image.imagePullPolicy }}
        name: copyutil
        resources:
          {{- toYaml .Values.repoServer.resources | nindent 10 }}
        {{- with .Values.repoServer.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        volumeMounts:
        - mountPath: /var/run/argocd
          name: var-files
      {{- with .Values.repoServer.initContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.repoServer) }}
      affinity:
        {{- trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.repoServer.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.repoServer.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.repoServer.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-cd.selectorLabels" (dict "context" $ "name" $.Values.repoServer.name) | nindent 12 }}
        {{- end }}
        {{- end }}
      {{- end }}
      volumes:
      {{- with .Values.repoServer.volumes }}
        {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- if .Values.repoServer.useEphemeralHelmWorkingDir }}
      - name: helm-working-dir
        {{- if .Values.repoServer.existingVolumes.helmWorkingDir -}}
        {{ toYaml .Values.repoServer.existingVolumes.helmWorkingDir | nindent 8  }}
        {{- else }}
          {{- if .Values.repoServer.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.repoServer.emptyDir.sizeLimit }}
          {{- else }}
        emptyDir: {}
          {{- end }}
        {{- end }}
      {{- end }}
      - name: plugins
        {{- if .Values.repoServer.existingVolumes.plugins -}}
        {{ toYaml .Values.repoServer.existingVolumes.plugins | nindent 8  }}
        {{- else }}
          {{- if .Values.repoServer.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.repoServer.emptyDir.sizeLimit }}
          {{- else }}
        emptyDir: {}
          {{- end }}
        {{- end }}
      - name: var-files
        {{- if .Values.repoServer.existingVolumes.varFiles -}}
        {{ toYaml .Values.repoServer.existingVolumes.varFiles | nindent 8  }}
        {{- else }}
          {{- if .Values.repoServer.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.repoServer.emptyDir.sizeLimit }}
          {{- else }}
        emptyDir: {}
          {{- end }}
        {{- end }}
      - name: tmp
        {{- if .Values.repoServer.existingVolumes.tmp -}}
        {{ toYaml .Values.repoServer.existingVolumes.tmp | nindent 8  }}
        {{- else }}
          {{- if .Values.repoServer.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.repoServer.emptyDir.sizeLimit }}
          {{- else }}
        emptyDir: {}
          {{- end }}
        {{- end }}
      - name: ssh-known-hosts
        configMap:
          name: argocd-ssh-known-hosts-cm
      - name: tls-certs
        configMap:
          name: argocd-tls-certs-cm
      - name: gpg-keys
        configMap:
          name: argocd-gpg-keys-cm
      - name: gpg-keyring
        {{- if .Values.repoServer.existingVolumes.gpgKeyring -}}
        {{ toYaml .Values.repoServer.existingVolumes.gpgKeyring | nindent 8  }}
        {{- else }}
          {{- if .Values.repoServer.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.repoServer.emptyDir.sizeLimit }}
          {{- else }}
        emptyDir: {}
          {{- end }}
        {{- end }}
      - name: argocd-repo-server-tls
        secret:
          secretName: argocd-repo-server-tls
          optional: true
          items:
          - key: tls.crt
            path: tls.crt
          - key: tls.key
            path: tls.key
          - key: ca.crt
            path: ca.crt
      {{- if .Values.repoServer.hostNetwork }}
      hostNetwork: {{ .Values.repoServer.hostNetwork }}
      {{- end }}
      {{- with .Values.repoServer.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.repoServer.dnsPolicy }}
