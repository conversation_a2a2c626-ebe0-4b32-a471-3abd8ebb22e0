{{- if .Values.repoServer.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}-metrics
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" (printf "%s-metrics" .Values.repoServer.name)) | nindent 4 }}
    {{- with .Values.repoServer.metrics.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .Values.repoServer.metrics.service.annotations .Values.global.addPrometheusAnnotations }}
  annotations:
    {{- if .Values.global.addPrometheusAnnotations }}
    prometheus.io/port: {{ .Values.repoServer.metrics.service.servicePort | quote }}
    prometheus.io/scrape: "true"
    {{- end }}
    {{- range $key, $value := .Values.repoServer.metrics.service.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.repoServer.metrics.service.type }}
  {{- if and .Values.repoServer.metrics.service.clusterIP (eq .Values.repoServer.metrics.service.type "ClusterIP") }}
  clusterIP: {{ .Values.repoServer.metrics.service.clusterIP }}
  {{- end }}
  {{- include "argo-cd.dualStack" . | indent 2 }}
  ports:
  - name: {{ .Values.repoServer.metrics.service.portName }}
    protocol: TCP
    port: {{ .Values.repoServer.metrics.service.servicePort }}
    targetPort: metrics
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.repoServer.name) | nindent 4 }}
{{- end }}
