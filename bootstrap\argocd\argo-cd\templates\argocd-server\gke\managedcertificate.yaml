{{- if and .Values.server.ingress.enabled (eq .Values.server.ingress.controller "gke") .Values.server.ingress.gke.managedCertificate.create }}
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: {{ include "argo-cd.server.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
spec:
  domains:
    - {{ .Values.server.ingress.hostname | default .Values.global.domain }}
    {{- with .Values.server.ingress.gke.managedCertificate.extraDomains }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
