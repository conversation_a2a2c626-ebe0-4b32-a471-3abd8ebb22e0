{{- if .Values.server.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "argo-cd.server.fullname" . }}-metrics
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" (printf "%s-metrics" .Values.server.name)) | nindent 4 }}
    {{- with .Values.server.metrics.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .Values.server.metrics.service.annotations .Values.global.addPrometheusAnnotations }}
  annotations:
    {{- if .Values.global.addPrometheusAnnotations }}
    prometheus.io/port: {{ .Values.server.metrics.service.servicePort | quote }}
    prometheus.io/scrape: "true"
    {{- end }}
    {{- range $key, $value := .Values.server.metrics.service.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.server.metrics.service.type }}
  {{- if and .Values.server.metrics.service.clusterIP (eq .Values.server.metrics.service.type "ClusterIP") }}
  clusterIP: {{ .Values.server.metrics.service.clusterIP }}
  {{- end }}
  {{- include "argo-cd.dualStack" . | indent 2 }}
  ports:
  - name: {{ .Values.server.metrics.service.portName }}
    protocol: TCP
    port: {{ .Values.server.metrics.service.servicePort }}
    targetPort: metrics
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 4 }}
{{- end }}
