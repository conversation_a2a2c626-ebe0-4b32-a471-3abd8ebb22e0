{{- if .Values.server.route.enabled -}}
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: {{ template "argo-cd.server.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
{{- with .Values.server.route.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
{{- end }}
spec:
  host: {{ .Values.server.route.hostname | default .Values.global.domain | quote }}
  to:
    kind: Service
    name: {{ template "argo-cd.server.fullname" . }}
    weight: 100
  port:
    targetPort: https
  tls:
    termination: {{ .Values.server.route.termination_type | quote }}
    insecureEdgeTerminationPolicy: {{ .Values.server.route.termination_policy | quote }}
  wildcardPolicy: None
{{- end }}
