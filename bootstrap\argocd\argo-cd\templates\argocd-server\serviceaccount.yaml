{{- if .Values.server.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.server.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.server.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.server.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.serviceAccount.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
