{{- if .Values.crds.install }}
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    {{- if .Values.crds.keep }}
    "helm.sh/resource-policy": keep
    {{- end }}
    {{- with .Values.crds.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    app.kubernetes.io/name: applications.argoproj.io
    app.kubernetes.io/part-of: argocd
    {{- with .Values.crds.additionalLabels }}
      {{- toYaml . | nindent 4}}
    {{- end }}
  name: applications.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: Application
    listKind: ApplicationList
    plural: applications
    shortNames:
    - app
    - apps
    singular: application
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.sync.status
      name: Sync Status
      type: string
    - jsonPath: .status.health.status
      name: Health Status
      type: string
    - jsonPath: .status.sync.revision
      name: Revision
      priority: 10
      type: string
    - jsonPath: .spec.project
      name: Project
      priority: 10
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Application is a definition of Application resource.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          operation:
            description: Operation contains information about a requested or running
              operation
            properties:
              info:
                description: Info is a list of informational items for this operation
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                  required:
                  - name
                  - value
                  type: object
                type: array
              initiatedBy:
                description: InitiatedBy contains information about who initiated
                  the operations
                properties:
                  automated:
                    description: Automated is set to true if operation was initiated
                      automatically by the application controller.
                    type: boolean
                  username:
                    description: Username contains the name of a user who started
                      operation
                    type: string
                type: object
              retry:
                description: Retry controls the strategy to apply if a sync fails
                properties:
                  backoff:
                    description: Backoff controls how to backoff on subsequent retries
                      of failed syncs
                    properties:
                      duration:
                        description: Duration is the amount to back off. Default unit
                          is seconds, but could also be a duration (e.g. "2m", "1h")
                        type: string
                      factor:
                        description: Factor is a factor to multiply the base duration
                          after each failed retry
                        format: int64
                        type: integer
                      maxDuration:
                        description: MaxDuration is the maximum amount of time allowed
                          for the backoff strategy
                        type: string
                    type: object
                  limit:
                    description: Limit is the maximum number of attempts for retrying
                      a failed sync. If set to 0, no retries will be performed.
                    format: int64
                    type: integer
                type: object
              sync:
                description: Sync contains parameters for the operation
                properties:
                  autoHealAttemptsCount:
                    description: SelfHealAttemptsCount contains the number of auto-heal
                      attempts
                    format: int64
                    type: integer
                  dryRun:
                    description: DryRun specifies to perform a `kubectl apply --dry-run`
                      without actually performing the sync
                    type: boolean
                  manifests:
                    description: Manifests is an optional field that overrides sync
                      source with a local directory for development
                    items:
                      type: string
                    type: array
                  prune:
                    description: Prune specifies to delete resources from the cluster
                      that are no longer tracked in git
                    type: boolean
                  resources:
                    description: Resources describes which resources shall be part
                      of the sync
                    items:
                      description: SyncOperationResource contains resources to sync.
                      properties:
                        group:
                          type: string
                        kind:
                          type: string
                        name:
                          type: string
                        namespace:
                          type: string
                      required:
                      - kind
                      - name
                      type: object
                    type: array
                  revision:
                    description: |-
                      Revision is the revision (Git) or chart version (Helm) which to sync the application to
                      If omitted, will use the revision specified in app spec.
                    type: string
                  revisions:
                    description: |-
                      Revisions is the list of revision (Git) or chart version (Helm) which to sync each source in sources field for the application to
                      If omitted, will use the revision specified in app spec.
                    items:
                      type: string
                    type: array
                  source:
                    description: |-
                      Source overrides the source definition set in the application.
                      This is typically set in a Rollback operation and is nil during a Sync operation
                    properties:
                      chart:
                        description: Chart is a Helm chart name, and must be specified
                          for applications sourced from a Helm repo.
                        type: string
                      directory:
                        description: Directory holds path/directory specific options
                        properties:
                          exclude:
                            description: Exclude contains a glob pattern to match
                              paths against that should be explicitly excluded from
                              being used during manifest generation
                            type: string
                          include:
                            description: Include contains a glob pattern to match
                              paths against that should be explicitly included during
                              manifest generation
                            type: string
                          jsonnet:
                            description: Jsonnet holds options specific to Jsonnet
                            properties:
                              extVars:
                                description: ExtVars is a list of Jsonnet External
                                  Variables
                                items:
                                  description: JsonnetVar represents a variable to
                                    be passed to jsonnet during manifest generation
                                  properties:
                                    code:
                                      type: boolean
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              libs:
                                description: Additional library search dirs
                                items:
                                  type: string
                                type: array
                              tlas:
                                description: TLAS is a list of Jsonnet Top-level Arguments
                                items:
                                  description: JsonnetVar represents a variable to
                                    be passed to jsonnet during manifest generation
                                  properties:
                                    code:
                                      type: boolean
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                            type: object
                          recurse:
                            description: Recurse specifies whether to scan a directory
                              recursively for manifests
                            type: boolean
                        type: object
                      helm:
                        description: Helm holds helm specific options
                        properties:
                          apiVersions:
                            description: |-
                              APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                              Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                            items:
                              type: string
                            type: array
                          fileParameters:
                            description: FileParameters are file parameters to the
                              helm template
                            items:
                              description: HelmFileParameter is a file parameter that's
                                passed to helm template during manifest generation
                              properties:
                                name:
                                  description: Name is the name of the Helm parameter
                                  type: string
                                path:
                                  description: Path is the path to the file containing
                                    the values for the Helm parameter
                                  type: string
                              type: object
                            type: array
                          ignoreMissingValueFiles:
                            description: IgnoreMissingValueFiles prevents helm template
                              from failing when valueFiles do not exist locally by
                              not appending them to helm template --values
                            type: boolean
                          kubeVersion:
                            description: |-
                              KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                              uses the Kubernetes version of the target cluster.
                            type: string
                          namespace:
                            description: Namespace is an optional namespace to template
                              with. If left empty, defaults to the app's destination
                              namespace.
                            type: string
                          parameters:
                            description: Parameters is a list of Helm parameters which
                              are passed to the helm template command upon manifest
                              generation
                            items:
                              description: HelmParameter is a parameter that's passed
                                to helm template during manifest generation
                              properties:
                                forceString:
                                  description: ForceString determines whether to tell
                                    Helm to interpret booleans and numbers as strings
                                  type: boolean
                                name:
                                  description: Name is the name of the Helm parameter
                                  type: string
                                value:
                                  description: Value is the value for the Helm parameter
                                  type: string
                              type: object
                            type: array
                          passCredentials:
                            description: PassCredentials pass credentials to all domains
                              (Helm's --pass-credentials)
                            type: boolean
                          releaseName:
                            description: ReleaseName is the Helm release name to use.
                              If omitted it will use the application name
                            type: string
                          skipCrds:
                            description: SkipCrds skips custom resource definition
                              installation step (Helm's --skip-crds)
                            type: boolean
                          skipSchemaValidation:
                            description: SkipSchemaValidation skips JSON schema validation
                              (Helm's --skip-schema-validation)
                            type: boolean
                          skipTests:
                            description: SkipTests skips test manifest installation
                              step (Helm's --skip-tests).
                            type: boolean
                          valueFiles:
                            description: ValuesFiles is a list of Helm value files
                              to use when generating a template
                            items:
                              type: string
                            type: array
                          values:
                            description: Values specifies Helm values to be passed
                              to helm template, typically defined as a block. ValuesObject
                              takes precedence over Values, so use one or the other.
                            type: string
                          valuesObject:
                            description: ValuesObject specifies Helm values to be
                              passed to helm template, defined as a map. This takes
                              precedence over Values.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          version:
                            description: Version is the Helm version to use for templating
                              ("3")
                            type: string
                        type: object
                      kustomize:
                        description: Kustomize holds kustomize specific options
                        properties:
                          apiVersions:
                            description: |-
                              APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                              Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                            items:
                              type: string
                            type: array
                          commonAnnotations:
                            additionalProperties:
                              type: string
                            description: CommonAnnotations is a list of additional
                              annotations to add to rendered manifests
                            type: object
                          commonAnnotationsEnvsubst:
                            description: CommonAnnotationsEnvsubst specifies whether
                              to apply env variables substitution for annotation values
                            type: boolean
                          commonLabels:
                            additionalProperties:
                              type: string
                            description: CommonLabels is a list of additional labels
                              to add to rendered manifests
                            type: object
                          components:
                            description: Components specifies a list of kustomize
                              components to add to the kustomization before building
                            items:
                              type: string
                            type: array
                          forceCommonAnnotations:
                            description: ForceCommonAnnotations specifies whether
                              to force applying common annotations to resources for
                              Kustomize apps
                            type: boolean
                          forceCommonLabels:
                            description: ForceCommonLabels specifies whether to force
                              applying common labels to resources for Kustomize apps
                            type: boolean
                          ignoreMissingComponents:
                            description: IgnoreMissingComponents prevents kustomize
                              from failing when components do not exist locally by
                              not appending them to kustomization file
                            type: boolean
                          images:
                            description: Images is a list of Kustomize image override
                              specifications
                            items:
                              description: KustomizeImage represents a Kustomize image
                                definition in the format [old_image_name=]<image_name>:<image_tag>
                              type: string
                            type: array
                          kubeVersion:
                            description: |-
                              KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                              uses the Kubernetes version of the target cluster.
                            type: string
                          labelIncludeTemplates:
                            description: LabelIncludeTemplates specifies whether to
                              apply common labels to resource templates or not
                            type: boolean
                          labelWithoutSelector:
                            description: LabelWithoutSelector specifies whether to
                              apply common labels to resource selectors or not
                            type: boolean
                          namePrefix:
                            description: NamePrefix is a prefix appended to resources
                              for Kustomize apps
                            type: string
                          nameSuffix:
                            description: NameSuffix is a suffix appended to resources
                              for Kustomize apps
                            type: string
                          namespace:
                            description: Namespace sets the namespace that Kustomize
                              adds to all resources
                            type: string
                          patches:
                            description: Patches is a list of Kustomize patches
                            items:
                              properties:
                                options:
                                  additionalProperties:
                                    type: boolean
                                  type: object
                                patch:
                                  type: string
                                path:
                                  type: string
                                target:
                                  properties:
                                    annotationSelector:
                                      type: string
                                    group:
                                      type: string
                                    kind:
                                      type: string
                                    labelSelector:
                                      type: string
                                    name:
                                      type: string
                                    namespace:
                                      type: string
                                    version:
                                      type: string
                                  type: object
                              type: object
                            type: array
                          replicas:
                            description: Replicas is a list of Kustomize Replicas
                              override specifications
                            items:
                              properties:
                                count:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: Number of replicas
                                  x-kubernetes-int-or-string: true
                                name:
                                  description: Name of Deployment or StatefulSet
                                  type: string
                              required:
                              - count
                              - name
                              type: object
                            type: array
                          version:
                            description: Version controls which version of Kustomize
                              to use for rendering manifests
                            type: string
                        type: object
                      name:
                        description: Name is used to refer to a source and is displayed
                          in the UI. It is used in multi-source Applications.
                        type: string
                      path:
                        description: Path is a directory path within the Git repository,
                          and is only valid for applications sourced from Git.
                        type: string
                      plugin:
                        description: Plugin holds config management plugin specific
                          options
                        properties:
                          env:
                            description: Env is a list of environment variable entries
                            items:
                              description: EnvEntry represents an entry in the application's
                                environment
                              properties:
                                name:
                                  description: Name is the name of the variable, usually
                                    expressed in uppercase
                                  type: string
                                value:
                                  description: Value is the value of the variable
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                          name:
                            type: string
                          parameters:
                            items:
                              properties:
                                array:
                                  description: Array is the value of an array type
                                    parameter.
                                  items:
                                    type: string
                                  type: array
                                map:
                                  additionalProperties:
                                    type: string
                                  description: Map is the value of a map type parameter.
                                  type: object
                                name:
                                  description: Name is the name identifying a parameter.
                                  type: string
                                string:
                                  description: String_ is the value of a string type
                                    parameter.
                                  type: string
                              type: object
                            type: array
                        type: object
                      ref:
                        description: Ref is reference to another source within sources
                          field. This field will not be used if used with a `source`
                          tag.
                        type: string
                      repoURL:
                        description: RepoURL is the URL to the repository (Git or
                          Helm) that contains the application manifests
                        type: string
                      targetRevision:
                        description: |-
                          TargetRevision defines the revision of the source to sync the application to.
                          In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                          In case of Helm, this is a semver tag for the Chart's version.
                        type: string
                    required:
                    - repoURL
                    type: object
                  sources:
                    description: |-
                      Sources overrides the source definition set in the application.
                      This is typically set in a Rollback operation and is nil during a Sync operation
                    items:
                      description: ApplicationSource contains all required information
                        about the source of an application
                      properties:
                        chart:
                          description: Chart is a Helm chart name, and must be specified
                            for applications sourced from a Helm repo.
                          type: string
                        directory:
                          description: Directory holds path/directory specific options
                          properties:
                            exclude:
                              description: Exclude contains a glob pattern to match
                                paths against that should be explicitly excluded from
                                being used during manifest generation
                              type: string
                            include:
                              description: Include contains a glob pattern to match
                                paths against that should be explicitly included during
                                manifest generation
                              type: string
                            jsonnet:
                              description: Jsonnet holds options specific to Jsonnet
                              properties:
                                extVars:
                                  description: ExtVars is a list of Jsonnet External
                                    Variables
                                  items:
                                    description: JsonnetVar represents a variable
                                      to be passed to jsonnet during manifest generation
                                    properties:
                                      code:
                                        type: boolean
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                libs:
                                  description: Additional library search dirs
                                  items:
                                    type: string
                                  type: array
                                tlas:
                                  description: TLAS is a list of Jsonnet Top-level
                                    Arguments
                                  items:
                                    description: JsonnetVar represents a variable
                                      to be passed to jsonnet during manifest generation
                                    properties:
                                      code:
                                        type: boolean
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                              type: object
                            recurse:
                              description: Recurse specifies whether to scan a directory
                                recursively for manifests
                              type: boolean
                          type: object
                        helm:
                          description: Helm holds helm specific options
                          properties:
                            apiVersions:
                              description: |-
                                APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                              items:
                                type: string
                              type: array
                            fileParameters:
                              description: FileParameters are file parameters to the
                                helm template
                              items:
                                description: HelmFileParameter is a file parameter
                                  that's passed to helm template during manifest generation
                                properties:
                                  name:
                                    description: Name is the name of the Helm parameter
                                    type: string
                                  path:
                                    description: Path is the path to the file containing
                                      the values for the Helm parameter
                                    type: string
                                type: object
                              type: array
                            ignoreMissingValueFiles:
                              description: IgnoreMissingValueFiles prevents helm template
                                from failing when valueFiles do not exist locally
                                by not appending them to helm template --values
                              type: boolean
                            kubeVersion:
                              description: |-
                                KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                uses the Kubernetes version of the target cluster.
                              type: string
                            namespace:
                              description: Namespace is an optional namespace to template
                                with. If left empty, defaults to the app's destination
                                namespace.
                              type: string
                            parameters:
                              description: Parameters is a list of Helm parameters
                                which are passed to the helm template command upon
                                manifest generation
                              items:
                                description: HelmParameter is a parameter that's passed
                                  to helm template during manifest generation
                                properties:
                                  forceString:
                                    description: ForceString determines whether to
                                      tell Helm to interpret booleans and numbers
                                      as strings
                                    type: boolean
                                  name:
                                    description: Name is the name of the Helm parameter
                                    type: string
                                  value:
                                    description: Value is the value for the Helm parameter
                                    type: string
                                type: object
                              type: array
                            passCredentials:
                              description: PassCredentials pass credentials to all
                                domains (Helm's --pass-credentials)
                              type: boolean
                            releaseName:
                              description: ReleaseName is the Helm release name to
                                use. If omitted it will use the application name
                              type: string
                            skipCrds:
                              description: SkipCrds skips custom resource definition
                                installation step (Helm's --skip-crds)
                              type: boolean
                            skipSchemaValidation:
                              description: SkipSchemaValidation skips JSON schema
                                validation (Helm's --skip-schema-validation)
                              type: boolean
                            skipTests:
                              description: SkipTests skips test manifest installation
                                step (Helm's --skip-tests).
                              type: boolean
                            valueFiles:
                              description: ValuesFiles is a list of Helm value files
                                to use when generating a template
                              items:
                                type: string
                              type: array
                            values:
                              description: Values specifies Helm values to be passed
                                to helm template, typically defined as a block. ValuesObject
                                takes precedence over Values, so use one or the other.
                              type: string
                            valuesObject:
                              description: ValuesObject specifies Helm values to be
                                passed to helm template, defined as a map. This takes
                                precedence over Values.
                              type: object
                              x-kubernetes-preserve-unknown-fields: true
                            version:
                              description: Version is the Helm version to use for
                                templating ("3")
                              type: string
                          type: object
                        kustomize:
                          description: Kustomize holds kustomize specific options
                          properties:
                            apiVersions:
                              description: |-
                                APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                              items:
                                type: string
                              type: array
                            commonAnnotations:
                              additionalProperties:
                                type: string
                              description: CommonAnnotations is a list of additional
                                annotations to add to rendered manifests
                              type: object
                            commonAnnotationsEnvsubst:
                              description: CommonAnnotationsEnvsubst specifies whether
                                to apply env variables substitution for annotation
                                values
                              type: boolean
                            commonLabels:
                              additionalProperties:
                                type: string
                              description: CommonLabels is a list of additional labels
                                to add to rendered manifests
                              type: object
                            components:
                              description: Components specifies a list of kustomize
                                components to add to the kustomization before building
                              items:
                                type: string
                              type: array
                            forceCommonAnnotations:
                              description: ForceCommonAnnotations specifies whether
                                to force applying common annotations to resources
                                for Kustomize apps
                              type: boolean
                            forceCommonLabels:
                              description: ForceCommonLabels specifies whether to
                                force applying common labels to resources for Kustomize
                                apps
                              type: boolean
                            ignoreMissingComponents:
                              description: IgnoreMissingComponents prevents kustomize
                                from failing when components do not exist locally
                                by not appending them to kustomization file
                              type: boolean
                            images:
                              description: Images is a list of Kustomize image override
                                specifications
                              items:
                                description: KustomizeImage represents a Kustomize
                                  image definition in the format [old_image_name=]<image_name>:<image_tag>
                                type: string
                              type: array
                            kubeVersion:
                              description: |-
                                KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                uses the Kubernetes version of the target cluster.
                              type: string
                            labelIncludeTemplates:
                              description: LabelIncludeTemplates specifies whether
                                to apply common labels to resource templates or not
                              type: boolean
                            labelWithoutSelector:
                              description: LabelWithoutSelector specifies whether
                                to apply common labels to resource selectors or not
                              type: boolean
                            namePrefix:
                              description: NamePrefix is a prefix appended to resources
                                for Kustomize apps
                              type: string
                            nameSuffix:
                              description: NameSuffix is a suffix appended to resources
                                for Kustomize apps
                              type: string
                            namespace:
                              description: Namespace sets the namespace that Kustomize
                                adds to all resources
                              type: string
                            patches:
                              description: Patches is a list of Kustomize patches
                              items:
                                properties:
                                  options:
                                    additionalProperties:
                                      type: boolean
                                    type: object
                                  patch:
                                    type: string
                                  path:
                                    type: string
                                  target:
                                    properties:
                                      annotationSelector:
                                        type: string
                                      group:
                                        type: string
                                      kind:
                                        type: string
                                      labelSelector:
                                        type: string
                                      name:
                                        type: string
                                      namespace:
                                        type: string
                                      version:
                                        type: string
                                    type: object
                                type: object
                              type: array
                            replicas:
                              description: Replicas is a list of Kustomize Replicas
                                override specifications
                              items:
                                properties:
                                  count:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Number of replicas
                                    x-kubernetes-int-or-string: true
                                  name:
                                    description: Name of Deployment or StatefulSet
                                    type: string
                                required:
                                - count
                                - name
                                type: object
                              type: array
                            version:
                              description: Version controls which version of Kustomize
                                to use for rendering manifests
                              type: string
                          type: object
                        name:
                          description: Name is used to refer to a source and is displayed
                            in the UI. It is used in multi-source Applications.
                          type: string
                        path:
                          description: Path is a directory path within the Git repository,
                            and is only valid for applications sourced from Git.
                          type: string
                        plugin:
                          description: Plugin holds config management plugin specific
                            options
                          properties:
                            env:
                              description: Env is a list of environment variable entries
                              items:
                                description: EnvEntry represents an entry in the application's
                                  environment
                                properties:
                                  name:
                                    description: Name is the name of the variable,
                                      usually expressed in uppercase
                                    type: string
                                  value:
                                    description: Value is the value of the variable
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                            name:
                              type: string
                            parameters:
                              items:
                                properties:
                                  array:
                                    description: Array is the value of an array type
                                      parameter.
                                    items:
                                      type: string
                                    type: array
                                  map:
                                    additionalProperties:
                                      type: string
                                    description: Map is the value of a map type parameter.
                                    type: object
                                  name:
                                    description: Name is the name identifying a parameter.
                                    type: string
                                  string:
                                    description: String_ is the value of a string
                                      type parameter.
                                    type: string
                                type: object
                              type: array
                          type: object
                        ref:
                          description: Ref is reference to another source within sources
                            field. This field will not be used if used with a `source`
                            tag.
                          type: string
                        repoURL:
                          description: RepoURL is the URL to the repository (Git or
                            Helm) that contains the application manifests
                          type: string
                        targetRevision:
                          description: |-
                            TargetRevision defines the revision of the source to sync the application to.
                            In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                            In case of Helm, this is a semver tag for the Chart's version.
                          type: string
                      required:
                      - repoURL
                      type: object
                    type: array
                  syncOptions:
                    description: SyncOptions provide per-sync sync-options, e.g. Validate=false
                    items:
                      type: string
                    type: array
                  syncStrategy:
                    description: SyncStrategy describes how to perform the sync
                    properties:
                      apply:
                        description: Apply will perform a `kubectl apply` to perform
                          the sync.
                        properties:
                          force:
                            description: |-
                              Force indicates whether or not to supply the --force flag to `kubectl apply`.
                              The --force flag deletes and re-create the resource, when PATCH encounters conflict and has
                              retried for 5 times.
                            type: boolean
                        type: object
                      hook:
                        description: Hook will submit any referenced resources to
                          perform the sync. This is the default strategy
                        properties:
                          force:
                            description: |-
                              Force indicates whether or not to supply the --force flag to `kubectl apply`.
                              The --force flag deletes and re-create the resource, when PATCH encounters conflict and has
                              retried for 5 times.
                            type: boolean
                        type: object
                    type: object
                type: object
            type: object
          spec:
            description: ApplicationSpec represents desired application state. Contains
              link to repository with application definition and additional parameters
              link definition revision.
            properties:
              destination:
                description: Destination is a reference to the target Kubernetes server
                  and namespace
                properties:
                  name:
                    description: Name is an alternate way of specifying the target
                      cluster by its symbolic name. This must be set if Server is
                      not set.
                    type: string
                  namespace:
                    description: |-
                      Namespace specifies the target namespace for the application's resources.
                      The namespace will only be set for namespace-scoped resources that have not set a value for .metadata.namespace
                    type: string
                  server:
                    description: Server specifies the URL of the target cluster's
                      Kubernetes control plane API. This must be set if Name is not
                      set.
                    type: string
                type: object
              ignoreDifferences:
                description: IgnoreDifferences is a list of resources and their fields
                  which should be ignored during comparison
                items:
                  description: ResourceIgnoreDifferences contains resource filter
                    and list of json paths which should be ignored during comparison
                    with live state.
                  properties:
                    group:
                      type: string
                    jqPathExpressions:
                      items:
                        type: string
                      type: array
                    jsonPointers:
                      items:
                        type: string
                      type: array
                    kind:
                      type: string
                    managedFieldsManagers:
                      description: |-
                        ManagedFieldsManagers is a list of trusted managers. Fields mutated by those managers will take precedence over the
                        desired state defined in the SCM and won't be displayed in diffs
                      items:
                        type: string
                      type: array
                    name:
                      type: string
                    namespace:
                      type: string
                  required:
                  - kind
                  type: object
                type: array
              info:
                description: Info contains a list of information (URLs, email addresses,
                  and plain text) that relates to the application
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                  required:
                  - name
                  - value
                  type: object
                type: array
              project:
                description: |-
                  Project is a reference to the project this application belongs to.
                  The empty string means that application belongs to the 'default' project.
                type: string
              revisionHistoryLimit:
                description: |-
                  RevisionHistoryLimit limits the number of items kept in the application's revision history, which is used for informational purposes as well as for rollbacks to previous versions.
                  This should only be changed in exceptional circumstances.
                  Setting to zero will store no history. This will reduce storage used.
                  Increasing will increase the space used to store the history, so we do not recommend increasing it.
                  Default is 10.
                format: int64
                type: integer
              source:
                description: Source is a reference to the location of the application's
                  manifests or chart
                properties:
                  chart:
                    description: Chart is a Helm chart name, and must be specified
                      for applications sourced from a Helm repo.
                    type: string
                  directory:
                    description: Directory holds path/directory specific options
                    properties:
                      exclude:
                        description: Exclude contains a glob pattern to match paths
                          against that should be explicitly excluded from being used
                          during manifest generation
                        type: string
                      include:
                        description: Include contains a glob pattern to match paths
                          against that should be explicitly included during manifest
                          generation
                        type: string
                      jsonnet:
                        description: Jsonnet holds options specific to Jsonnet
                        properties:
                          extVars:
                            description: ExtVars is a list of Jsonnet External Variables
                            items:
                              description: JsonnetVar represents a variable to be
                                passed to jsonnet during manifest generation
                              properties:
                                code:
                                  type: boolean
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                          libs:
                            description: Additional library search dirs
                            items:
                              type: string
                            type: array
                          tlas:
                            description: TLAS is a list of Jsonnet Top-level Arguments
                            items:
                              description: JsonnetVar represents a variable to be
                                passed to jsonnet during manifest generation
                              properties:
                                code:
                                  type: boolean
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                        type: object
                      recurse:
                        description: Recurse specifies whether to scan a directory
                          recursively for manifests
                        type: boolean
                    type: object
                  helm:
                    description: Helm holds helm specific options
                    properties:
                      apiVersions:
                        description: |-
                          APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                          Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                        items:
                          type: string
                        type: array
                      fileParameters:
                        description: FileParameters are file parameters to the helm
                          template
                        items:
                          description: HelmFileParameter is a file parameter that's
                            passed to helm template during manifest generation
                          properties:
                            name:
                              description: Name is the name of the Helm parameter
                              type: string
                            path:
                              description: Path is the path to the file containing
                                the values for the Helm parameter
                              type: string
                          type: object
                        type: array
                      ignoreMissingValueFiles:
                        description: IgnoreMissingValueFiles prevents helm template
                          from failing when valueFiles do not exist locally by not
                          appending them to helm template --values
                        type: boolean
                      kubeVersion:
                        description: |-
                          KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                          uses the Kubernetes version of the target cluster.
                        type: string
                      namespace:
                        description: Namespace is an optional namespace to template
                          with. If left empty, defaults to the app's destination namespace.
                        type: string
                      parameters:
                        description: Parameters is a list of Helm parameters which
                          are passed to the helm template command upon manifest generation
                        items:
                          description: HelmParameter is a parameter that's passed
                            to helm template during manifest generation
                          properties:
                            forceString:
                              description: ForceString determines whether to tell
                                Helm to interpret booleans and numbers as strings
                              type: boolean
                            name:
                              description: Name is the name of the Helm parameter
                              type: string
                            value:
                              description: Value is the value for the Helm parameter
                              type: string
                          type: object
                        type: array
                      passCredentials:
                        description: PassCredentials pass credentials to all domains
                          (Helm's --pass-credentials)
                        type: boolean
                      releaseName:
                        description: ReleaseName is the Helm release name to use.
                          If omitted it will use the application name
                        type: string
                      skipCrds:
                        description: SkipCrds skips custom resource definition installation
                          step (Helm's --skip-crds)
                        type: boolean
                      skipSchemaValidation:
                        description: SkipSchemaValidation skips JSON schema validation
                          (Helm's --skip-schema-validation)
                        type: boolean
                      skipTests:
                        description: SkipTests skips test manifest installation step
                          (Helm's --skip-tests).
                        type: boolean
                      valueFiles:
                        description: ValuesFiles is a list of Helm value files to
                          use when generating a template
                        items:
                          type: string
                        type: array
                      values:
                        description: Values specifies Helm values to be passed to
                          helm template, typically defined as a block. ValuesObject
                          takes precedence over Values, so use one or the other.
                        type: string
                      valuesObject:
                        description: ValuesObject specifies Helm values to be passed
                          to helm template, defined as a map. This takes precedence
                          over Values.
                        type: object
                        x-kubernetes-preserve-unknown-fields: true
                      version:
                        description: Version is the Helm version to use for templating
                          ("3")
                        type: string
                    type: object
                  kustomize:
                    description: Kustomize holds kustomize specific options
                    properties:
                      apiVersions:
                        description: |-
                          APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                          Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                        items:
                          type: string
                        type: array
                      commonAnnotations:
                        additionalProperties:
                          type: string
                        description: CommonAnnotations is a list of additional annotations
                          to add to rendered manifests
                        type: object
                      commonAnnotationsEnvsubst:
                        description: CommonAnnotationsEnvsubst specifies whether to
                          apply env variables substitution for annotation values
                        type: boolean
                      commonLabels:
                        additionalProperties:
                          type: string
                        description: CommonLabels is a list of additional labels to
                          add to rendered manifests
                        type: object
                      components:
                        description: Components specifies a list of kustomize components
                          to add to the kustomization before building
                        items:
                          type: string
                        type: array
                      forceCommonAnnotations:
                        description: ForceCommonAnnotations specifies whether to force
                          applying common annotations to resources for Kustomize apps
                        type: boolean
                      forceCommonLabels:
                        description: ForceCommonLabels specifies whether to force
                          applying common labels to resources for Kustomize apps
                        type: boolean
                      ignoreMissingComponents:
                        description: IgnoreMissingComponents prevents kustomize from
                          failing when components do not exist locally by not appending
                          them to kustomization file
                        type: boolean
                      images:
                        description: Images is a list of Kustomize image override
                          specifications
                        items:
                          description: KustomizeImage represents a Kustomize image
                            definition in the format [old_image_name=]<image_name>:<image_tag>
                          type: string
                        type: array
                      kubeVersion:
                        description: |-
                          KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                          uses the Kubernetes version of the target cluster.
                        type: string
                      labelIncludeTemplates:
                        description: LabelIncludeTemplates specifies whether to apply
                          common labels to resource templates or not
                        type: boolean
                      labelWithoutSelector:
                        description: LabelWithoutSelector specifies whether to apply
                          common labels to resource selectors or not
                        type: boolean
                      namePrefix:
                        description: NamePrefix is a prefix appended to resources
                          for Kustomize apps
                        type: string
                      nameSuffix:
                        description: NameSuffix is a suffix appended to resources
                          for Kustomize apps
                        type: string
                      namespace:
                        description: Namespace sets the namespace that Kustomize adds
                          to all resources
                        type: string
                      patches:
                        description: Patches is a list of Kustomize patches
                        items:
                          properties:
                            options:
                              additionalProperties:
                                type: boolean
                              type: object
                            patch:
                              type: string
                            path:
                              type: string
                            target:
                              properties:
                                annotationSelector:
                                  type: string
                                group:
                                  type: string
                                kind:
                                  type: string
                                labelSelector:
                                  type: string
                                name:
                                  type: string
                                namespace:
                                  type: string
                                version:
                                  type: string
                              type: object
                          type: object
                        type: array
                      replicas:
                        description: Replicas is a list of Kustomize Replicas override
                          specifications
                        items:
                          properties:
                            count:
                              anyOf:
                              - type: integer
                              - type: string
                              description: Number of replicas
                              x-kubernetes-int-or-string: true
                            name:
                              description: Name of Deployment or StatefulSet
                              type: string
                          required:
                          - count
                          - name
                          type: object
                        type: array
                      version:
                        description: Version controls which version of Kustomize to
                          use for rendering manifests
                        type: string
                    type: object
                  name:
                    description: Name is used to refer to a source and is displayed
                      in the UI. It is used in multi-source Applications.
                    type: string
                  path:
                    description: Path is a directory path within the Git repository,
                      and is only valid for applications sourced from Git.
                    type: string
                  plugin:
                    description: Plugin holds config management plugin specific options
                    properties:
                      env:
                        description: Env is a list of environment variable entries
                        items:
                          description: EnvEntry represents an entry in the application's
                            environment
                          properties:
                            name:
                              description: Name is the name of the variable, usually
                                expressed in uppercase
                              type: string
                            value:
                              description: Value is the value of the variable
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                      name:
                        type: string
                      parameters:
                        items:
                          properties:
                            array:
                              description: Array is the value of an array type parameter.
                              items:
                                type: string
                              type: array
                            map:
                              additionalProperties:
                                type: string
                              description: Map is the value of a map type parameter.
                              type: object
                            name:
                              description: Name is the name identifying a parameter.
                              type: string
                            string:
                              description: String_ is the value of a string type parameter.
                              type: string
                          type: object
                        type: array
                    type: object
                  ref:
                    description: Ref is reference to another source within sources
                      field. This field will not be used if used with a `source` tag.
                    type: string
                  repoURL:
                    description: RepoURL is the URL to the repository (Git or Helm)
                      that contains the application manifests
                    type: string
                  targetRevision:
                    description: |-
                      TargetRevision defines the revision of the source to sync the application to.
                      In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                      In case of Helm, this is a semver tag for the Chart's version.
                    type: string
                required:
                - repoURL
                type: object
              sourceHydrator:
                description: SourceHydrator provides a way to push hydrated manifests
                  back to git before syncing them to the cluster.
                properties:
                  drySource:
                    description: DrySource specifies where the dry "don't repeat yourself"
                      manifest source lives.
                    properties:
                      path:
                        description: Path is a directory path within the Git repository
                          where the manifests are located
                        type: string
                      repoURL:
                        description: RepoURL is the URL to the git repository that
                          contains the application manifests
                        type: string
                      targetRevision:
                        description: TargetRevision defines the revision of the source
                          to hydrate
                        type: string
                    required:
                    - path
                    - repoURL
                    - targetRevision
                    type: object
                  hydrateTo:
                    description: |-
                      HydrateTo specifies an optional "staging" location to push hydrated manifests to. An external system would then
                      have to move manifests to the SyncSource, e.g. by pull request.
                    properties:
                      targetBranch:
                        description: TargetBranch is the branch to which hydrated
                          manifests should be committed
                        type: string
                    required:
                    - targetBranch
                    type: object
                  syncSource:
                    description: SyncSource specifies where to sync hydrated manifests
                      from.
                    properties:
                      path:
                        description: |-
                          Path is a directory path within the git repository where hydrated manifests should be committed to and synced
                          from. If hydrateTo is set, this is just the path from which hydrated manifests will be synced.
                        type: string
                      targetBranch:
                        description: TargetBranch is the branch to which hydrated
                          manifests should be committed
                        type: string
                    required:
                    - path
                    - targetBranch
                    type: object
                required:
                - drySource
                - syncSource
                type: object
              sources:
                description: Sources is a reference to the location of the application's
                  manifests or chart
                items:
                  description: ApplicationSource contains all required information
                    about the source of an application
                  properties:
                    chart:
                      description: Chart is a Helm chart name, and must be specified
                        for applications sourced from a Helm repo.
                      type: string
                    directory:
                      description: Directory holds path/directory specific options
                      properties:
                        exclude:
                          description: Exclude contains a glob pattern to match paths
                            against that should be explicitly excluded from being
                            used during manifest generation
                          type: string
                        include:
                          description: Include contains a glob pattern to match paths
                            against that should be explicitly included during manifest
                            generation
                          type: string
                        jsonnet:
                          description: Jsonnet holds options specific to Jsonnet
                          properties:
                            extVars:
                              description: ExtVars is a list of Jsonnet External Variables
                              items:
                                description: JsonnetVar represents a variable to be
                                  passed to jsonnet during manifest generation
                                properties:
                                  code:
                                    type: boolean
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                            libs:
                              description: Additional library search dirs
                              items:
                                type: string
                              type: array
                            tlas:
                              description: TLAS is a list of Jsonnet Top-level Arguments
                              items:
                                description: JsonnetVar represents a variable to be
                                  passed to jsonnet during manifest generation
                                properties:
                                  code:
                                    type: boolean
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                          type: object
                        recurse:
                          description: Recurse specifies whether to scan a directory
                            recursively for manifests
                          type: boolean
                      type: object
                    helm:
                      description: Helm holds helm specific options
                      properties:
                        apiVersions:
                          description: |-
                            APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                            Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                          items:
                            type: string
                          type: array
                        fileParameters:
                          description: FileParameters are file parameters to the helm
                            template
                          items:
                            description: HelmFileParameter is a file parameter that's
                              passed to helm template during manifest generation
                            properties:
                              name:
                                description: Name is the name of the Helm parameter
                                type: string
                              path:
                                description: Path is the path to the file containing
                                  the values for the Helm parameter
                                type: string
                            type: object
                          type: array
                        ignoreMissingValueFiles:
                          description: IgnoreMissingValueFiles prevents helm template
                            from failing when valueFiles do not exist locally by not
                            appending them to helm template --values
                          type: boolean
                        kubeVersion:
                          description: |-
                            KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                            uses the Kubernetes version of the target cluster.
                          type: string
                        namespace:
                          description: Namespace is an optional namespace to template
                            with. If left empty, defaults to the app's destination
                            namespace.
                          type: string
                        parameters:
                          description: Parameters is a list of Helm parameters which
                            are passed to the helm template command upon manifest
                            generation
                          items:
                            description: HelmParameter is a parameter that's passed
                              to helm template during manifest generation
                            properties:
                              forceString:
                                description: ForceString determines whether to tell
                                  Helm to interpret booleans and numbers as strings
                                type: boolean
                              name:
                                description: Name is the name of the Helm parameter
                                type: string
                              value:
                                description: Value is the value for the Helm parameter
                                type: string
                            type: object
                          type: array
                        passCredentials:
                          description: PassCredentials pass credentials to all domains
                            (Helm's --pass-credentials)
                          type: boolean
                        releaseName:
                          description: ReleaseName is the Helm release name to use.
                            If omitted it will use the application name
                          type: string
                        skipCrds:
                          description: SkipCrds skips custom resource definition installation
                            step (Helm's --skip-crds)
                          type: boolean
                        skipSchemaValidation:
                          description: SkipSchemaValidation skips JSON schema validation
                            (Helm's --skip-schema-validation)
                          type: boolean
                        skipTests:
                          description: SkipTests skips test manifest installation
                            step (Helm's --skip-tests).
                          type: boolean
                        valueFiles:
                          description: ValuesFiles is a list of Helm value files to
                            use when generating a template
                          items:
                            type: string
                          type: array
                        values:
                          description: Values specifies Helm values to be passed to
                            helm template, typically defined as a block. ValuesObject
                            takes precedence over Values, so use one or the other.
                          type: string
                        valuesObject:
                          description: ValuesObject specifies Helm values to be passed
                            to helm template, defined as a map. This takes precedence
                            over Values.
                          type: object
                          x-kubernetes-preserve-unknown-fields: true
                        version:
                          description: Version is the Helm version to use for templating
                            ("3")
                          type: string
                      type: object
                    kustomize:
                      description: Kustomize holds kustomize specific options
                      properties:
                        apiVersions:
                          description: |-
                            APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                            Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                          items:
                            type: string
                          type: array
                        commonAnnotations:
                          additionalProperties:
                            type: string
                          description: CommonAnnotations is a list of additional annotations
                            to add to rendered manifests
                          type: object
                        commonAnnotationsEnvsubst:
                          description: CommonAnnotationsEnvsubst specifies whether
                            to apply env variables substitution for annotation values
                          type: boolean
                        commonLabels:
                          additionalProperties:
                            type: string
                          description: CommonLabels is a list of additional labels
                            to add to rendered manifests
                          type: object
                        components:
                          description: Components specifies a list of kustomize components
                            to add to the kustomization before building
                          items:
                            type: string
                          type: array
                        forceCommonAnnotations:
                          description: ForceCommonAnnotations specifies whether to
                            force applying common annotations to resources for Kustomize
                            apps
                          type: boolean
                        forceCommonLabels:
                          description: ForceCommonLabels specifies whether to force
                            applying common labels to resources for Kustomize apps
                          type: boolean
                        ignoreMissingComponents:
                          description: IgnoreMissingComponents prevents kustomize
                            from failing when components do not exist locally by not
                            appending them to kustomization file
                          type: boolean
                        images:
                          description: Images is a list of Kustomize image override
                            specifications
                          items:
                            description: KustomizeImage represents a Kustomize image
                              definition in the format [old_image_name=]<image_name>:<image_tag>
                            type: string
                          type: array
                        kubeVersion:
                          description: |-
                            KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                            uses the Kubernetes version of the target cluster.
                          type: string
                        labelIncludeTemplates:
                          description: LabelIncludeTemplates specifies whether to
                            apply common labels to resource templates or not
                          type: boolean
                        labelWithoutSelector:
                          description: LabelWithoutSelector specifies whether to apply
                            common labels to resource selectors or not
                          type: boolean
                        namePrefix:
                          description: NamePrefix is a prefix appended to resources
                            for Kustomize apps
                          type: string
                        nameSuffix:
                          description: NameSuffix is a suffix appended to resources
                            for Kustomize apps
                          type: string
                        namespace:
                          description: Namespace sets the namespace that Kustomize
                            adds to all resources
                          type: string
                        patches:
                          description: Patches is a list of Kustomize patches
                          items:
                            properties:
                              options:
                                additionalProperties:
                                  type: boolean
                                type: object
                              patch:
                                type: string
                              path:
                                type: string
                              target:
                                properties:
                                  annotationSelector:
                                    type: string
                                  group:
                                    type: string
                                  kind:
                                    type: string
                                  labelSelector:
                                    type: string
                                  name:
                                    type: string
                                  namespace:
                                    type: string
                                  version:
                                    type: string
                                type: object
                            type: object
                          type: array
                        replicas:
                          description: Replicas is a list of Kustomize Replicas override
                            specifications
                          items:
                            properties:
                              count:
                                anyOf:
                                - type: integer
                                - type: string
                                description: Number of replicas
                                x-kubernetes-int-or-string: true
                              name:
                                description: Name of Deployment or StatefulSet
                                type: string
                            required:
                            - count
                            - name
                            type: object
                          type: array
                        version:
                          description: Version controls which version of Kustomize
                            to use for rendering manifests
                          type: string
                      type: object
                    name:
                      description: Name is used to refer to a source and is displayed
                        in the UI. It is used in multi-source Applications.
                      type: string
                    path:
                      description: Path is a directory path within the Git repository,
                        and is only valid for applications sourced from Git.
                      type: string
                    plugin:
                      description: Plugin holds config management plugin specific
                        options
                      properties:
                        env:
                          description: Env is a list of environment variable entries
                          items:
                            description: EnvEntry represents an entry in the application's
                              environment
                            properties:
                              name:
                                description: Name is the name of the variable, usually
                                  expressed in uppercase
                                type: string
                              value:
                                description: Value is the value of the variable
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                        name:
                          type: string
                        parameters:
                          items:
                            properties:
                              array:
                                description: Array is the value of an array type parameter.
                                items:
                                  type: string
                                type: array
                              map:
                                additionalProperties:
                                  type: string
                                description: Map is the value of a map type parameter.
                                type: object
                              name:
                                description: Name is the name identifying a parameter.
                                type: string
                              string:
                                description: String_ is the value of a string type
                                  parameter.
                                type: string
                            type: object
                          type: array
                      type: object
                    ref:
                      description: Ref is reference to another source within sources
                        field. This field will not be used if used with a `source`
                        tag.
                      type: string
                    repoURL:
                      description: RepoURL is the URL to the repository (Git or Helm)
                        that contains the application manifests
                      type: string
                    targetRevision:
                      description: |-
                        TargetRevision defines the revision of the source to sync the application to.
                        In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                        In case of Helm, this is a semver tag for the Chart's version.
                      type: string
                  required:
                  - repoURL
                  type: object
                type: array
              syncPolicy:
                description: SyncPolicy controls when and how a sync will be performed
                properties:
                  automated:
                    description: Automated will keep an application synced to the
                      target revision
                    properties:
                      allowEmpty:
                        description: 'AllowEmpty allows apps have zero live resources
                          (default: false)'
                        type: boolean
                      enabled:
                        description: Enable allows apps to explicitly control automated
                          sync
                        type: boolean
                      prune:
                        description: 'Prune specifies whether to delete resources
                          from the cluster that are not found in the sources anymore
                          as part of automated sync (default: false)'
                        type: boolean
                      selfHeal:
                        description: 'SelfHeal specifies whether to revert resources
                          back to their desired state upon modification in the cluster
                          (default: false)'
                        type: boolean
                    type: object
                  managedNamespaceMetadata:
                    description: ManagedNamespaceMetadata controls metadata in the
                      given namespace (if CreateNamespace=true)
                    properties:
                      annotations:
                        additionalProperties:
                          type: string
                        type: object
                      labels:
                        additionalProperties:
                          type: string
                        type: object
                    type: object
                  retry:
                    description: Retry controls failed sync retry behavior
                    properties:
                      backoff:
                        description: Backoff controls how to backoff on subsequent
                          retries of failed syncs
                        properties:
                          duration:
                            description: Duration is the amount to back off. Default
                              unit is seconds, but could also be a duration (e.g.
                              "2m", "1h")
                            type: string
                          factor:
                            description: Factor is a factor to multiply the base duration
                              after each failed retry
                            format: int64
                            type: integer
                          maxDuration:
                            description: MaxDuration is the maximum amount of time
                              allowed for the backoff strategy
                            type: string
                        type: object
                      limit:
                        description: Limit is the maximum number of attempts for retrying
                          a failed sync. If set to 0, no retries will be performed.
                        format: int64
                        type: integer
                    type: object
                  syncOptions:
                    description: Options allow you to specify whole app sync-options
                    items:
                      type: string
                    type: array
                type: object
            required:
            - destination
            - project
            type: object
          status:
            description: ApplicationStatus contains status information for the application
            properties:
              conditions:
                description: Conditions is a list of currently observed application
                  conditions
                items:
                  description: ApplicationCondition contains details about an application
                    condition, which is usually an error or warning
                  properties:
                    lastTransitionTime:
                      description: LastTransitionTime is the time the condition was
                        last observed
                      format: date-time
                      type: string
                    message:
                      description: Message contains human-readable message indicating
                        details about condition
                      type: string
                    type:
                      description: Type is an application condition type
                      type: string
                  required:
                  - message
                  - type
                  type: object
                type: array
              controllerNamespace:
                description: ControllerNamespace indicates the namespace in which
                  the application controller is located
                type: string
              health:
                description: Health contains information about the application's current
                  health status
                properties:
                  lastTransitionTime:
                    description: LastTransitionTime is the time the HealthStatus was
                      set or updated
                    format: date-time
                    type: string
                  message:
                    description: |-
                      Message is a human-readable informational message describing the health status

                      Deprecated: this field is not used and will be removed in a future release.
                    type: string
                  status:
                    description: Status holds the status code of the application
                    type: string
                type: object
              history:
                description: History contains information about the application's
                  sync history
                items:
                  description: RevisionHistory contains history information about
                    a previous sync
                  properties:
                    deployStartedAt:
                      description: DeployStartedAt holds the time the sync operation
                        started
                      format: date-time
                      type: string
                    deployedAt:
                      description: DeployedAt holds the time the sync operation completed
                      format: date-time
                      type: string
                    id:
                      description: ID is an auto incrementing identifier of the RevisionHistory
                      format: int64
                      type: integer
                    initiatedBy:
                      description: InitiatedBy contains information about who initiated
                        the operations
                      properties:
                        automated:
                          description: Automated is set to true if operation was initiated
                            automatically by the application controller.
                          type: boolean
                        username:
                          description: Username contains the name of a user who started
                            operation
                          type: string
                      type: object
                    revision:
                      description: Revision holds the revision the sync was performed
                        against
                      type: string
                    revisions:
                      description: Revisions holds the revision of each source in
                        sources field the sync was performed against
                      items:
                        type: string
                      type: array
                    source:
                      description: Source is a reference to the application source
                        used for the sync operation
                      properties:
                        chart:
                          description: Chart is a Helm chart name, and must be specified
                            for applications sourced from a Helm repo.
                          type: string
                        directory:
                          description: Directory holds path/directory specific options
                          properties:
                            exclude:
                              description: Exclude contains a glob pattern to match
                                paths against that should be explicitly excluded from
                                being used during manifest generation
                              type: string
                            include:
                              description: Include contains a glob pattern to match
                                paths against that should be explicitly included during
                                manifest generation
                              type: string
                            jsonnet:
                              description: Jsonnet holds options specific to Jsonnet
                              properties:
                                extVars:
                                  description: ExtVars is a list of Jsonnet External
                                    Variables
                                  items:
                                    description: JsonnetVar represents a variable
                                      to be passed to jsonnet during manifest generation
                                    properties:
                                      code:
                                        type: boolean
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                libs:
                                  description: Additional library search dirs
                                  items:
                                    type: string
                                  type: array
                                tlas:
                                  description: TLAS is a list of Jsonnet Top-level
                                    Arguments
                                  items:
                                    description: JsonnetVar represents a variable
                                      to be passed to jsonnet during manifest generation
                                    properties:
                                      code:
                                        type: boolean
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                              type: object
                            recurse:
                              description: Recurse specifies whether to scan a directory
                                recursively for manifests
                              type: boolean
                          type: object
                        helm:
                          description: Helm holds helm specific options
                          properties:
                            apiVersions:
                              description: |-
                                APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                              items:
                                type: string
                              type: array
                            fileParameters:
                              description: FileParameters are file parameters to the
                                helm template
                              items:
                                description: HelmFileParameter is a file parameter
                                  that's passed to helm template during manifest generation
                                properties:
                                  name:
                                    description: Name is the name of the Helm parameter
                                    type: string
                                  path:
                                    description: Path is the path to the file containing
                                      the values for the Helm parameter
                                    type: string
                                type: object
                              type: array
                            ignoreMissingValueFiles:
                              description: IgnoreMissingValueFiles prevents helm template
                                from failing when valueFiles do not exist locally
                                by not appending them to helm template --values
                              type: boolean
                            kubeVersion:
                              description: |-
                                KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                uses the Kubernetes version of the target cluster.
                              type: string
                            namespace:
                              description: Namespace is an optional namespace to template
                                with. If left empty, defaults to the app's destination
                                namespace.
                              type: string
                            parameters:
                              description: Parameters is a list of Helm parameters
                                which are passed to the helm template command upon
                                manifest generation
                              items:
                                description: HelmParameter is a parameter that's passed
                                  to helm template during manifest generation
                                properties:
                                  forceString:
                                    description: ForceString determines whether to
                                      tell Helm to interpret booleans and numbers
                                      as strings
                                    type: boolean
                                  name:
                                    description: Name is the name of the Helm parameter
                                    type: string
                                  value:
                                    description: Value is the value for the Helm parameter
                                    type: string
                                type: object
                              type: array
                            passCredentials:
                              description: PassCredentials pass credentials to all
                                domains (Helm's --pass-credentials)
                              type: boolean
                            releaseName:
                              description: ReleaseName is the Helm release name to
                                use. If omitted it will use the application name
                              type: string
                            skipCrds:
                              description: SkipCrds skips custom resource definition
                                installation step (Helm's --skip-crds)
                              type: boolean
                            skipSchemaValidation:
                              description: SkipSchemaValidation skips JSON schema
                                validation (Helm's --skip-schema-validation)
                              type: boolean
                            skipTests:
                              description: SkipTests skips test manifest installation
                                step (Helm's --skip-tests).
                              type: boolean
                            valueFiles:
                              description: ValuesFiles is a list of Helm value files
                                to use when generating a template
                              items:
                                type: string
                              type: array
                            values:
                              description: Values specifies Helm values to be passed
                                to helm template, typically defined as a block. ValuesObject
                                takes precedence over Values, so use one or the other.
                              type: string
                            valuesObject:
                              description: ValuesObject specifies Helm values to be
                                passed to helm template, defined as a map. This takes
                                precedence over Values.
                              type: object
                              x-kubernetes-preserve-unknown-fields: true
                            version:
                              description: Version is the Helm version to use for
                                templating ("3")
                              type: string
                          type: object
                        kustomize:
                          description: Kustomize holds kustomize specific options
                          properties:
                            apiVersions:
                              description: |-
                                APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                              items:
                                type: string
                              type: array
                            commonAnnotations:
                              additionalProperties:
                                type: string
                              description: CommonAnnotations is a list of additional
                                annotations to add to rendered manifests
                              type: object
                            commonAnnotationsEnvsubst:
                              description: CommonAnnotationsEnvsubst specifies whether
                                to apply env variables substitution for annotation
                                values
                              type: boolean
                            commonLabels:
                              additionalProperties:
                                type: string
                              description: CommonLabels is a list of additional labels
                                to add to rendered manifests
                              type: object
                            components:
                              description: Components specifies a list of kustomize
                                components to add to the kustomization before building
                              items:
                                type: string
                              type: array
                            forceCommonAnnotations:
                              description: ForceCommonAnnotations specifies whether
                                to force applying common annotations to resources
                                for Kustomize apps
                              type: boolean
                            forceCommonLabels:
                              description: ForceCommonLabels specifies whether to
                                force applying common labels to resources for Kustomize
                                apps
                              type: boolean
                            ignoreMissingComponents:
                              description: IgnoreMissingComponents prevents kustomize
                                from failing when components do not exist locally
                                by not appending them to kustomization file
                              type: boolean
                            images:
                              description: Images is a list of Kustomize image override
                                specifications
                              items:
                                description: KustomizeImage represents a Kustomize
                                  image definition in the format [old_image_name=]<image_name>:<image_tag>
                                type: string
                              type: array
                            kubeVersion:
                              description: |-
                                KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                uses the Kubernetes version of the target cluster.
                              type: string
                            labelIncludeTemplates:
                              description: LabelIncludeTemplates specifies whether
                                to apply common labels to resource templates or not
                              type: boolean
                            labelWithoutSelector:
                              description: LabelWithoutSelector specifies whether
                                to apply common labels to resource selectors or not
                              type: boolean
                            namePrefix:
                              description: NamePrefix is a prefix appended to resources
                                for Kustomize apps
                              type: string
                            nameSuffix:
                              description: NameSuffix is a suffix appended to resources
                                for Kustomize apps
                              type: string
                            namespace:
                              description: Namespace sets the namespace that Kustomize
                                adds to all resources
                              type: string
                            patches:
                              description: Patches is a list of Kustomize patches
                              items:
                                properties:
                                  options:
                                    additionalProperties:
                                      type: boolean
                                    type: object
                                  patch:
                                    type: string
                                  path:
                                    type: string
                                  target:
                                    properties:
                                      annotationSelector:
                                        type: string
                                      group:
                                        type: string
                                      kind:
                                        type: string
                                      labelSelector:
                                        type: string
                                      name:
                                        type: string
                                      namespace:
                                        type: string
                                      version:
                                        type: string
                                    type: object
                                type: object
                              type: array
                            replicas:
                              description: Replicas is a list of Kustomize Replicas
                                override specifications
                              items:
                                properties:
                                  count:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Number of replicas
                                    x-kubernetes-int-or-string: true
                                  name:
                                    description: Name of Deployment or StatefulSet
                                    type: string
                                required:
                                - count
                                - name
                                type: object
                              type: array
                            version:
                              description: Version controls which version of Kustomize
                                to use for rendering manifests
                              type: string
                          type: object
                        name:
                          description: Name is used to refer to a source and is displayed
                            in the UI. It is used in multi-source Applications.
                          type: string
                        path:
                          description: Path is a directory path within the Git repository,
                            and is only valid for applications sourced from Git.
                          type: string
                        plugin:
                          description: Plugin holds config management plugin specific
                            options
                          properties:
                            env:
                              description: Env is a list of environment variable entries
                              items:
                                description: EnvEntry represents an entry in the application's
                                  environment
                                properties:
                                  name:
                                    description: Name is the name of the variable,
                                      usually expressed in uppercase
                                    type: string
                                  value:
                                    description: Value is the value of the variable
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                            name:
                              type: string
                            parameters:
                              items:
                                properties:
                                  array:
                                    description: Array is the value of an array type
                                      parameter.
                                    items:
                                      type: string
                                    type: array
                                  map:
                                    additionalProperties:
                                      type: string
                                    description: Map is the value of a map type parameter.
                                    type: object
                                  name:
                                    description: Name is the name identifying a parameter.
                                    type: string
                                  string:
                                    description: String_ is the value of a string
                                      type parameter.
                                    type: string
                                type: object
                              type: array
                          type: object
                        ref:
                          description: Ref is reference to another source within sources
                            field. This field will not be used if used with a `source`
                            tag.
                          type: string
                        repoURL:
                          description: RepoURL is the URL to the repository (Git or
                            Helm) that contains the application manifests
                          type: string
                        targetRevision:
                          description: |-
                            TargetRevision defines the revision of the source to sync the application to.
                            In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                            In case of Helm, this is a semver tag for the Chart's version.
                          type: string
                      required:
                      - repoURL
                      type: object
                    sources:
                      description: Sources is a reference to the application sources
                        used for the sync operation
                      items:
                        description: ApplicationSource contains all required information
                          about the source of an application
                        properties:
                          chart:
                            description: Chart is a Helm chart name, and must be specified
                              for applications sourced from a Helm repo.
                            type: string
                          directory:
                            description: Directory holds path/directory specific options
                            properties:
                              exclude:
                                description: Exclude contains a glob pattern to match
                                  paths against that should be explicitly excluded
                                  from being used during manifest generation
                                type: string
                              include:
                                description: Include contains a glob pattern to match
                                  paths against that should be explicitly included
                                  during manifest generation
                                type: string
                              jsonnet:
                                description: Jsonnet holds options specific to Jsonnet
                                properties:
                                  extVars:
                                    description: ExtVars is a list of Jsonnet External
                                      Variables
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                  libs:
                                    description: Additional library search dirs
                                    items:
                                      type: string
                                    type: array
                                  tlas:
                                    description: TLAS is a list of Jsonnet Top-level
                                      Arguments
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                type: object
                              recurse:
                                description: Recurse specifies whether to scan a directory
                                  recursively for manifests
                                type: boolean
                            type: object
                          helm:
                            description: Helm holds helm specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              fileParameters:
                                description: FileParameters are file parameters to
                                  the helm template
                                items:
                                  description: HelmFileParameter is a file parameter
                                    that's passed to helm template during manifest
                                    generation
                                  properties:
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    path:
                                      description: Path is the path to the file containing
                                        the values for the Helm parameter
                                      type: string
                                  type: object
                                type: array
                              ignoreMissingValueFiles:
                                description: IgnoreMissingValueFiles prevents helm
                                  template from failing when valueFiles do not exist
                                  locally by not appending them to helm template --values
                                type: boolean
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              namespace:
                                description: Namespace is an optional namespace to
                                  template with. If left empty, defaults to the app's
                                  destination namespace.
                                type: string
                              parameters:
                                description: Parameters is a list of Helm parameters
                                  which are passed to the helm template command upon
                                  manifest generation
                                items:
                                  description: HelmParameter is a parameter that's
                                    passed to helm template during manifest generation
                                  properties:
                                    forceString:
                                      description: ForceString determines whether
                                        to tell Helm to interpret booleans and numbers
                                        as strings
                                      type: boolean
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    value:
                                      description: Value is the value for the Helm
                                        parameter
                                      type: string
                                  type: object
                                type: array
                              passCredentials:
                                description: PassCredentials pass credentials to all
                                  domains (Helm's --pass-credentials)
                                type: boolean
                              releaseName:
                                description: ReleaseName is the Helm release name
                                  to use. If omitted it will use the application name
                                type: string
                              skipCrds:
                                description: SkipCrds skips custom resource definition
                                  installation step (Helm's --skip-crds)
                                type: boolean
                              skipSchemaValidation:
                                description: SkipSchemaValidation skips JSON schema
                                  validation (Helm's --skip-schema-validation)
                                type: boolean
                              skipTests:
                                description: SkipTests skips test manifest installation
                                  step (Helm's --skip-tests).
                                type: boolean
                              valueFiles:
                                description: ValuesFiles is a list of Helm value files
                                  to use when generating a template
                                items:
                                  type: string
                                type: array
                              values:
                                description: Values specifies Helm values to be passed
                                  to helm template, typically defined as a block.
                                  ValuesObject takes precedence over Values, so use
                                  one or the other.
                                type: string
                              valuesObject:
                                description: ValuesObject specifies Helm values to
                                  be passed to helm template, defined as a map. This
                                  takes precedence over Values.
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              version:
                                description: Version is the Helm version to use for
                                  templating ("3")
                                type: string
                            type: object
                          kustomize:
                            description: Kustomize holds kustomize specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              commonAnnotations:
                                additionalProperties:
                                  type: string
                                description: CommonAnnotations is a list of additional
                                  annotations to add to rendered manifests
                                type: object
                              commonAnnotationsEnvsubst:
                                description: CommonAnnotationsEnvsubst specifies whether
                                  to apply env variables substitution for annotation
                                  values
                                type: boolean
                              commonLabels:
                                additionalProperties:
                                  type: string
                                description: CommonLabels is a list of additional
                                  labels to add to rendered manifests
                                type: object
                              components:
                                description: Components specifies a list of kustomize
                                  components to add to the kustomization before building
                                items:
                                  type: string
                                type: array
                              forceCommonAnnotations:
                                description: ForceCommonAnnotations specifies whether
                                  to force applying common annotations to resources
                                  for Kustomize apps
                                type: boolean
                              forceCommonLabels:
                                description: ForceCommonLabels specifies whether to
                                  force applying common labels to resources for Kustomize
                                  apps
                                type: boolean
                              ignoreMissingComponents:
                                description: IgnoreMissingComponents prevents kustomize
                                  from failing when components do not exist locally
                                  by not appending them to kustomization file
                                type: boolean
                              images:
                                description: Images is a list of Kustomize image override
                                  specifications
                                items:
                                  description: KustomizeImage represents a Kustomize
                                    image definition in the format [old_image_name=]<image_name>:<image_tag>
                                  type: string
                                type: array
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              labelIncludeTemplates:
                                description: LabelIncludeTemplates specifies whether
                                  to apply common labels to resource templates or
                                  not
                                type: boolean
                              labelWithoutSelector:
                                description: LabelWithoutSelector specifies whether
                                  to apply common labels to resource selectors or
                                  not
                                type: boolean
                              namePrefix:
                                description: NamePrefix is a prefix appended to resources
                                  for Kustomize apps
                                type: string
                              nameSuffix:
                                description: NameSuffix is a suffix appended to resources
                                  for Kustomize apps
                                type: string
                              namespace:
                                description: Namespace sets the namespace that Kustomize
                                  adds to all resources
                                type: string
                              patches:
                                description: Patches is a list of Kustomize patches
                                items:
                                  properties:
                                    options:
                                      additionalProperties:
                                        type: boolean
                                      type: object
                                    patch:
                                      type: string
                                    path:
                                      type: string
                                    target:
                                      properties:
                                        annotationSelector:
                                          type: string
                                        group:
                                          type: string
                                        kind:
                                          type: string
                                        labelSelector:
                                          type: string
                                        name:
                                          type: string
                                        namespace:
                                          type: string
                                        version:
                                          type: string
                                      type: object
                                  type: object
                                type: array
                              replicas:
                                description: Replicas is a list of Kustomize Replicas
                                  override specifications
                                items:
                                  properties:
                                    count:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      description: Number of replicas
                                      x-kubernetes-int-or-string: true
                                    name:
                                      description: Name of Deployment or StatefulSet
                                      type: string
                                  required:
                                  - count
                                  - name
                                  type: object
                                type: array
                              version:
                                description: Version controls which version of Kustomize
                                  to use for rendering manifests
                                type: string
                            type: object
                          name:
                            description: Name is used to refer to a source and is
                              displayed in the UI. It is used in multi-source Applications.
                            type: string
                          path:
                            description: Path is a directory path within the Git repository,
                              and is only valid for applications sourced from Git.
                            type: string
                          plugin:
                            description: Plugin holds config management plugin specific
                              options
                            properties:
                              env:
                                description: Env is a list of environment variable
                                  entries
                                items:
                                  description: EnvEntry represents an entry in the
                                    application's environment
                                  properties:
                                    name:
                                      description: Name is the name of the variable,
                                        usually expressed in uppercase
                                      type: string
                                    value:
                                      description: Value is the value of the variable
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              name:
                                type: string
                              parameters:
                                items:
                                  properties:
                                    array:
                                      description: Array is the value of an array
                                        type parameter.
                                      items:
                                        type: string
                                      type: array
                                    map:
                                      additionalProperties:
                                        type: string
                                      description: Map is the value of a map type
                                        parameter.
                                      type: object
                                    name:
                                      description: Name is the name identifying a
                                        parameter.
                                      type: string
                                    string:
                                      description: String_ is the value of a string
                                        type parameter.
                                      type: string
                                  type: object
                                type: array
                            type: object
                          ref:
                            description: Ref is reference to another source within
                              sources field. This field will not be used if used with
                              a `source` tag.
                            type: string
                          repoURL:
                            description: RepoURL is the URL to the repository (Git
                              or Helm) that contains the application manifests
                            type: string
                          targetRevision:
                            description: |-
                              TargetRevision defines the revision of the source to sync the application to.
                              In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                              In case of Helm, this is a semver tag for the Chart's version.
                            type: string
                        required:
                        - repoURL
                        type: object
                      type: array
                  required:
                  - deployedAt
                  - id
                  type: object
                type: array
              observedAt:
                description: |-
                  ObservedAt indicates when the application state was updated without querying latest git state
                  Deprecated: controller no longer updates ObservedAt field
                format: date-time
                type: string
              operationState:
                description: OperationState contains information about any ongoing
                  operations, such as a sync
                properties:
                  finishedAt:
                    description: FinishedAt contains time of operation completion
                    format: date-time
                    type: string
                  message:
                    description: Message holds any pertinent messages when attempting
                      to perform operation (typically errors).
                    type: string
                  operation:
                    description: Operation is the original requested operation
                    properties:
                      info:
                        description: Info is a list of informational items for this
                          operation
                        items:
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                      initiatedBy:
                        description: InitiatedBy contains information about who initiated
                          the operations
                        properties:
                          automated:
                            description: Automated is set to true if operation was
                              initiated automatically by the application controller.
                            type: boolean
                          username:
                            description: Username contains the name of a user who
                              started operation
                            type: string
                        type: object
                      retry:
                        description: Retry controls the strategy to apply if a sync
                          fails
                        properties:
                          backoff:
                            description: Backoff controls how to backoff on subsequent
                              retries of failed syncs
                            properties:
                              duration:
                                description: Duration is the amount to back off. Default
                                  unit is seconds, but could also be a duration (e.g.
                                  "2m", "1h")
                                type: string
                              factor:
                                description: Factor is a factor to multiply the base
                                  duration after each failed retry
                                format: int64
                                type: integer
                              maxDuration:
                                description: MaxDuration is the maximum amount of
                                  time allowed for the backoff strategy
                                type: string
                            type: object
                          limit:
                            description: Limit is the maximum number of attempts for
                              retrying a failed sync. If set to 0, no retries will
                              be performed.
                            format: int64
                            type: integer
                        type: object
                      sync:
                        description: Sync contains parameters for the operation
                        properties:
                          autoHealAttemptsCount:
                            description: SelfHealAttemptsCount contains the number
                              of auto-heal attempts
                            format: int64
                            type: integer
                          dryRun:
                            description: DryRun specifies to perform a `kubectl apply
                              --dry-run` without actually performing the sync
                            type: boolean
                          manifests:
                            description: Manifests is an optional field that overrides
                              sync source with a local directory for development
                            items:
                              type: string
                            type: array
                          prune:
                            description: Prune specifies to delete resources from
                              the cluster that are no longer tracked in git
                            type: boolean
                          resources:
                            description: Resources describes which resources shall
                              be part of the sync
                            items:
                              description: SyncOperationResource contains resources
                                to sync.
                              properties:
                                group:
                                  type: string
                                kind:
                                  type: string
                                name:
                                  type: string
                                namespace:
                                  type: string
                              required:
                              - kind
                              - name
                              type: object
                            type: array
                          revision:
                            description: |-
                              Revision is the revision (Git) or chart version (Helm) which to sync the application to
                              If omitted, will use the revision specified in app spec.
                            type: string
                          revisions:
                            description: |-
                              Revisions is the list of revision (Git) or chart version (Helm) which to sync each source in sources field for the application to
                              If omitted, will use the revision specified in app spec.
                            items:
                              type: string
                            type: array
                          source:
                            description: |-
                              Source overrides the source definition set in the application.
                              This is typically set in a Rollback operation and is nil during a Sync operation
                            properties:
                              chart:
                                description: Chart is a Helm chart name, and must
                                  be specified for applications sourced from a Helm
                                  repo.
                                type: string
                              directory:
                                description: Directory holds path/directory specific
                                  options
                                properties:
                                  exclude:
                                    description: Exclude contains a glob pattern to
                                      match paths against that should be explicitly
                                      excluded from being used during manifest generation
                                    type: string
                                  include:
                                    description: Include contains a glob pattern to
                                      match paths against that should be explicitly
                                      included during manifest generation
                                    type: string
                                  jsonnet:
                                    description: Jsonnet holds options specific to
                                      Jsonnet
                                    properties:
                                      extVars:
                                        description: ExtVars is a list of Jsonnet
                                          External Variables
                                        items:
                                          description: JsonnetVar represents a variable
                                            to be passed to jsonnet during manifest
                                            generation
                                          properties:
                                            code:
                                              type: boolean
                                            name:
                                              type: string
                                            value:
                                              type: string
                                          required:
                                          - name
                                          - value
                                          type: object
                                        type: array
                                      libs:
                                        description: Additional library search dirs
                                        items:
                                          type: string
                                        type: array
                                      tlas:
                                        description: TLAS is a list of Jsonnet Top-level
                                          Arguments
                                        items:
                                          description: JsonnetVar represents a variable
                                            to be passed to jsonnet during manifest
                                            generation
                                          properties:
                                            code:
                                              type: boolean
                                            name:
                                              type: string
                                            value:
                                              type: string
                                          required:
                                          - name
                                          - value
                                          type: object
                                        type: array
                                    type: object
                                  recurse:
                                    description: Recurse specifies whether to scan
                                      a directory recursively for manifests
                                    type: boolean
                                type: object
                              helm:
                                description: Helm holds helm specific options
                                properties:
                                  apiVersions:
                                    description: |-
                                      APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                      Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                    items:
                                      type: string
                                    type: array
                                  fileParameters:
                                    description: FileParameters are file parameters
                                      to the helm template
                                    items:
                                      description: HelmFileParameter is a file parameter
                                        that's passed to helm template during manifest
                                        generation
                                      properties:
                                        name:
                                          description: Name is the name of the Helm
                                            parameter
                                          type: string
                                        path:
                                          description: Path is the path to the file
                                            containing the values for the Helm parameter
                                          type: string
                                      type: object
                                    type: array
                                  ignoreMissingValueFiles:
                                    description: IgnoreMissingValueFiles prevents
                                      helm template from failing when valueFiles do
                                      not exist locally by not appending them to helm
                                      template --values
                                    type: boolean
                                  kubeVersion:
                                    description: |-
                                      KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                      uses the Kubernetes version of the target cluster.
                                    type: string
                                  namespace:
                                    description: Namespace is an optional namespace
                                      to template with. If left empty, defaults to
                                      the app's destination namespace.
                                    type: string
                                  parameters:
                                    description: Parameters is a list of Helm parameters
                                      which are passed to the helm template command
                                      upon manifest generation
                                    items:
                                      description: HelmParameter is a parameter that's
                                        passed to helm template during manifest generation
                                      properties:
                                        forceString:
                                          description: ForceString determines whether
                                            to tell Helm to interpret booleans and
                                            numbers as strings
                                          type: boolean
                                        name:
                                          description: Name is the name of the Helm
                                            parameter
                                          type: string
                                        value:
                                          description: Value is the value for the
                                            Helm parameter
                                          type: string
                                      type: object
                                    type: array
                                  passCredentials:
                                    description: PassCredentials pass credentials
                                      to all domains (Helm's --pass-credentials)
                                    type: boolean
                                  releaseName:
                                    description: ReleaseName is the Helm release name
                                      to use. If omitted it will use the application
                                      name
                                    type: string
                                  skipCrds:
                                    description: SkipCrds skips custom resource definition
                                      installation step (Helm's --skip-crds)
                                    type: boolean
                                  skipSchemaValidation:
                                    description: SkipSchemaValidation skips JSON schema
                                      validation (Helm's --skip-schema-validation)
                                    type: boolean
                                  skipTests:
                                    description: SkipTests skips test manifest installation
                                      step (Helm's --skip-tests).
                                    type: boolean
                                  valueFiles:
                                    description: ValuesFiles is a list of Helm value
                                      files to use when generating a template
                                    items:
                                      type: string
                                    type: array
                                  values:
                                    description: Values specifies Helm values to be
                                      passed to helm template, typically defined as
                                      a block. ValuesObject takes precedence over
                                      Values, so use one or the other.
                                    type: string
                                  valuesObject:
                                    description: ValuesObject specifies Helm values
                                      to be passed to helm template, defined as a
                                      map. This takes precedence over Values.
                                    type: object
                                    x-kubernetes-preserve-unknown-fields: true
                                  version:
                                    description: Version is the Helm version to use
                                      for templating ("3")
                                    type: string
                                type: object
                              kustomize:
                                description: Kustomize holds kustomize specific options
                                properties:
                                  apiVersions:
                                    description: |-
                                      APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                      Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                    items:
                                      type: string
                                    type: array
                                  commonAnnotations:
                                    additionalProperties:
                                      type: string
                                    description: CommonAnnotations is a list of additional
                                      annotations to add to rendered manifests
                                    type: object
                                  commonAnnotationsEnvsubst:
                                    description: CommonAnnotationsEnvsubst specifies
                                      whether to apply env variables substitution
                                      for annotation values
                                    type: boolean
                                  commonLabels:
                                    additionalProperties:
                                      type: string
                                    description: CommonLabels is a list of additional
                                      labels to add to rendered manifests
                                    type: object
                                  components:
                                    description: Components specifies a list of kustomize
                                      components to add to the kustomization before
                                      building
                                    items:
                                      type: string
                                    type: array
                                  forceCommonAnnotations:
                                    description: ForceCommonAnnotations specifies
                                      whether to force applying common annotations
                                      to resources for Kustomize apps
                                    type: boolean
                                  forceCommonLabels:
                                    description: ForceCommonLabels specifies whether
                                      to force applying common labels to resources
                                      for Kustomize apps
                                    type: boolean
                                  ignoreMissingComponents:
                                    description: IgnoreMissingComponents prevents
                                      kustomize from failing when components do not
                                      exist locally by not appending them to kustomization
                                      file
                                    type: boolean
                                  images:
                                    description: Images is a list of Kustomize image
                                      override specifications
                                    items:
                                      description: KustomizeImage represents a Kustomize
                                        image definition in the format [old_image_name=]<image_name>:<image_tag>
                                      type: string
                                    type: array
                                  kubeVersion:
                                    description: |-
                                      KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                      uses the Kubernetes version of the target cluster.
                                    type: string
                                  labelIncludeTemplates:
                                    description: LabelIncludeTemplates specifies whether
                                      to apply common labels to resource templates
                                      or not
                                    type: boolean
                                  labelWithoutSelector:
                                    description: LabelWithoutSelector specifies whether
                                      to apply common labels to resource selectors
                                      or not
                                    type: boolean
                                  namePrefix:
                                    description: NamePrefix is a prefix appended to
                                      resources for Kustomize apps
                                    type: string
                                  nameSuffix:
                                    description: NameSuffix is a suffix appended to
                                      resources for Kustomize apps
                                    type: string
                                  namespace:
                                    description: Namespace sets the namespace that
                                      Kustomize adds to all resources
                                    type: string
                                  patches:
                                    description: Patches is a list of Kustomize patches
                                    items:
                                      properties:
                                        options:
                                          additionalProperties:
                                            type: boolean
                                          type: object
                                        patch:
                                          type: string
                                        path:
                                          type: string
                                        target:
                                          properties:
                                            annotationSelector:
                                              type: string
                                            group:
                                              type: string
                                            kind:
                                              type: string
                                            labelSelector:
                                              type: string
                                            name:
                                              type: string
                                            namespace:
                                              type: string
                                            version:
                                              type: string
                                          type: object
                                      type: object
                                    type: array
                                  replicas:
                                    description: Replicas is a list of Kustomize Replicas
                                      override specifications
                                    items:
                                      properties:
                                        count:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          description: Number of replicas
                                          x-kubernetes-int-or-string: true
                                        name:
                                          description: Name of Deployment or StatefulSet
                                          type: string
                                      required:
                                      - count
                                      - name
                                      type: object
                                    type: array
                                  version:
                                    description: Version controls which version of
                                      Kustomize to use for rendering manifests
                                    type: string
                                type: object
                              name:
                                description: Name is used to refer to a source and
                                  is displayed in the UI. It is used in multi-source
                                  Applications.
                                type: string
                              path:
                                description: Path is a directory path within the Git
                                  repository, and is only valid for applications sourced
                                  from Git.
                                type: string
                              plugin:
                                description: Plugin holds config management plugin
                                  specific options
                                properties:
                                  env:
                                    description: Env is a list of environment variable
                                      entries
                                    items:
                                      description: EnvEntry represents an entry in
                                        the application's environment
                                      properties:
                                        name:
                                          description: Name is the name of the variable,
                                            usually expressed in uppercase
                                          type: string
                                        value:
                                          description: Value is the value of the variable
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                  name:
                                    type: string
                                  parameters:
                                    items:
                                      properties:
                                        array:
                                          description: Array is the value of an array
                                            type parameter.
                                          items:
                                            type: string
                                          type: array
                                        map:
                                          additionalProperties:
                                            type: string
                                          description: Map is the value of a map type
                                            parameter.
                                          type: object
                                        name:
                                          description: Name is the name identifying
                                            a parameter.
                                          type: string
                                        string:
                                          description: String_ is the value of a string
                                            type parameter.
                                          type: string
                                      type: object
                                    type: array
                                type: object
                              ref:
                                description: Ref is reference to another source within
                                  sources field. This field will not be used if used
                                  with a `source` tag.
                                type: string
                              repoURL:
                                description: RepoURL is the URL to the repository
                                  (Git or Helm) that contains the application manifests
                                type: string
                              targetRevision:
                                description: |-
                                  TargetRevision defines the revision of the source to sync the application to.
                                  In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                                  In case of Helm, this is a semver tag for the Chart's version.
                                type: string
                            required:
                            - repoURL
                            type: object
                          sources:
                            description: |-
                              Sources overrides the source definition set in the application.
                              This is typically set in a Rollback operation and is nil during a Sync operation
                            items:
                              description: ApplicationSource contains all required
                                information about the source of an application
                              properties:
                                chart:
                                  description: Chart is a Helm chart name, and must
                                    be specified for applications sourced from a Helm
                                    repo.
                                  type: string
                                directory:
                                  description: Directory holds path/directory specific
                                    options
                                  properties:
                                    exclude:
                                      description: Exclude contains a glob pattern
                                        to match paths against that should be explicitly
                                        excluded from being used during manifest generation
                                      type: string
                                    include:
                                      description: Include contains a glob pattern
                                        to match paths against that should be explicitly
                                        included during manifest generation
                                      type: string
                                    jsonnet:
                                      description: Jsonnet holds options specific
                                        to Jsonnet
                                      properties:
                                        extVars:
                                          description: ExtVars is a list of Jsonnet
                                            External Variables
                                          items:
                                            description: JsonnetVar represents a variable
                                              to be passed to jsonnet during manifest
                                              generation
                                            properties:
                                              code:
                                                type: boolean
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        libs:
                                          description: Additional library search dirs
                                          items:
                                            type: string
                                          type: array
                                        tlas:
                                          description: TLAS is a list of Jsonnet Top-level
                                            Arguments
                                          items:
                                            description: JsonnetVar represents a variable
                                              to be passed to jsonnet during manifest
                                              generation
                                            properties:
                                              code:
                                                type: boolean
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                      type: object
                                    recurse:
                                      description: Recurse specifies whether to scan
                                        a directory recursively for manifests
                                      type: boolean
                                  type: object
                                helm:
                                  description: Helm holds helm specific options
                                  properties:
                                    apiVersions:
                                      description: |-
                                        APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                        Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                      items:
                                        type: string
                                      type: array
                                    fileParameters:
                                      description: FileParameters are file parameters
                                        to the helm template
                                      items:
                                        description: HelmFileParameter is a file parameter
                                          that's passed to helm template during manifest
                                          generation
                                        properties:
                                          name:
                                            description: Name is the name of the Helm
                                              parameter
                                            type: string
                                          path:
                                            description: Path is the path to the file
                                              containing the values for the Helm parameter
                                            type: string
                                        type: object
                                      type: array
                                    ignoreMissingValueFiles:
                                      description: IgnoreMissingValueFiles prevents
                                        helm template from failing when valueFiles
                                        do not exist locally by not appending them
                                        to helm template --values
                                      type: boolean
                                    kubeVersion:
                                      description: |-
                                        KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                        uses the Kubernetes version of the target cluster.
                                      type: string
                                    namespace:
                                      description: Namespace is an optional namespace
                                        to template with. If left empty, defaults
                                        to the app's destination namespace.
                                      type: string
                                    parameters:
                                      description: Parameters is a list of Helm parameters
                                        which are passed to the helm template command
                                        upon manifest generation
                                      items:
                                        description: HelmParameter is a parameter
                                          that's passed to helm template during manifest
                                          generation
                                        properties:
                                          forceString:
                                            description: ForceString determines whether
                                              to tell Helm to interpret booleans and
                                              numbers as strings
                                            type: boolean
                                          name:
                                            description: Name is the name of the Helm
                                              parameter
                                            type: string
                                          value:
                                            description: Value is the value for the
                                              Helm parameter
                                            type: string
                                        type: object
                                      type: array
                                    passCredentials:
                                      description: PassCredentials pass credentials
                                        to all domains (Helm's --pass-credentials)
                                      type: boolean
                                    releaseName:
                                      description: ReleaseName is the Helm release
                                        name to use. If omitted it will use the application
                                        name
                                      type: string
                                    skipCrds:
                                      description: SkipCrds skips custom resource
                                        definition installation step (Helm's --skip-crds)
                                      type: boolean
                                    skipSchemaValidation:
                                      description: SkipSchemaValidation skips JSON
                                        schema validation (Helm's --skip-schema-validation)
                                      type: boolean
                                    skipTests:
                                      description: SkipTests skips test manifest installation
                                        step (Helm's --skip-tests).
                                      type: boolean
                                    valueFiles:
                                      description: ValuesFiles is a list of Helm value
                                        files to use when generating a template
                                      items:
                                        type: string
                                      type: array
                                    values:
                                      description: Values specifies Helm values to
                                        be passed to helm template, typically defined
                                        as a block. ValuesObject takes precedence
                                        over Values, so use one or the other.
                                      type: string
                                    valuesObject:
                                      description: ValuesObject specifies Helm values
                                        to be passed to helm template, defined as
                                        a map. This takes precedence over Values.
                                      type: object
                                      x-kubernetes-preserve-unknown-fields: true
                                    version:
                                      description: Version is the Helm version to
                                        use for templating ("3")
                                      type: string
                                  type: object
                                kustomize:
                                  description: Kustomize holds kustomize specific
                                    options
                                  properties:
                                    apiVersions:
                                      description: |-
                                        APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                        Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                      items:
                                        type: string
                                      type: array
                                    commonAnnotations:
                                      additionalProperties:
                                        type: string
                                      description: CommonAnnotations is a list of
                                        additional annotations to add to rendered
                                        manifests
                                      type: object
                                    commonAnnotationsEnvsubst:
                                      description: CommonAnnotationsEnvsubst specifies
                                        whether to apply env variables substitution
                                        for annotation values
                                      type: boolean
                                    commonLabels:
                                      additionalProperties:
                                        type: string
                                      description: CommonLabels is a list of additional
                                        labels to add to rendered manifests
                                      type: object
                                    components:
                                      description: Components specifies a list of
                                        kustomize components to add to the kustomization
                                        before building
                                      items:
                                        type: string
                                      type: array
                                    forceCommonAnnotations:
                                      description: ForceCommonAnnotations specifies
                                        whether to force applying common annotations
                                        to resources for Kustomize apps
                                      type: boolean
                                    forceCommonLabels:
                                      description: ForceCommonLabels specifies whether
                                        to force applying common labels to resources
                                        for Kustomize apps
                                      type: boolean
                                    ignoreMissingComponents:
                                      description: IgnoreMissingComponents prevents
                                        kustomize from failing when components do
                                        not exist locally by not appending them to
                                        kustomization file
                                      type: boolean
                                    images:
                                      description: Images is a list of Kustomize image
                                        override specifications
                                      items:
                                        description: KustomizeImage represents a Kustomize
                                          image definition in the format [old_image_name=]<image_name>:<image_tag>
                                        type: string
                                      type: array
                                    kubeVersion:
                                      description: |-
                                        KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                        uses the Kubernetes version of the target cluster.
                                      type: string
                                    labelIncludeTemplates:
                                      description: LabelIncludeTemplates specifies
                                        whether to apply common labels to resource
                                        templates or not
                                      type: boolean
                                    labelWithoutSelector:
                                      description: LabelWithoutSelector specifies
                                        whether to apply common labels to resource
                                        selectors or not
                                      type: boolean
                                    namePrefix:
                                      description: NamePrefix is a prefix appended
                                        to resources for Kustomize apps
                                      type: string
                                    nameSuffix:
                                      description: NameSuffix is a suffix appended
                                        to resources for Kustomize apps
                                      type: string
                                    namespace:
                                      description: Namespace sets the namespace that
                                        Kustomize adds to all resources
                                      type: string
                                    patches:
                                      description: Patches is a list of Kustomize
                                        patches
                                      items:
                                        properties:
                                          options:
                                            additionalProperties:
                                              type: boolean
                                            type: object
                                          patch:
                                            type: string
                                          path:
                                            type: string
                                          target:
                                            properties:
                                              annotationSelector:
                                                type: string
                                              group:
                                                type: string
                                              kind:
                                                type: string
                                              labelSelector:
                                                type: string
                                              name:
                                                type: string
                                              namespace:
                                                type: string
                                              version:
                                                type: string
                                            type: object
                                        type: object
                                      type: array
                                    replicas:
                                      description: Replicas is a list of Kustomize
                                        Replicas override specifications
                                      items:
                                        properties:
                                          count:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            description: Number of replicas
                                            x-kubernetes-int-or-string: true
                                          name:
                                            description: Name of Deployment or StatefulSet
                                            type: string
                                        required:
                                        - count
                                        - name
                                        type: object
                                      type: array
                                    version:
                                      description: Version controls which version
                                        of Kustomize to use for rendering manifests
                                      type: string
                                  type: object
                                name:
                                  description: Name is used to refer to a source and
                                    is displayed in the UI. It is used in multi-source
                                    Applications.
                                  type: string
                                path:
                                  description: Path is a directory path within the
                                    Git repository, and is only valid for applications
                                    sourced from Git.
                                  type: string
                                plugin:
                                  description: Plugin holds config management plugin
                                    specific options
                                  properties:
                                    env:
                                      description: Env is a list of environment variable
                                        entries
                                      items:
                                        description: EnvEntry represents an entry
                                          in the application's environment
                                        properties:
                                          name:
                                            description: Name is the name of the variable,
                                              usually expressed in uppercase
                                            type: string
                                          value:
                                            description: Value is the value of the
                                              variable
                                            type: string
                                        required:
                                        - name
                                        - value
                                        type: object
                                      type: array
                                    name:
                                      type: string
                                    parameters:
                                      items:
                                        properties:
                                          array:
                                            description: Array is the value of an
                                              array type parameter.
                                            items:
                                              type: string
                                            type: array
                                          map:
                                            additionalProperties:
                                              type: string
                                            description: Map is the value of a map
                                              type parameter.
                                            type: object
                                          name:
                                            description: Name is the name identifying
                                              a parameter.
                                            type: string
                                          string:
                                            description: String_ is the value of a
                                              string type parameter.
                                            type: string
                                        type: object
                                      type: array
                                  type: object
                                ref:
                                  description: Ref is reference to another source
                                    within sources field. This field will not be used
                                    if used with a `source` tag.
                                  type: string
                                repoURL:
                                  description: RepoURL is the URL to the repository
                                    (Git or Helm) that contains the application manifests
                                  type: string
                                targetRevision:
                                  description: |-
                                    TargetRevision defines the revision of the source to sync the application to.
                                    In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                                    In case of Helm, this is a semver tag for the Chart's version.
                                  type: string
                              required:
                              - repoURL
                              type: object
                            type: array
                          syncOptions:
                            description: SyncOptions provide per-sync sync-options,
                              e.g. Validate=false
                            items:
                              type: string
                            type: array
                          syncStrategy:
                            description: SyncStrategy describes how to perform the
                              sync
                            properties:
                              apply:
                                description: Apply will perform a `kubectl apply`
                                  to perform the sync.
                                properties:
                                  force:
                                    description: |-
                                      Force indicates whether or not to supply the --force flag to `kubectl apply`.
                                      The --force flag deletes and re-create the resource, when PATCH encounters conflict and has
                                      retried for 5 times.
                                    type: boolean
                                type: object
                              hook:
                                description: Hook will submit any referenced resources
                                  to perform the sync. This is the default strategy
                                properties:
                                  force:
                                    description: |-
                                      Force indicates whether or not to supply the --force flag to `kubectl apply`.
                                      The --force flag deletes and re-create the resource, when PATCH encounters conflict and has
                                      retried for 5 times.
                                    type: boolean
                                type: object
                            type: object
                        type: object
                    type: object
                  phase:
                    description: Phase is the current phase of the operation
                    type: string
                  retryCount:
                    description: RetryCount contains time of operation retries
                    format: int64
                    type: integer
                  startedAt:
                    description: StartedAt contains time of operation start
                    format: date-time
                    type: string
                  syncResult:
                    description: SyncResult is the result of a Sync operation
                    properties:
                      managedNamespaceMetadata:
                        description: ManagedNamespaceMetadata contains the current
                          sync state of managed namespace metadata
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                      resources:
                        description: Resources contains a list of sync result items
                          for each individual resource in a sync operation
                        items:
                          description: ResourceResult holds the operation result details
                            of a specific resource
                          properties:
                            group:
                              description: Group specifies the API group of the resource
                              type: string
                            hookPhase:
                              description: |-
                                HookPhase contains the state of any operation associated with this resource OR hook
                                This can also contain values for non-hook resources.
                              type: string
                            hookType:
                              description: HookType specifies the type of the hook.
                                Empty for non-hook resources
                              type: string
                            images:
                              description: Images contains the images related to the
                                ResourceResult
                              items:
                                type: string
                              type: array
                            kind:
                              description: Kind specifies the API kind of the resource
                              type: string
                            message:
                              description: Message contains an informational or error
                                message for the last sync OR operation
                              type: string
                            name:
                              description: Name specifies the name of the resource
                              type: string
                            namespace:
                              description: Namespace specifies the target namespace
                                of the resource
                              type: string
                            status:
                              description: Status holds the final result of the sync.
                                Will be empty if the resources is yet to be applied/pruned
                                and is always zero-value for hooks
                              type: string
                            syncPhase:
                              description: SyncPhase indicates the particular phase
                                of the sync that this result was acquired in
                              type: string
                            version:
                              description: Version specifies the API version of the
                                resource
                              type: string
                          required:
                          - group
                          - kind
                          - name
                          - namespace
                          - version
                          type: object
                        type: array
                      revision:
                        description: Revision holds the revision this sync operation
                          was performed to
                        type: string
                      revisions:
                        description: Revisions holds the revision this sync operation
                          was performed for respective indexed source in sources field
                        items:
                          type: string
                        type: array
                      source:
                        description: Source records the application source information
                          of the sync, used for comparing auto-sync
                        properties:
                          chart:
                            description: Chart is a Helm chart name, and must be specified
                              for applications sourced from a Helm repo.
                            type: string
                          directory:
                            description: Directory holds path/directory specific options
                            properties:
                              exclude:
                                description: Exclude contains a glob pattern to match
                                  paths against that should be explicitly excluded
                                  from being used during manifest generation
                                type: string
                              include:
                                description: Include contains a glob pattern to match
                                  paths against that should be explicitly included
                                  during manifest generation
                                type: string
                              jsonnet:
                                description: Jsonnet holds options specific to Jsonnet
                                properties:
                                  extVars:
                                    description: ExtVars is a list of Jsonnet External
                                      Variables
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                  libs:
                                    description: Additional library search dirs
                                    items:
                                      type: string
                                    type: array
                                  tlas:
                                    description: TLAS is a list of Jsonnet Top-level
                                      Arguments
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                type: object
                              recurse:
                                description: Recurse specifies whether to scan a directory
                                  recursively for manifests
                                type: boolean
                            type: object
                          helm:
                            description: Helm holds helm specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              fileParameters:
                                description: FileParameters are file parameters to
                                  the helm template
                                items:
                                  description: HelmFileParameter is a file parameter
                                    that's passed to helm template during manifest
                                    generation
                                  properties:
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    path:
                                      description: Path is the path to the file containing
                                        the values for the Helm parameter
                                      type: string
                                  type: object
                                type: array
                              ignoreMissingValueFiles:
                                description: IgnoreMissingValueFiles prevents helm
                                  template from failing when valueFiles do not exist
                                  locally by not appending them to helm template --values
                                type: boolean
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              namespace:
                                description: Namespace is an optional namespace to
                                  template with. If left empty, defaults to the app's
                                  destination namespace.
                                type: string
                              parameters:
                                description: Parameters is a list of Helm parameters
                                  which are passed to the helm template command upon
                                  manifest generation
                                items:
                                  description: HelmParameter is a parameter that's
                                    passed to helm template during manifest generation
                                  properties:
                                    forceString:
                                      description: ForceString determines whether
                                        to tell Helm to interpret booleans and numbers
                                        as strings
                                      type: boolean
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    value:
                                      description: Value is the value for the Helm
                                        parameter
                                      type: string
                                  type: object
                                type: array
                              passCredentials:
                                description: PassCredentials pass credentials to all
                                  domains (Helm's --pass-credentials)
                                type: boolean
                              releaseName:
                                description: ReleaseName is the Helm release name
                                  to use. If omitted it will use the application name
                                type: string
                              skipCrds:
                                description: SkipCrds skips custom resource definition
                                  installation step (Helm's --skip-crds)
                                type: boolean
                              skipSchemaValidation:
                                description: SkipSchemaValidation skips JSON schema
                                  validation (Helm's --skip-schema-validation)
                                type: boolean
                              skipTests:
                                description: SkipTests skips test manifest installation
                                  step (Helm's --skip-tests).
                                type: boolean
                              valueFiles:
                                description: ValuesFiles is a list of Helm value files
                                  to use when generating a template
                                items:
                                  type: string
                                type: array
                              values:
                                description: Values specifies Helm values to be passed
                                  to helm template, typically defined as a block.
                                  ValuesObject takes precedence over Values, so use
                                  one or the other.
                                type: string
                              valuesObject:
                                description: ValuesObject specifies Helm values to
                                  be passed to helm template, defined as a map. This
                                  takes precedence over Values.
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              version:
                                description: Version is the Helm version to use for
                                  templating ("3")
                                type: string
                            type: object
                          kustomize:
                            description: Kustomize holds kustomize specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              commonAnnotations:
                                additionalProperties:
                                  type: string
                                description: CommonAnnotations is a list of additional
                                  annotations to add to rendered manifests
                                type: object
                              commonAnnotationsEnvsubst:
                                description: CommonAnnotationsEnvsubst specifies whether
                                  to apply env variables substitution for annotation
                                  values
                                type: boolean
                              commonLabels:
                                additionalProperties:
                                  type: string
                                description: CommonLabels is a list of additional
                                  labels to add to rendered manifests
                                type: object
                              components:
                                description: Components specifies a list of kustomize
                                  components to add to the kustomization before building
                                items:
                                  type: string
                                type: array
                              forceCommonAnnotations:
                                description: ForceCommonAnnotations specifies whether
                                  to force applying common annotations to resources
                                  for Kustomize apps
                                type: boolean
                              forceCommonLabels:
                                description: ForceCommonLabels specifies whether to
                                  force applying common labels to resources for Kustomize
                                  apps
                                type: boolean
                              ignoreMissingComponents:
                                description: IgnoreMissingComponents prevents kustomize
                                  from failing when components do not exist locally
                                  by not appending them to kustomization file
                                type: boolean
                              images:
                                description: Images is a list of Kustomize image override
                                  specifications
                                items:
                                  description: KustomizeImage represents a Kustomize
                                    image definition in the format [old_image_name=]<image_name>:<image_tag>
                                  type: string
                                type: array
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              labelIncludeTemplates:
                                description: LabelIncludeTemplates specifies whether
                                  to apply common labels to resource templates or
                                  not
                                type: boolean
                              labelWithoutSelector:
                                description: LabelWithoutSelector specifies whether
                                  to apply common labels to resource selectors or
                                  not
                                type: boolean
                              namePrefix:
                                description: NamePrefix is a prefix appended to resources
                                  for Kustomize apps
                                type: string
                              nameSuffix:
                                description: NameSuffix is a suffix appended to resources
                                  for Kustomize apps
                                type: string
                              namespace:
                                description: Namespace sets the namespace that Kustomize
                                  adds to all resources
                                type: string
                              patches:
                                description: Patches is a list of Kustomize patches
                                items:
                                  properties:
                                    options:
                                      additionalProperties:
                                        type: boolean
                                      type: object
                                    patch:
                                      type: string
                                    path:
                                      type: string
                                    target:
                                      properties:
                                        annotationSelector:
                                          type: string
                                        group:
                                          type: string
                                        kind:
                                          type: string
                                        labelSelector:
                                          type: string
                                        name:
                                          type: string
                                        namespace:
                                          type: string
                                        version:
                                          type: string
                                      type: object
                                  type: object
                                type: array
                              replicas:
                                description: Replicas is a list of Kustomize Replicas
                                  override specifications
                                items:
                                  properties:
                                    count:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      description: Number of replicas
                                      x-kubernetes-int-or-string: true
                                    name:
                                      description: Name of Deployment or StatefulSet
                                      type: string
                                  required:
                                  - count
                                  - name
                                  type: object
                                type: array
                              version:
                                description: Version controls which version of Kustomize
                                  to use for rendering manifests
                                type: string
                            type: object
                          name:
                            description: Name is used to refer to a source and is
                              displayed in the UI. It is used in multi-source Applications.
                            type: string
                          path:
                            description: Path is a directory path within the Git repository,
                              and is only valid for applications sourced from Git.
                            type: string
                          plugin:
                            description: Plugin holds config management plugin specific
                              options
                            properties:
                              env:
                                description: Env is a list of environment variable
                                  entries
                                items:
                                  description: EnvEntry represents an entry in the
                                    application's environment
                                  properties:
                                    name:
                                      description: Name is the name of the variable,
                                        usually expressed in uppercase
                                      type: string
                                    value:
                                      description: Value is the value of the variable
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              name:
                                type: string
                              parameters:
                                items:
                                  properties:
                                    array:
                                      description: Array is the value of an array
                                        type parameter.
                                      items:
                                        type: string
                                      type: array
                                    map:
                                      additionalProperties:
                                        type: string
                                      description: Map is the value of a map type
                                        parameter.
                                      type: object
                                    name:
                                      description: Name is the name identifying a
                                        parameter.
                                      type: string
                                    string:
                                      description: String_ is the value of a string
                                        type parameter.
                                      type: string
                                  type: object
                                type: array
                            type: object
                          ref:
                            description: Ref is reference to another source within
                              sources field. This field will not be used if used with
                              a `source` tag.
                            type: string
                          repoURL:
                            description: RepoURL is the URL to the repository (Git
                              or Helm) that contains the application manifests
                            type: string
                          targetRevision:
                            description: |-
                              TargetRevision defines the revision of the source to sync the application to.
                              In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                              In case of Helm, this is a semver tag for the Chart's version.
                            type: string
                        required:
                        - repoURL
                        type: object
                      sources:
                        description: Source records the application source information
                          of the sync, used for comparing auto-sync
                        items:
                          description: ApplicationSource contains all required information
                            about the source of an application
                          properties:
                            chart:
                              description: Chart is a Helm chart name, and must be
                                specified for applications sourced from a Helm repo.
                              type: string
                            directory:
                              description: Directory holds path/directory specific
                                options
                              properties:
                                exclude:
                                  description: Exclude contains a glob pattern to
                                    match paths against that should be explicitly
                                    excluded from being used during manifest generation
                                  type: string
                                include:
                                  description: Include contains a glob pattern to
                                    match paths against that should be explicitly
                                    included during manifest generation
                                  type: string
                                jsonnet:
                                  description: Jsonnet holds options specific to Jsonnet
                                  properties:
                                    extVars:
                                      description: ExtVars is a list of Jsonnet External
                                        Variables
                                      items:
                                        description: JsonnetVar represents a variable
                                          to be passed to jsonnet during manifest
                                          generation
                                        properties:
                                          code:
                                            type: boolean
                                          name:
                                            type: string
                                          value:
                                            type: string
                                        required:
                                        - name
                                        - value
                                        type: object
                                      type: array
                                    libs:
                                      description: Additional library search dirs
                                      items:
                                        type: string
                                      type: array
                                    tlas:
                                      description: TLAS is a list of Jsonnet Top-level
                                        Arguments
                                      items:
                                        description: JsonnetVar represents a variable
                                          to be passed to jsonnet during manifest
                                          generation
                                        properties:
                                          code:
                                            type: boolean
                                          name:
                                            type: string
                                          value:
                                            type: string
                                        required:
                                        - name
                                        - value
                                        type: object
                                      type: array
                                  type: object
                                recurse:
                                  description: Recurse specifies whether to scan a
                                    directory recursively for manifests
                                  type: boolean
                              type: object
                            helm:
                              description: Helm holds helm specific options
                              properties:
                                apiVersions:
                                  description: |-
                                    APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                    Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                  items:
                                    type: string
                                  type: array
                                fileParameters:
                                  description: FileParameters are file parameters
                                    to the helm template
                                  items:
                                    description: HelmFileParameter is a file parameter
                                      that's passed to helm template during manifest
                                      generation
                                    properties:
                                      name:
                                        description: Name is the name of the Helm
                                          parameter
                                        type: string
                                      path:
                                        description: Path is the path to the file
                                          containing the values for the Helm parameter
                                        type: string
                                    type: object
                                  type: array
                                ignoreMissingValueFiles:
                                  description: IgnoreMissingValueFiles prevents helm
                                    template from failing when valueFiles do not exist
                                    locally by not appending them to helm template
                                    --values
                                  type: boolean
                                kubeVersion:
                                  description: |-
                                    KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                    uses the Kubernetes version of the target cluster.
                                  type: string
                                namespace:
                                  description: Namespace is an optional namespace
                                    to template with. If left empty, defaults to the
                                    app's destination namespace.
                                  type: string
                                parameters:
                                  description: Parameters is a list of Helm parameters
                                    which are passed to the helm template command
                                    upon manifest generation
                                  items:
                                    description: HelmParameter is a parameter that's
                                      passed to helm template during manifest generation
                                    properties:
                                      forceString:
                                        description: ForceString determines whether
                                          to tell Helm to interpret booleans and numbers
                                          as strings
                                        type: boolean
                                      name:
                                        description: Name is the name of the Helm
                                          parameter
                                        type: string
                                      value:
                                        description: Value is the value for the Helm
                                          parameter
                                        type: string
                                    type: object
                                  type: array
                                passCredentials:
                                  description: PassCredentials pass credentials to
                                    all domains (Helm's --pass-credentials)
                                  type: boolean
                                releaseName:
                                  description: ReleaseName is the Helm release name
                                    to use. If omitted it will use the application
                                    name
                                  type: string
                                skipCrds:
                                  description: SkipCrds skips custom resource definition
                                    installation step (Helm's --skip-crds)
                                  type: boolean
                                skipSchemaValidation:
                                  description: SkipSchemaValidation skips JSON schema
                                    validation (Helm's --skip-schema-validation)
                                  type: boolean
                                skipTests:
                                  description: SkipTests skips test manifest installation
                                    step (Helm's --skip-tests).
                                  type: boolean
                                valueFiles:
                                  description: ValuesFiles is a list of Helm value
                                    files to use when generating a template
                                  items:
                                    type: string
                                  type: array
                                values:
                                  description: Values specifies Helm values to be
                                    passed to helm template, typically defined as
                                    a block. ValuesObject takes precedence over Values,
                                    so use one or the other.
                                  type: string
                                valuesObject:
                                  description: ValuesObject specifies Helm values
                                    to be passed to helm template, defined as a map.
                                    This takes precedence over Values.
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                                version:
                                  description: Version is the Helm version to use
                                    for templating ("3")
                                  type: string
                              type: object
                            kustomize:
                              description: Kustomize holds kustomize specific options
                              properties:
                                apiVersions:
                                  description: |-
                                    APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                    Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                  items:
                                    type: string
                                  type: array
                                commonAnnotations:
                                  additionalProperties:
                                    type: string
                                  description: CommonAnnotations is a list of additional
                                    annotations to add to rendered manifests
                                  type: object
                                commonAnnotationsEnvsubst:
                                  description: CommonAnnotationsEnvsubst specifies
                                    whether to apply env variables substitution for
                                    annotation values
                                  type: boolean
                                commonLabels:
                                  additionalProperties:
                                    type: string
                                  description: CommonLabels is a list of additional
                                    labels to add to rendered manifests
                                  type: object
                                components:
                                  description: Components specifies a list of kustomize
                                    components to add to the kustomization before
                                    building
                                  items:
                                    type: string
                                  type: array
                                forceCommonAnnotations:
                                  description: ForceCommonAnnotations specifies whether
                                    to force applying common annotations to resources
                                    for Kustomize apps
                                  type: boolean
                                forceCommonLabels:
                                  description: ForceCommonLabels specifies whether
                                    to force applying common labels to resources for
                                    Kustomize apps
                                  type: boolean
                                ignoreMissingComponents:
                                  description: IgnoreMissingComponents prevents kustomize
                                    from failing when components do not exist locally
                                    by not appending them to kustomization file
                                  type: boolean
                                images:
                                  description: Images is a list of Kustomize image
                                    override specifications
                                  items:
                                    description: KustomizeImage represents a Kustomize
                                      image definition in the format [old_image_name=]<image_name>:<image_tag>
                                    type: string
                                  type: array
                                kubeVersion:
                                  description: |-
                                    KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                    uses the Kubernetes version of the target cluster.
                                  type: string
                                labelIncludeTemplates:
                                  description: LabelIncludeTemplates specifies whether
                                    to apply common labels to resource templates or
                                    not
                                  type: boolean
                                labelWithoutSelector:
                                  description: LabelWithoutSelector specifies whether
                                    to apply common labels to resource selectors or
                                    not
                                  type: boolean
                                namePrefix:
                                  description: NamePrefix is a prefix appended to
                                    resources for Kustomize apps
                                  type: string
                                nameSuffix:
                                  description: NameSuffix is a suffix appended to
                                    resources for Kustomize apps
                                  type: string
                                namespace:
                                  description: Namespace sets the namespace that Kustomize
                                    adds to all resources
                                  type: string
                                patches:
                                  description: Patches is a list of Kustomize patches
                                  items:
                                    properties:
                                      options:
                                        additionalProperties:
                                          type: boolean
                                        type: object
                                      patch:
                                        type: string
                                      path:
                                        type: string
                                      target:
                                        properties:
                                          annotationSelector:
                                            type: string
                                          group:
                                            type: string
                                          kind:
                                            type: string
                                          labelSelector:
                                            type: string
                                          name:
                                            type: string
                                          namespace:
                                            type: string
                                          version:
                                            type: string
                                        type: object
                                    type: object
                                  type: array
                                replicas:
                                  description: Replicas is a list of Kustomize Replicas
                                    override specifications
                                  items:
                                    properties:
                                      count:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Number of replicas
                                        x-kubernetes-int-or-string: true
                                      name:
                                        description: Name of Deployment or StatefulSet
                                        type: string
                                    required:
                                    - count
                                    - name
                                    type: object
                                  type: array
                                version:
                                  description: Version controls which version of Kustomize
                                    to use for rendering manifests
                                  type: string
                              type: object
                            name:
                              description: Name is used to refer to a source and is
                                displayed in the UI. It is used in multi-source Applications.
                              type: string
                            path:
                              description: Path is a directory path within the Git
                                repository, and is only valid for applications sourced
                                from Git.
                              type: string
                            plugin:
                              description: Plugin holds config management plugin specific
                                options
                              properties:
                                env:
                                  description: Env is a list of environment variable
                                    entries
                                  items:
                                    description: EnvEntry represents an entry in the
                                      application's environment
                                    properties:
                                      name:
                                        description: Name is the name of the variable,
                                          usually expressed in uppercase
                                        type: string
                                      value:
                                        description: Value is the value of the variable
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                name:
                                  type: string
                                parameters:
                                  items:
                                    properties:
                                      array:
                                        description: Array is the value of an array
                                          type parameter.
                                        items:
                                          type: string
                                        type: array
                                      map:
                                        additionalProperties:
                                          type: string
                                        description: Map is the value of a map type
                                          parameter.
                                        type: object
                                      name:
                                        description: Name is the name identifying
                                          a parameter.
                                        type: string
                                      string:
                                        description: String_ is the value of a string
                                          type parameter.
                                        type: string
                                    type: object
                                  type: array
                              type: object
                            ref:
                              description: Ref is reference to another source within
                                sources field. This field will not be used if used
                                with a `source` tag.
                              type: string
                            repoURL:
                              description: RepoURL is the URL to the repository (Git
                                or Helm) that contains the application manifests
                              type: string
                            targetRevision:
                              description: |-
                                TargetRevision defines the revision of the source to sync the application to.
                                In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                                In case of Helm, this is a semver tag for the Chart's version.
                              type: string
                          required:
                          - repoURL
                          type: object
                        type: array
                    required:
                    - revision
                    type: object
                required:
                - operation
                - phase
                - startedAt
                type: object
              reconciledAt:
                description: ReconciledAt indicates when the application state was
                  reconciled using the latest git version
                format: date-time
                type: string
              resourceHealthSource:
                description: 'ResourceHealthSource indicates where the resource health
                  status is stored: inline if not set or appTree'
                type: string
              resources:
                description: Resources is a list of Kubernetes resources managed by
                  this application
                items:
                  description: ResourceStatus holds the current synchronization and
                    health status of a Kubernetes resource.
                  properties:
                    group:
                      description: Group represents the API group of the resource
                        (e.g., "apps" for Deployments).
                      type: string
                    health:
                      description: Health indicates the health status of the resource
                        (e.g., Healthy, Degraded, Progressing).
                      properties:
                        lastTransitionTime:
                          description: |-
                            LastTransitionTime is the time the HealthStatus was set or updated

                            Deprecated: this field is not used and will be removed in a future release.
                          format: date-time
                          type: string
                        message:
                          description: Message is a human-readable informational message
                            describing the health status
                          type: string
                        status:
                          description: Status holds the status code of the resource
                          type: string
                      type: object
                    hook:
                      description: Hook is true if the resource is used as a lifecycle
                        hook in an Argo CD application.
                      type: boolean
                    kind:
                      description: Kind specifies the type of the resource (e.g.,
                        "Deployment", "Service").
                      type: string
                    name:
                      description: Name is the unique name of the resource within
                        the namespace.
                      type: string
                    namespace:
                      description: Namespace defines the Kubernetes namespace where
                        the resource is located.
                      type: string
                    requiresDeletionConfirmation:
                      description: RequiresDeletionConfirmation is true if the resource
                        requires explicit user confirmation before deletion.
                      type: boolean
                    requiresPruning:
                      description: RequiresPruning is true if the resource needs to
                        be pruned (deleted) as part of synchronization.
                      type: boolean
                    status:
                      description: Status represents the synchronization state of
                        the resource (e.g., Synced, OutOfSync).
                      type: string
                    syncWave:
                      description: |-
                        SyncWave determines the order in which resources are applied during a sync operation.
                        Lower values are applied first.
                      format: int64
                      type: integer
                    version:
                      description: Version indicates the API version of the resource
                        (e.g., "v1", "v1beta1").
                      type: string
                  type: object
                type: array
              sourceHydrator:
                description: SourceHydrator stores information about the current state
                  of source hydration
                properties:
                  currentOperation:
                    description: CurrentOperation holds the status of the hydrate
                      operation
                    properties:
                      drySHA:
                        description: DrySHA holds the resolved revision (sha) of the
                          dry source as of the most recent reconciliation
                        type: string
                      finishedAt:
                        description: FinishedAt indicates when the hydrate operation
                          finished
                        format: date-time
                        type: string
                      hydratedSHA:
                        description: HydratedSHA holds the resolved revision (sha)
                          of the hydrated source as of the most recent reconciliation
                        type: string
                      message:
                        description: Message contains a message describing the current
                          status of the hydrate operation
                        type: string
                      phase:
                        description: Phase indicates the status of the hydrate operation
                        enum:
                        - Hydrating
                        - Failed
                        - Hydrated
                        type: string
                      sourceHydrator:
                        description: SourceHydrator holds the hydrator config used
                          for the hydrate operation
                        properties:
                          drySource:
                            description: DrySource specifies where the dry "don't
                              repeat yourself" manifest source lives.
                            properties:
                              path:
                                description: Path is a directory path within the Git
                                  repository where the manifests are located
                                type: string
                              repoURL:
                                description: RepoURL is the URL to the git repository
                                  that contains the application manifests
                                type: string
                              targetRevision:
                                description: TargetRevision defines the revision of
                                  the source to hydrate
                                type: string
                            required:
                            - path
                            - repoURL
                            - targetRevision
                            type: object
                          hydrateTo:
                            description: |-
                              HydrateTo specifies an optional "staging" location to push hydrated manifests to. An external system would then
                              have to move manifests to the SyncSource, e.g. by pull request.
                            properties:
                              targetBranch:
                                description: TargetBranch is the branch to which hydrated
                                  manifests should be committed
                                type: string
                            required:
                            - targetBranch
                            type: object
                          syncSource:
                            description: SyncSource specifies where to sync hydrated
                              manifests from.
                            properties:
                              path:
                                description: |-
                                  Path is a directory path within the git repository where hydrated manifests should be committed to and synced
                                  from. If hydrateTo is set, this is just the path from which hydrated manifests will be synced.
                                type: string
                              targetBranch:
                                description: TargetBranch is the branch to which hydrated
                                  manifests should be committed
                                type: string
                            required:
                            - path
                            - targetBranch
                            type: object
                        required:
                        - drySource
                        - syncSource
                        type: object
                      startedAt:
                        description: StartedAt indicates when the hydrate operation
                          started
                        format: date-time
                        type: string
                    required:
                    - message
                    - phase
                    type: object
                  lastSuccessfulOperation:
                    description: LastSuccessfulOperation holds info about the most
                      recent successful hydration
                    properties:
                      drySHA:
                        description: DrySHA holds the resolved revision (sha) of the
                          dry source as of the most recent reconciliation
                        type: string
                      hydratedSHA:
                        description: HydratedSHA holds the resolved revision (sha)
                          of the hydrated source as of the most recent reconciliation
                        type: string
                      sourceHydrator:
                        description: SourceHydrator holds the hydrator config used
                          for the hydrate operation
                        properties:
                          drySource:
                            description: DrySource specifies where the dry "don't
                              repeat yourself" manifest source lives.
                            properties:
                              path:
                                description: Path is a directory path within the Git
                                  repository where the manifests are located
                                type: string
                              repoURL:
                                description: RepoURL is the URL to the git repository
                                  that contains the application manifests
                                type: string
                              targetRevision:
                                description: TargetRevision defines the revision of
                                  the source to hydrate
                                type: string
                            required:
                            - path
                            - repoURL
                            - targetRevision
                            type: object
                          hydrateTo:
                            description: |-
                              HydrateTo specifies an optional "staging" location to push hydrated manifests to. An external system would then
                              have to move manifests to the SyncSource, e.g. by pull request.
                            properties:
                              targetBranch:
                                description: TargetBranch is the branch to which hydrated
                                  manifests should be committed
                                type: string
                            required:
                            - targetBranch
                            type: object
                          syncSource:
                            description: SyncSource specifies where to sync hydrated
                              manifests from.
                            properties:
                              path:
                                description: |-
                                  Path is a directory path within the git repository where hydrated manifests should be committed to and synced
                                  from. If hydrateTo is set, this is just the path from which hydrated manifests will be synced.
                                type: string
                              targetBranch:
                                description: TargetBranch is the branch to which hydrated
                                  manifests should be committed
                                type: string
                            required:
                            - path
                            - targetBranch
                            type: object
                        required:
                        - drySource
                        - syncSource
                        type: object
                    type: object
                type: object
              sourceType:
                description: SourceType specifies the type of this application
                type: string
              sourceTypes:
                description: SourceTypes specifies the type of the sources included
                  in the application
                items:
                  description: ApplicationSourceType specifies the type of the application's
                    source
                  type: string
                type: array
              summary:
                description: Summary contains a list of URLs and container images
                  used by this application
                properties:
                  externalURLs:
                    description: ExternalURLs holds all external URLs of application
                      child resources.
                    items:
                      type: string
                    type: array
                  images:
                    description: Images holds all images of application child resources.
                    items:
                      type: string
                    type: array
                type: object
              sync:
                description: Sync contains information about the application's current
                  sync status
                properties:
                  comparedTo:
                    description: ComparedTo contains information about what has been
                      compared
                    properties:
                      destination:
                        description: Destination is a reference to the application's
                          destination used for comparison
                        properties:
                          name:
                            description: Name is an alternate way of specifying the
                              target cluster by its symbolic name. This must be set
                              if Server is not set.
                            type: string
                          namespace:
                            description: |-
                              Namespace specifies the target namespace for the application's resources.
                              The namespace will only be set for namespace-scoped resources that have not set a value for .metadata.namespace
                            type: string
                          server:
                            description: Server specifies the URL of the target cluster's
                              Kubernetes control plane API. This must be set if Name
                              is not set.
                            type: string
                        type: object
                      ignoreDifferences:
                        description: IgnoreDifferences is a reference to the application's
                          ignored differences used for comparison
                        items:
                          description: ResourceIgnoreDifferences contains resource
                            filter and list of json paths which should be ignored
                            during comparison with live state.
                          properties:
                            group:
                              type: string
                            jqPathExpressions:
                              items:
                                type: string
                              type: array
                            jsonPointers:
                              items:
                                type: string
                              type: array
                            kind:
                              type: string
                            managedFieldsManagers:
                              description: |-
                                ManagedFieldsManagers is a list of trusted managers. Fields mutated by those managers will take precedence over the
                                desired state defined in the SCM and won't be displayed in diffs
                              items:
                                type: string
                              type: array
                            name:
                              type: string
                            namespace:
                              type: string
                          required:
                          - kind
                          type: object
                        type: array
                      source:
                        description: Source is a reference to the application's source
                          used for comparison
                        properties:
                          chart:
                            description: Chart is a Helm chart name, and must be specified
                              for applications sourced from a Helm repo.
                            type: string
                          directory:
                            description: Directory holds path/directory specific options
                            properties:
                              exclude:
                                description: Exclude contains a glob pattern to match
                                  paths against that should be explicitly excluded
                                  from being used during manifest generation
                                type: string
                              include:
                                description: Include contains a glob pattern to match
                                  paths against that should be explicitly included
                                  during manifest generation
                                type: string
                              jsonnet:
                                description: Jsonnet holds options specific to Jsonnet
                                properties:
                                  extVars:
                                    description: ExtVars is a list of Jsonnet External
                                      Variables
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                  libs:
                                    description: Additional library search dirs
                                    items:
                                      type: string
                                    type: array
                                  tlas:
                                    description: TLAS is a list of Jsonnet Top-level
                                      Arguments
                                    items:
                                      description: JsonnetVar represents a variable
                                        to be passed to jsonnet during manifest generation
                                      properties:
                                        code:
                                          type: boolean
                                        name:
                                          type: string
                                        value:
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                type: object
                              recurse:
                                description: Recurse specifies whether to scan a directory
                                  recursively for manifests
                                type: boolean
                            type: object
                          helm:
                            description: Helm holds helm specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              fileParameters:
                                description: FileParameters are file parameters to
                                  the helm template
                                items:
                                  description: HelmFileParameter is a file parameter
                                    that's passed to helm template during manifest
                                    generation
                                  properties:
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    path:
                                      description: Path is the path to the file containing
                                        the values for the Helm parameter
                                      type: string
                                  type: object
                                type: array
                              ignoreMissingValueFiles:
                                description: IgnoreMissingValueFiles prevents helm
                                  template from failing when valueFiles do not exist
                                  locally by not appending them to helm template --values
                                type: boolean
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              namespace:
                                description: Namespace is an optional namespace to
                                  template with. If left empty, defaults to the app's
                                  destination namespace.
                                type: string
                              parameters:
                                description: Parameters is a list of Helm parameters
                                  which are passed to the helm template command upon
                                  manifest generation
                                items:
                                  description: HelmParameter is a parameter that's
                                    passed to helm template during manifest generation
                                  properties:
                                    forceString:
                                      description: ForceString determines whether
                                        to tell Helm to interpret booleans and numbers
                                        as strings
                                      type: boolean
                                    name:
                                      description: Name is the name of the Helm parameter
                                      type: string
                                    value:
                                      description: Value is the value for the Helm
                                        parameter
                                      type: string
                                  type: object
                                type: array
                              passCredentials:
                                description: PassCredentials pass credentials to all
                                  domains (Helm's --pass-credentials)
                                type: boolean
                              releaseName:
                                description: ReleaseName is the Helm release name
                                  to use. If omitted it will use the application name
                                type: string
                              skipCrds:
                                description: SkipCrds skips custom resource definition
                                  installation step (Helm's --skip-crds)
                                type: boolean
                              skipSchemaValidation:
                                description: SkipSchemaValidation skips JSON schema
                                  validation (Helm's --skip-schema-validation)
                                type: boolean
                              skipTests:
                                description: SkipTests skips test manifest installation
                                  step (Helm's --skip-tests).
                                type: boolean
                              valueFiles:
                                description: ValuesFiles is a list of Helm value files
                                  to use when generating a template
                                items:
                                  type: string
                                type: array
                              values:
                                description: Values specifies Helm values to be passed
                                  to helm template, typically defined as a block.
                                  ValuesObject takes precedence over Values, so use
                                  one or the other.
                                type: string
                              valuesObject:
                                description: ValuesObject specifies Helm values to
                                  be passed to helm template, defined as a map. This
                                  takes precedence over Values.
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              version:
                                description: Version is the Helm version to use for
                                  templating ("3")
                                type: string
                            type: object
                          kustomize:
                            description: Kustomize holds kustomize specific options
                            properties:
                              apiVersions:
                                description: |-
                                  APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                  Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                items:
                                  type: string
                                type: array
                              commonAnnotations:
                                additionalProperties:
                                  type: string
                                description: CommonAnnotations is a list of additional
                                  annotations to add to rendered manifests
                                type: object
                              commonAnnotationsEnvsubst:
                                description: CommonAnnotationsEnvsubst specifies whether
                                  to apply env variables substitution for annotation
                                  values
                                type: boolean
                              commonLabels:
                                additionalProperties:
                                  type: string
                                description: CommonLabels is a list of additional
                                  labels to add to rendered manifests
                                type: object
                              components:
                                description: Components specifies a list of kustomize
                                  components to add to the kustomization before building
                                items:
                                  type: string
                                type: array
                              forceCommonAnnotations:
                                description: ForceCommonAnnotations specifies whether
                                  to force applying common annotations to resources
                                  for Kustomize apps
                                type: boolean
                              forceCommonLabels:
                                description: ForceCommonLabels specifies whether to
                                  force applying common labels to resources for Kustomize
                                  apps
                                type: boolean
                              ignoreMissingComponents:
                                description: IgnoreMissingComponents prevents kustomize
                                  from failing when components do not exist locally
                                  by not appending them to kustomization file
                                type: boolean
                              images:
                                description: Images is a list of Kustomize image override
                                  specifications
                                items:
                                  description: KustomizeImage represents a Kustomize
                                    image definition in the format [old_image_name=]<image_name>:<image_tag>
                                  type: string
                                type: array
                              kubeVersion:
                                description: |-
                                  KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                  uses the Kubernetes version of the target cluster.
                                type: string
                              labelIncludeTemplates:
                                description: LabelIncludeTemplates specifies whether
                                  to apply common labels to resource templates or
                                  not
                                type: boolean
                              labelWithoutSelector:
                                description: LabelWithoutSelector specifies whether
                                  to apply common labels to resource selectors or
                                  not
                                type: boolean
                              namePrefix:
                                description: NamePrefix is a prefix appended to resources
                                  for Kustomize apps
                                type: string
                              nameSuffix:
                                description: NameSuffix is a suffix appended to resources
                                  for Kustomize apps
                                type: string
                              namespace:
                                description: Namespace sets the namespace that Kustomize
                                  adds to all resources
                                type: string
                              patches:
                                description: Patches is a list of Kustomize patches
                                items:
                                  properties:
                                    options:
                                      additionalProperties:
                                        type: boolean
                                      type: object
                                    patch:
                                      type: string
                                    path:
                                      type: string
                                    target:
                                      properties:
                                        annotationSelector:
                                          type: string
                                        group:
                                          type: string
                                        kind:
                                          type: string
                                        labelSelector:
                                          type: string
                                        name:
                                          type: string
                                        namespace:
                                          type: string
                                        version:
                                          type: string
                                      type: object
                                  type: object
                                type: array
                              replicas:
                                description: Replicas is a list of Kustomize Replicas
                                  override specifications
                                items:
                                  properties:
                                    count:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      description: Number of replicas
                                      x-kubernetes-int-or-string: true
                                    name:
                                      description: Name of Deployment or StatefulSet
                                      type: string
                                  required:
                                  - count
                                  - name
                                  type: object
                                type: array
                              version:
                                description: Version controls which version of Kustomize
                                  to use for rendering manifests
                                type: string
                            type: object
                          name:
                            description: Name is used to refer to a source and is
                              displayed in the UI. It is used in multi-source Applications.
                            type: string
                          path:
                            description: Path is a directory path within the Git repository,
                              and is only valid for applications sourced from Git.
                            type: string
                          plugin:
                            description: Plugin holds config management plugin specific
                              options
                            properties:
                              env:
                                description: Env is a list of environment variable
                                  entries
                                items:
                                  description: EnvEntry represents an entry in the
                                    application's environment
                                  properties:
                                    name:
                                      description: Name is the name of the variable,
                                        usually expressed in uppercase
                                      type: string
                                    value:
                                      description: Value is the value of the variable
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              name:
                                type: string
                              parameters:
                                items:
                                  properties:
                                    array:
                                      description: Array is the value of an array
                                        type parameter.
                                      items:
                                        type: string
                                      type: array
                                    map:
                                      additionalProperties:
                                        type: string
                                      description: Map is the value of a map type
                                        parameter.
                                      type: object
                                    name:
                                      description: Name is the name identifying a
                                        parameter.
                                      type: string
                                    string:
                                      description: String_ is the value of a string
                                        type parameter.
                                      type: string
                                  type: object
                                type: array
                            type: object
                          ref:
                            description: Ref is reference to another source within
                              sources field. This field will not be used if used with
                              a `source` tag.
                            type: string
                          repoURL:
                            description: RepoURL is the URL to the repository (Git
                              or Helm) that contains the application manifests
                            type: string
                          targetRevision:
                            description: |-
                              TargetRevision defines the revision of the source to sync the application to.
                              In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                              In case of Helm, this is a semver tag for the Chart's version.
                            type: string
                        required:
                        - repoURL
                        type: object
                      sources:
                        description: Sources is a reference to the application's multiple
                          sources used for comparison
                        items:
                          description: ApplicationSource contains all required information
                            about the source of an application
                          properties:
                            chart:
                              description: Chart is a Helm chart name, and must be
                                specified for applications sourced from a Helm repo.
                              type: string
                            directory:
                              description: Directory holds path/directory specific
                                options
                              properties:
                                exclude:
                                  description: Exclude contains a glob pattern to
                                    match paths against that should be explicitly
                                    excluded from being used during manifest generation
                                  type: string
                                include:
                                  description: Include contains a glob pattern to
                                    match paths against that should be explicitly
                                    included during manifest generation
                                  type: string
                                jsonnet:
                                  description: Jsonnet holds options specific to Jsonnet
                                  properties:
                                    extVars:
                                      description: ExtVars is a list of Jsonnet External
                                        Variables
                                      items:
                                        description: JsonnetVar represents a variable
                                          to be passed to jsonnet during manifest
                                          generation
                                        properties:
                                          code:
                                            type: boolean
                                          name:
                                            type: string
                                          value:
                                            type: string
                                        required:
                                        - name
                                        - value
                                        type: object
                                      type: array
                                    libs:
                                      description: Additional library search dirs
                                      items:
                                        type: string
                                      type: array
                                    tlas:
                                      description: TLAS is a list of Jsonnet Top-level
                                        Arguments
                                      items:
                                        description: JsonnetVar represents a variable
                                          to be passed to jsonnet during manifest
                                          generation
                                        properties:
                                          code:
                                            type: boolean
                                          name:
                                            type: string
                                          value:
                                            type: string
                                        required:
                                        - name
                                        - value
                                        type: object
                                      type: array
                                  type: object
                                recurse:
                                  description: Recurse specifies whether to scan a
                                    directory recursively for manifests
                                  type: boolean
                              type: object
                            helm:
                              description: Helm holds helm specific options
                              properties:
                                apiVersions:
                                  description: |-
                                    APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                    Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                  items:
                                    type: string
                                  type: array
                                fileParameters:
                                  description: FileParameters are file parameters
                                    to the helm template
                                  items:
                                    description: HelmFileParameter is a file parameter
                                      that's passed to helm template during manifest
                                      generation
                                    properties:
                                      name:
                                        description: Name is the name of the Helm
                                          parameter
                                        type: string
                                      path:
                                        description: Path is the path to the file
                                          containing the values for the Helm parameter
                                        type: string
                                    type: object
                                  type: array
                                ignoreMissingValueFiles:
                                  description: IgnoreMissingValueFiles prevents helm
                                    template from failing when valueFiles do not exist
                                    locally by not appending them to helm template
                                    --values
                                  type: boolean
                                kubeVersion:
                                  description: |-
                                    KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                    uses the Kubernetes version of the target cluster.
                                  type: string
                                namespace:
                                  description: Namespace is an optional namespace
                                    to template with. If left empty, defaults to the
                                    app's destination namespace.
                                  type: string
                                parameters:
                                  description: Parameters is a list of Helm parameters
                                    which are passed to the helm template command
                                    upon manifest generation
                                  items:
                                    description: HelmParameter is a parameter that's
                                      passed to helm template during manifest generation
                                    properties:
                                      forceString:
                                        description: ForceString determines whether
                                          to tell Helm to interpret booleans and numbers
                                          as strings
                                        type: boolean
                                      name:
                                        description: Name is the name of the Helm
                                          parameter
                                        type: string
                                      value:
                                        description: Value is the value for the Helm
                                          parameter
                                        type: string
                                    type: object
                                  type: array
                                passCredentials:
                                  description: PassCredentials pass credentials to
                                    all domains (Helm's --pass-credentials)
                                  type: boolean
                                releaseName:
                                  description: ReleaseName is the Helm release name
                                    to use. If omitted it will use the application
                                    name
                                  type: string
                                skipCrds:
                                  description: SkipCrds skips custom resource definition
                                    installation step (Helm's --skip-crds)
                                  type: boolean
                                skipSchemaValidation:
                                  description: SkipSchemaValidation skips JSON schema
                                    validation (Helm's --skip-schema-validation)
                                  type: boolean
                                skipTests:
                                  description: SkipTests skips test manifest installation
                                    step (Helm's --skip-tests).
                                  type: boolean
                                valueFiles:
                                  description: ValuesFiles is a list of Helm value
                                    files to use when generating a template
                                  items:
                                    type: string
                                  type: array
                                values:
                                  description: Values specifies Helm values to be
                                    passed to helm template, typically defined as
                                    a block. ValuesObject takes precedence over Values,
                                    so use one or the other.
                                  type: string
                                valuesObject:
                                  description: ValuesObject specifies Helm values
                                    to be passed to helm template, defined as a map.
                                    This takes precedence over Values.
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                                version:
                                  description: Version is the Helm version to use
                                    for templating ("3")
                                  type: string
                              type: object
                            kustomize:
                              description: Kustomize holds kustomize specific options
                              properties:
                                apiVersions:
                                  description: |-
                                    APIVersions specifies the Kubernetes resource API versions to pass to Helm when templating manifests. By default,
                                    Argo CD uses the API versions of the target cluster. The format is [group/]version/kind.
                                  items:
                                    type: string
                                  type: array
                                commonAnnotations:
                                  additionalProperties:
                                    type: string
                                  description: CommonAnnotations is a list of additional
                                    annotations to add to rendered manifests
                                  type: object
                                commonAnnotationsEnvsubst:
                                  description: CommonAnnotationsEnvsubst specifies
                                    whether to apply env variables substitution for
                                    annotation values
                                  type: boolean
                                commonLabels:
                                  additionalProperties:
                                    type: string
                                  description: CommonLabels is a list of additional
                                    labels to add to rendered manifests
                                  type: object
                                components:
                                  description: Components specifies a list of kustomize
                                    components to add to the kustomization before
                                    building
                                  items:
                                    type: string
                                  type: array
                                forceCommonAnnotations:
                                  description: ForceCommonAnnotations specifies whether
                                    to force applying common annotations to resources
                                    for Kustomize apps
                                  type: boolean
                                forceCommonLabels:
                                  description: ForceCommonLabels specifies whether
                                    to force applying common labels to resources for
                                    Kustomize apps
                                  type: boolean
                                ignoreMissingComponents:
                                  description: IgnoreMissingComponents prevents kustomize
                                    from failing when components do not exist locally
                                    by not appending them to kustomization file
                                  type: boolean
                                images:
                                  description: Images is a list of Kustomize image
                                    override specifications
                                  items:
                                    description: KustomizeImage represents a Kustomize
                                      image definition in the format [old_image_name=]<image_name>:<image_tag>
                                    type: string
                                  type: array
                                kubeVersion:
                                  description: |-
                                    KubeVersion specifies the Kubernetes API version to pass to Helm when templating manifests. By default, Argo CD
                                    uses the Kubernetes version of the target cluster.
                                  type: string
                                labelIncludeTemplates:
                                  description: LabelIncludeTemplates specifies whether
                                    to apply common labels to resource templates or
                                    not
                                  type: boolean
                                labelWithoutSelector:
                                  description: LabelWithoutSelector specifies whether
                                    to apply common labels to resource selectors or
                                    not
                                  type: boolean
                                namePrefix:
                                  description: NamePrefix is a prefix appended to
                                    resources for Kustomize apps
                                  type: string
                                nameSuffix:
                                  description: NameSuffix is a suffix appended to
                                    resources for Kustomize apps
                                  type: string
                                namespace:
                                  description: Namespace sets the namespace that Kustomize
                                    adds to all resources
                                  type: string
                                patches:
                                  description: Patches is a list of Kustomize patches
                                  items:
                                    properties:
                                      options:
                                        additionalProperties:
                                          type: boolean
                                        type: object
                                      patch:
                                        type: string
                                      path:
                                        type: string
                                      target:
                                        properties:
                                          annotationSelector:
                                            type: string
                                          group:
                                            type: string
                                          kind:
                                            type: string
                                          labelSelector:
                                            type: string
                                          name:
                                            type: string
                                          namespace:
                                            type: string
                                          version:
                                            type: string
                                        type: object
                                    type: object
                                  type: array
                                replicas:
                                  description: Replicas is a list of Kustomize Replicas
                                    override specifications
                                  items:
                                    properties:
                                      count:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Number of replicas
                                        x-kubernetes-int-or-string: true
                                      name:
                                        description: Name of Deployment or StatefulSet
                                        type: string
                                    required:
                                    - count
                                    - name
                                    type: object
                                  type: array
                                version:
                                  description: Version controls which version of Kustomize
                                    to use for rendering manifests
                                  type: string
                              type: object
                            name:
                              description: Name is used to refer to a source and is
                                displayed in the UI. It is used in multi-source Applications.
                              type: string
                            path:
                              description: Path is a directory path within the Git
                                repository, and is only valid for applications sourced
                                from Git.
                              type: string
                            plugin:
                              description: Plugin holds config management plugin specific
                                options
                              properties:
                                env:
                                  description: Env is a list of environment variable
                                    entries
                                  items:
                                    description: EnvEntry represents an entry in the
                                      application's environment
                                    properties:
                                      name:
                                        description: Name is the name of the variable,
                                          usually expressed in uppercase
                                        type: string
                                      value:
                                        description: Value is the value of the variable
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                name:
                                  type: string
                                parameters:
                                  items:
                                    properties:
                                      array:
                                        description: Array is the value of an array
                                          type parameter.
                                        items:
                                          type: string
                                        type: array
                                      map:
                                        additionalProperties:
                                          type: string
                                        description: Map is the value of a map type
                                          parameter.
                                        type: object
                                      name:
                                        description: Name is the name identifying
                                          a parameter.
                                        type: string
                                      string:
                                        description: String_ is the value of a string
                                          type parameter.
                                        type: string
                                    type: object
                                  type: array
                              type: object
                            ref:
                              description: Ref is reference to another source within
                                sources field. This field will not be used if used
                                with a `source` tag.
                              type: string
                            repoURL:
                              description: RepoURL is the URL to the repository (Git
                                or Helm) that contains the application manifests
                              type: string
                            targetRevision:
                              description: |-
                                TargetRevision defines the revision of the source to sync the application to.
                                In case of Git, this can be commit, tag, or branch. If omitted, will equal to HEAD.
                                In case of Helm, this is a semver tag for the Chart's version.
                              type: string
                          required:
                          - repoURL
                          type: object
                        type: array
                    required:
                    - destination
                    type: object
                  revision:
                    description: Revision contains information about the revision
                      the comparison has been performed to
                    type: string
                  revisions:
                    description: Revisions contains information about the revisions
                      of multiple sources the comparison has been performed to
                    items:
                      type: string
                    type: array
                  status:
                    description: Status is the sync state of the comparison
                    type: string
                required:
                - status
                type: object
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources: {}
{{- end }}

