{{- if and .Values.dex.enabled .Values.dex.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "argo-cd.dex.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 4 }}
    {{- with .Values.dex.pdb.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.dex.pdb.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.dex.pdb.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- else }}
  minAvailable: {{ .Values.dex.pdb.minAvailable | default 0 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.dex.name) | nindent 6 }}
{{- end }}
