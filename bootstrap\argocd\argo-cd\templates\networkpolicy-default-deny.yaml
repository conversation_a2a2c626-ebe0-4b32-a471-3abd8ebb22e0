{{- if and .Values.global.networkPolicy.create .Values.global.networkPolicy.defaultDenyIngress }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  labels:
    {{- include "argo-cd.labels" (dict "context" .) | nindent 4 }}
  name: {{ template "argo-cd.fullname" . }}-default-deny
  namespace: {{ include  "argo-cd.namespace" . }}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
{{- end }}
