{{- if and .Values.redisSecretInit.enabled (not .Values.externalRedis.host) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redisSecretInit.name "name" .Values.redisSecretInit.name) | nindent 4 }}
  name: {{ include "argo-cd.redisSecretInit.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . | quote }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "argo-cd.redisSecretInit.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "argo-cd.redisSecretInit.serviceAccountName" . }}
{{- end }}
