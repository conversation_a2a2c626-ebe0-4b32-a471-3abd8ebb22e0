{{- if and .Values.redisSecretInit.enabled .Values.redisSecretInit.serviceAccount.create (not .Values.externalRedis.host) }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.redisSecretInit.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.redisSecretInit.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . | quote }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
    {{- range $key, $value := .Values.redisSecretInit.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redisSecretInit.name "name" .Values.redisSecretInit.name) | nindent 4 }}
{{- end }}
