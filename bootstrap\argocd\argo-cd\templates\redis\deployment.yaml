{{- $redisHa := index .Values "redis-ha" -}}
{{- if and .Values.redis.enabled (not $redisHa.enabled) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.redis.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ include "argo-cd.redis.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
    {{- with (mergeOverwrite (deepCopy .Values.global.deploymentLabels) .Values.redis.deploymentLabels) }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: 1
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "argo-cd.name" . }}-{{ .Values.redis.name }}
  template:
    metadata:
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.redis.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.redis.podAnnotations) }}
      annotations:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
      {{- end }}
    spec:
      {{- with .Values.redis.runtimeClassName | default .Values.global.runtimeClassName }}
      runtimeClassName: {{ . }}
      {{- end }}
      {{- with .Values.redis.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- if .Values.redis.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.redis.terminationGracePeriodSeconds }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.redis.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.redis.automountServiceAccountToken }}
      containers:
      - name: {{ .Values.redis.name }}
        image: {{ .Values.redis.image.repository }}:{{ .Values.redis.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.redis.image.imagePullPolicy }}
        args:
        {{- with .Values.redis.extraArgs }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        - --save
        - ""
        - --appendonly
        - "no"
        - --requirepass $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: argocd-redis
              key: auth
        {{- with (concat .Values.global.env .Values.redis.env) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.redis.envFrom }}
        envFrom:
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if .Values.redis.livenessProbe.enabled }}
        livenessProbe:
          initialDelaySeconds: {{ .Values.redis.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.redis.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.redis.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.redis.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.redis.livenessProbe.failureThreshold }}
          exec:
            command:
              - sh
              - -c
              - /health/redis_liveness.sh
        {{- end }}
        {{- if .Values.redis.readinessProbe.enabled }}
        readinessProbe:
          initialDelaySeconds: {{ .Values.redis.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.redis.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.redis.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.redis.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.redis.readinessProbe.failureThreshold }}
          exec:
            command:
              - sh
              - -c
              - /health/redis_readiness.sh
        {{- end }}
        ports:
        - name: redis
          containerPort: {{ .Values.redis.containerPorts.redis }}
          protocol: TCP
        resources:
          {{- toYaml .Values.redis.resources | nindent 10 }}
        {{- with .Values.redis.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        volumeMounts:
          - mountPath: /health
            name: health
        {{- with .Values.redis.volumeMounts }}
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- if .Values.redis.exporter.enabled }}
      - name: metrics
        image: {{ .Values.redis.exporter.image.repository }}:{{ .Values.redis.exporter.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.redis.exporter.image.imagePullPolicy }}
        env:
        - name: REDIS_ADDR
          value: {{ printf "redis://localhost:%v" .Values.redis.containerPorts.redis }}
        - name: REDIS_EXPORTER_WEB_LISTEN_ADDRESS
          value: {{ printf "0.0.0.0:%v" .Values.redis.containerPorts.metrics }}
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: argocd-redis
              key: auth
        {{- with (concat .Values.global.env .Values.redis.exporter.env) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        ports:
        - name: metrics
          containerPort: {{ .Values.redis.containerPorts.metrics }}
          protocol: TCP
        {{- if .Values.redis.exporter.livenessProbe.enabled }}
        livenessProbe:
          httpGet:
            path: /metrics
            port: {{ .Values.redis.containerPorts.metrics }}
          initialDelaySeconds: {{ .Values.redis.exporter.livenessProbe.initialDelaySeconds }}
          timeoutSeconds: {{ .Values.redis.exporter.livenessProbe.timeoutSeconds }}
          periodSeconds: {{ .Values.redis.exporter.livenessProbe.periodSeconds }}
          successThreshold: {{ .Values.redis.exporter.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.redis.exporter.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.redis.exporter.readinessProbe.enabled }}
        readinessProbe:
          httpGet:
            path: /metrics
            port: {{ .Values.redis.containerPorts.metrics }}
          initialDelaySeconds: {{ .Values.redis.exporter.readinessProbe.initialDelaySeconds }}
          timeoutSeconds: {{ .Values.redis.exporter.readinessProbe.timeoutSeconds }}
          periodSeconds: {{ .Values.redis.exporter.readinessProbe.periodSeconds }}
          successThreshold: {{ .Values.redis.exporter.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.redis.exporter.readinessProbe.failureThreshold }}
        {{- end }}
        resources:
          {{- toYaml .Values.redis.exporter.resources | nindent 10 }}
        {{- with .Values.redis.exporter.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- end }}
      {{- with .Values.redis.extraContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.redis.initContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.redis.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.redis) }}
      affinity:
        {{-  trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            app.kubernetes.io/name: {{ include "argo-cd.name" $ }}-{{ $.Values.redis.name }}
        {{- end }}
        {{- end }}
      {{- end }}
      volumes:
        - name: health
          configMap:
            name: {{ include "argo-cd.redis.fullname" . }}-health-configmap
            defaultMode: 493
      {{- with .Values.redis.volumes }}
        {{- toYaml . | nindent 8}}
      {{- end }}
      {{- with .Values.redis.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.redis.dnsPolicy }}
{{- end }}
