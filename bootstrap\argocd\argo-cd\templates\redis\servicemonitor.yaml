{{- $redisHa := (index .Values "redis-ha") -}}
{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") .Values.redis.enabled (not $redisHa.enabled) .Values.redis.metrics.enabled .Values.redis.metrics.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "argo-cd.redis.fullname" . }}
  namespace: {{ default (include  "argo-cd.namespace" .) .Values.redis.metrics.serviceMonitor.namespace | quote }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
    {{- with .Values.redis.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.redis.metrics.serviceMonitor.additionalLabels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.redis.metrics.serviceMonitor.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - port: {{ .Values.redis.metrics.service.portName }}
      {{- with .Values.redis.metrics.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
      path: /metrics
      {{- with .Values.redis.metrics.serviceMonitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      honorLabels: {{ .Values.redis.metrics.serviceMonitor.honorLabels }}
      {{- with .Values.redis.metrics.serviceMonitor.scheme }}
      scheme: {{ . }}
      {{- end }}
      {{- with .Values.redis.metrics.serviceMonitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
      - {{ include "argo-cd.namespace" . }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 6 }}
{{- end }}
