# ArgoCD Configuration for Bootstrap Environment
# <PERSON><PERSON><PERSON> hình <PERSON>rgoCD cho môi trường bootstrap

# Global configuration
global:
  domain: argocd.local
  image:
    repository: quay.io/argoproj/argocd
    tag: "v2.13.2"
    imagePullPolicy: IfNotPresent

# ArgoCD Configs
configs:
  # ConfigMap for ArgoCD settings
  cm:
    # Enable insecure mode for bootstrap (disable TLS)
    "server.insecure": "true"
    # Enable local admin user
    "admin.enabled": "true"
    # Application instance label key
    "application.instanceLabelKey": "argocd.argoproj.io/instance"
    # Enable exec feature in UI
    "exec.enabled": "false"
    # Timeout settings
    "timeout.reconciliation": "180s"
    # Enable status badge
    "statusbadge.enabled": "true"
    # URL for ArgoCD server
    url: "http://argocd.local"

  # Parameters for ArgoCD
  params:
    # Enable insecure mode
    "server.insecure": true
    # Disable TLS
    "server.disable.auth": false
    # Application namespaces (empty = all namespaces)
    "application.namespaces": ""
    # Controller settings
    "controller.status.processors": 20
    "controller.operation.processors": 10
    "controller.self.heal.timeout.seconds": 5
    "controller.repo.server.timeout.seconds": 60

  # RBAC configuration
  rbac:
    # Default policy
    "policy.default": "role:readonly"
    # Admin users
    "policy.csv": |
      p, role:admin, applications, *, */*, allow
      p, role:admin, clusters, *, *, allow
      p, role:admin, repositories, *, *, allow
      g, argocd-admins, role:admin

  # Secret for initial admin password
  secret:
    # Admin password (change this in production!)
    argocdServerAdminPassword: "$2a$10$rRyBsGSHK6.uc8fntPwVIuLiTleHI3c4f2Bk/2OKADHuatQgjddra"  # password: admin123
    # GitHub webhook secret (optional)
    githubSecret: ""
    # GitLab webhook secret (optional)
    gitlabSecret: ""

# ArgoCD Server configuration
server:
  # Number of replicas
  replicas: 1

  # Service configuration
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080

  # Ingress configuration (disabled, will use Gateway API)
  ingress:
    enabled: false

  # Resources
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 512Mi

  # Additional arguments
  extraArgs:
    - --insecure

  # Environment variables
  env: []

# ArgoCD Application Controller
controller:
  # Number of replicas
  replicas: 1

  # Resources
  resources:
    requests:
      cpu: 250m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi

# ArgoCD Repo Server
repoServer:
  # Number of replicas
  replicas: 1

  # Resources
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

# ArgoCD ApplicationSet Controller
applicationSet:
  # Enable ApplicationSet controller
  enabled: true

  # Resources
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

# ArgoCD Notifications (disabled for bootstrap)
notifications:
  enabled: false

# ArgoCD Dex (disabled for bootstrap)
dex:
  enabled: false

# Redis configuration
redis:
  enabled: true
  
  # Resources for Redis
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

# Redis HA (disabled for bootstrap)
redis-ha:
  enabled: false

# External Redis (disabled, using internal)
externalRedis:
  host: ""
  port: 6379

# Service Account
serviceAccount:
  create: true
  name: "argocd-server"
  annotations: {}

# RBAC
rbac:
  create: true

# Security contexts
securityContext:
  runAsNonRoot: true
  runAsUser: 999
  runAsGroup: 999
  fsGroup: 999

# Node selector, affinity, tolerations
nodeSelector: {}
affinity: {}
tolerations: []
