apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bootstrap-platform
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"  # Deploy early in bootstrap phase
  labels:
    app.kubernetes.io/name: bootstrap-platform
    app.kubernetes.io/component: platform-config
    app.kubernetes.io/part-of: bootstrap
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: bootstrap/platform
    directory:
      recurse: false
      include: "*.yaml"
  destination:
    server: https://kubernetes.default.svc
    namespace: bootstrap
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
