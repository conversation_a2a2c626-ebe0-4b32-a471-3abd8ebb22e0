apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-server
  namespace: bootstrap
  labels:
    app: argocd
    component: server
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: web
  hostnames:
  - argocd.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: argocd-bootstrap-server
      port: 80
