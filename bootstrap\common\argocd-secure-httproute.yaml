apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-server-secure
  namespace: bootstrap
  labels:
    app: argocd
    component: server-secure
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: web
  hostnames:
  - argocd-secure.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: argocd-bootstrap-server
      port: 80