apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-ospgroup-tls
  namespace: bootstrap
spec:
  secretName: wildcard-ospgroup-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
    - "*.ospgroup.vn"
    - ospgroup.vn
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-credentials
            key: CF_API_KEY