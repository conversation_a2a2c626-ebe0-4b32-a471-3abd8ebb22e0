#!/bin/bash

# Bootstrap Services Installation Script
# Script cài đặt các dịch vụ nền tảng cho Kubernetes cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if KUBECONFIG is set
if [ -z "$KUBECONFIG" ]; then
    export KUBECONFIG=$(pwd)/.kube/config
    print_warning "KUBECONFIG not set, using $(pwd)/.kube/config"
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

# Check cluster connectivity
print_status "Checking cluster connectivity..."
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi
print_success "Connected to Kubernetes cluster"

# Create bootstrap namespace if not exists
print_status "Creating bootstrap namespace..."
kubectl apply -f bootstrap/common/namespace.yaml
print_success "Bootstrap namespace ready"

# Add Helm repositories
print_status "Adding Helm repositories..."
helm repo add hashicorp https://helm.releases.hashicorp.com
helm repo add traefik https://traefik.github.io/charts
helm repo add argo https://argoproj.github.io/argo-helm
helm repo update
print_success "Helm repositories updated"

# Install Vault
print_status "Installing HashiCorp Vault..."
if helm list -n bootstrap | grep -q vault-bootstrap; then
    print_warning "Vault already installed, upgrading..."
    helm upgrade vault-bootstrap ./bootstrap/vault/vault -n bootstrap -f ./bootstrap/vault/values.yaml
else
    helm install vault-bootstrap ./bootstrap/vault/vault -n bootstrap -f ./bootstrap/vault/values.yaml
fi

# Wait for Vault to be ready
print_status "Waiting for Vault to be ready..."
kubectl wait --for=condition=ready pod/vault-bootstrap-0 -n bootstrap --timeout=120s
print_success "Vault is ready"

# Install Traefik
print_status "Installing Traefik Gateway..."
if helm list -n bootstrap | grep -q traefik-bootstrap; then
    print_warning "Traefik already installed, upgrading..."
    helm upgrade traefik-bootstrap ./bootstrap/traefik/traefik -n bootstrap -f ./bootstrap/traefik/values.yaml
else
    helm install traefik-bootstrap ./bootstrap/traefik/traefik -n bootstrap -f ./bootstrap/traefik/values.yaml
fi

# Wait for Traefik to be ready
print_status "Waiting for Traefik to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=traefik -n bootstrap --timeout=120s
print_success "Traefik is ready"

# Install ArgoCD
print_status "Installing ArgoCD..."
if helm list -n bootstrap | grep -q argocd-bootstrap; then
    print_warning "ArgoCD already installed, upgrading..."
    helm upgrade argocd-bootstrap ./bootstrap/argocd/argo-cd -n bootstrap -f ./bootstrap/argocd/values.yaml
else
    helm install argocd-bootstrap ./bootstrap/argocd/argo-cd -n bootstrap -f ./bootstrap/argocd/values.yaml
fi

# Wait for ArgoCD to be ready
print_status "Waiting for ArgoCD to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=argocd-server -n bootstrap --timeout=300s
print_success "ArgoCD is ready"

# Display status
print_status "Checking all services status..."
kubectl get pods -n bootstrap

print_success "Bootstrap services installation completed!"
print_status "Next steps:"
echo "1. Check the services report: docs/bootstrap-services-report.md"
echo "2. Access ArgoCD UI via port-forward: kubectl port-forward svc/argocd-bootstrap-server -n bootstrap 8080:80"
echo "3. Login to ArgoCD with username: admin, password: admin123"
echo "4. Access Vault UI via port-forward: kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200"
echo "5. Login to Vault with root token: bootstrap-root-token"
