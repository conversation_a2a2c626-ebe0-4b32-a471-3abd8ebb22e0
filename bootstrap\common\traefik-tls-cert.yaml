apiVersion: v1
kind: Secret
metadata:
  name: traefik-default-cert
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
type: kubernetes.io/tls
data:
  # Self-signed certificate for *.ospgroup.vn
  # Generated with: openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/CN=*.ospgroup.vn"
  tls.crt: 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
  tls.key: 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