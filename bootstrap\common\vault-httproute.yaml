apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: vault-ui
  namespace: bootstrap
  labels:
    app: vault
    component: ui
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: web
  hostnames:
  - vault.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: vault-bootstrap-ui
      port: 8200
