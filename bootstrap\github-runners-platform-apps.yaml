# ArgoCD App of Apps for GitHub Runners Platform
# This application manages all GitHub runner applications using the App of Apps pattern

apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: github-runners-platform
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"  # Deploy platform first
  labels:
    app.kubernetes.io/name: github-runners
    app.kubernetes.io/component: platform
    app.kubernetes.io/part-of: github-runners
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: github-runners-apps
    directory:
      recurse: false
      include: "*.yaml"
      exclude: "TEMPLATE-*.yaml"  # Exclude template files
  destination:
    server: https://kubernetes.default.svc
    namespace: bootstrap
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
