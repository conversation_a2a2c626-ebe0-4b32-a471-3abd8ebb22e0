apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: github-runners-secrets
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"  # Deploy secrets before runners
  labels:
    app.kubernetes.io/name: github-runners
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: github-runners
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: github-runners-manifests
  destination:
    server: https://kubernetes.default.svc
    namespace: github-runners
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
