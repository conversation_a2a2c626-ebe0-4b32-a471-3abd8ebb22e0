apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: common-ospgroup-tls
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
type: kubernetes.io/tls
