apiVersion: v1
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: dockerhub-ospgroup-tls
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
type: kubernetes.io/tls
