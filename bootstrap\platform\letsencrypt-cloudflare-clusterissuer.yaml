apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-cloudflare-prod
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-cloudflare-prod
    solvers:
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-credentials
            key: CF_API_KEY
      selector:
        dnsZones:
        - "ospgroup.io.vn"