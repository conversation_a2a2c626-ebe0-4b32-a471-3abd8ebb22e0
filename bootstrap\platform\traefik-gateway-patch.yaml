apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: traefik-gateway
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  gatewayClassName: traefik
  listeners:
  - name: web
    port: 8000
    protocol: HTTP
    allowedRoutes:
      namespaces:
        from: All
  - name: websecure
    port: 8443
    protocol: HTTPS
    allowedRoutes:
      namespaces:
        from: All
    tls:
      mode: Terminate
      certificateRefs:
      # Specific certificate for common.ospgroup.io.vn (highest priority)
      - name: common-ospgroup-io-vn-tls-secret
        namespace: bootstrap
        group: ""
        kind: Secret
      # Wildcard certificate for *.ospgroup.io.vn
      - name: wildcard-ospgroup-io-vn-tls-secret
        namespace: bootstrap
        group: ""
        kind: Secret
      # Wildcard certificate for general use
      - name: wildcard-ospgroup-tls
        namespace: bootstrap
        group: ""
        kind: Secret
      # Specific certificate for wiki.ospgroup.vn
      - name: wiki-ospgroup-letsencrypt-tls
        namespace: bootstrap
        group: ""
        kind: Secret