apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: wiki-gateway
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  gatewayClassName: traefik
  listeners:
  - name: wiki-websecure
    port: 8443
    protocol: HTTPS
    hostname: "wiki.ospgroup.vn"
    tls:
      mode: Terminate
      certificateRefs:
      - name: wiki-ospgroup-letsencrypt-tls
        namespace: bootstrap
        group: ""
        kind: Secret
    allowedRoutes:
      namespaces:
        from: All