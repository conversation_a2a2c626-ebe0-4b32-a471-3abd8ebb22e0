# ReferenceGrant để cho phép Gateway trong bootstrap namespace
# truy cập certificate secret wildcard-ospgroup-io-vn-tls-secret
# ReferenceGrant phải đ<PERSON>c tạo trong namespace chứa secret (bootstrap)
apiVersion: gateway.networking.k8s.io/v1beta1
kind: ReferenceGrant
metadata:
  name: wildcard-ospgroup-io-vn-cert-access
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  from:
  - group: gateway.networking.k8s.io
    kind: Gateway
    namespace: bootstrap
  to:
  - group: ""
    kind: Secret
    name: wildcard-ospgroup-io-vn-tls-secret
  - group: ""
    kind: Secret
    name: common-ospgroup-io-vn-tls-secret
---
# ReferenceGrant cho HTTPRoute từ platform-services namespace
apiVersion: gateway.networking.k8s.io/v1beta1
kind: ReferenceGrant
metadata:
  name: platform-services-to-bootstrap-cert
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  from:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    namespace: platform-services
  to:
  - group: ""
    kind: Secret
    name: wildcard-ospgroup-io-vn-tls-secret
  - group: ""
    kind: Secret
    name: common-ospgroup-io-vn-tls-secret
