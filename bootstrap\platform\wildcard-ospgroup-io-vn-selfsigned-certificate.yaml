# Self-signed certificate tạm thời cho *.ospgroup.io.vn
# Sử dụng để test domain common.ospgroup.io.vn trong khi chờ Let's Encrypt certificate
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-ospgroup-io-vn-selfsigned
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  secretName: wildcard-ospgroup-io-vn-tls-secret
  issuerRef:
    name: selfsigned-issuer
    kind: ClusterIssuer
  dnsNames:
  - "*.ospgroup.io.vn"
  - "ospgroup.io.vn"
  - "common.ospgroup.io.vn"
---
# ClusterIssuer cho self-signed certificates
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: selfsigned-issuer
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  selfSigned: {}
