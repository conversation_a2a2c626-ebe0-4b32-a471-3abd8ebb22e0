apiVersion: v1
kind: Secret
metadata:
  name: wildcard-ospgroup-tls
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/component: tls-certificate
    app.kubernetes.io/name: wildcard-ospgroup-tls
    app.kubernetes.io/part-of: platform-services
type: kubernetes.io/tls
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  creationTimestamp: null
  name: wildcard-ospgroup-tls
  namespace: bootstrap
type: kubernetes.io/tls
