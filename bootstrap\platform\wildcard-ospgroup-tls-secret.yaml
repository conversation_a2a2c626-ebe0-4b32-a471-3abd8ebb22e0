apiVersion: v1
kind: Secret
metadata:
  name: wildcard-ospgroup-tls
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/component: tls-certificate
    app.kubernetes.io/name: wildcard-ospgroup-tls
    app.kubernetes.io/part-of: platform-services
type: kubernetes.io/tls
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  creationTimestamp: null
  name: wildcard-ospgroup-tls
  namespace: bootstrap
type: kubernetes.io/tls
