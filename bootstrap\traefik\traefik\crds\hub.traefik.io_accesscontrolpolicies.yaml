---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: accesscontrolpolicies.hub.traefik.io
spec:
  group: hub.traefik.io
  names:
    kind: AccessControlPolicy
    listKind: AccessControlPolicyList
    plural: accesscontrolpolicies
    singular: accesscontrolpolicy
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: AccessControlPolicy defines an access control policy.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: AccessControlPolicySpec configures an access control policy.
            properties:
              apiKey:
                description: AccessControlPolicyAPIKey configure an APIKey control
                  policy.
                properties:
                  forwardHeaders:
                    additionalProperties:
                      type: string
                    description: ForwardHeaders instructs the middleware to forward
                      key metadata as header values upon successful authentication.
                    type: object
                  keySource:
                    description: KeySource defines how to extract API keys from requests.
                    properties:
                      cookie:
                        description: Cookie is the name of a cookie.
                        type: string
                      header:
                        description: Header is the name of a header.
                        type: string
                      headerAuthScheme:
                        description: |-
                          HeaderAuthScheme sets an optional auth scheme when Header is set to "Authorization".
                          If set, this scheme is removed from the token, and all requests not including it are dropped.
                        type: string
                      query:
                        description: Query is the name of a query parameter.
                        type: string
                    type: object
                  keys:
                    description: Keys define the set of authorized keys to access
                      a protected resource.
                    items:
                      description: AccessControlPolicyAPIKeyKey defines an API key.
                      properties:
                        id:
                          description: ID is the unique identifier of the key.
                          type: string
                        metadata:
                          additionalProperties:
                            type: string
                          description: Metadata holds arbitrary metadata for this
                            key, can be used by ForwardHeaders.
                          type: object
                        value:
                          description: Value is the SHAKE-256 hash (using 64 bytes)
                            of the API key.
                          type: string
                      required:
                      - id
                      - value
                      type: object
                    type: array
                required:
                - keySource
                type: object
              basicAuth:
                description: AccessControlPolicyBasicAuth holds the HTTP basic authentication
                  configuration.
                properties:
                  forwardUsernameHeader:
                    type: string
                  realm:
                    type: string
                  stripAuthorizationHeader:
                    type: boolean
                  users:
                    items:
                      type: string
                    type: array
                type: object
              jwt:
                description: AccessControlPolicyJWT configures a JWT access control
                  policy.
                properties:
                  claims:
                    type: string
                  forwardHeaders:
                    additionalProperties:
                      type: string
                    type: object
                  jwksFile:
                    type: string
                  jwksUrl:
                    type: string
                  publicKey:
                    type: string
                  signingSecret:
                    type: string
                  signingSecretBase64Encoded:
                    type: boolean
                  stripAuthorizationHeader:
                    type: boolean
                  tokenQueryKey:
                    type: string
                type: object
              oAuthIntro:
                description: AccessControlOAuthIntro configures an OAuth 2.0 Token
                  Introspection access control policy.
                properties:
                  claims:
                    type: string
                  clientConfig:
                    description: AccessControlOAuthIntroClientConfig configures the
                      OAuth 2.0 client for issuing token introspection requests.
                    properties:
                      headers:
                        additionalProperties:
                          type: string
                        description: Headers to set when sending requests to the Authorization
                          Server.
                        type: object
                      maxRetries:
                        default: 3
                        description: MaxRetries defines the number of retries for
                          introspection requests.
                        type: integer
                      timeoutSeconds:
                        default: 5
                        description: TimeoutSeconds configures the maximum amount
                          of seconds to wait before giving up on requests.
                        type: integer
                      tls:
                        description: TLS configures TLS communication with the Authorization
                          Server.
                        properties:
                          ca:
                            description: CA sets the CA bundle used to sign the Authorization
                              Server certificate.
                            type: string
                          insecureSkipVerify:
                            description: |-
                              InsecureSkipVerify skips the Authorization Server certificate validation.
                              For testing purposes only, do not use in production.
                            type: boolean
                        type: object
                      tokenTypeHint:
                        description: |-
                          TokenTypeHint is a hint to pass to the Authorization Server.
                          See https://tools.ietf.org/html/rfc7662#section-2.1 for more information.
                        type: string
                      url:
                        description: URL of the Authorization Server.
                        type: string
                    required:
                    - url
                    type: object
                  forwardHeaders:
                    additionalProperties:
                      type: string
                    type: object
                  tokenSource:
                    description: |-
                      TokenSource describes how to extract tokens from HTTP requests.
                      If multiple sources are set, the order is the following: header > query > cookie.
                    properties:
                      cookie:
                        description: Cookie is the name of a cookie.
                        type: string
                      header:
                        description: Header is the name of a header.
                        type: string
                      headerAuthScheme:
                        description: |-
                          HeaderAuthScheme sets an optional auth scheme when Header is set to "Authorization".
                          If set, this scheme is removed from the token, and all requests not including it are dropped.
                        type: string
                      query:
                        description: Query is the name of a query parameter.
                        type: string
                    type: object
                required:
                - clientConfig
                - tokenSource
                type: object
              oidc:
                description: AccessControlPolicyOIDC holds the OIDC authentication
                  configuration.
                properties:
                  authParams:
                    additionalProperties:
                      type: string
                    type: object
                  claims:
                    type: string
                  clientId:
                    type: string
                  disableAuthRedirectionPaths:
                    items:
                      type: string
                    type: array
                  forwardHeaders:
                    additionalProperties:
                      type: string
                    type: object
                  issuer:
                    type: string
                  logoutUrl:
                    type: string
                  redirectUrl:
                    type: string
                  scopes:
                    items:
                      type: string
                    type: array
                  secret:
                    description: |-
                      SecretReference represents a Secret Reference. It has enough information to retrieve secret
                      in any namespace
                    properties:
                      name:
                        description: name is unique within a namespace to reference
                          a secret resource.
                        type: string
                      namespace:
                        description: namespace defines the space within which the
                          secret name must be unique.
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  session:
                    description: Session holds session configuration.
                    properties:
                      domain:
                        type: string
                      path:
                        type: string
                      refresh:
                        type: boolean
                      sameSite:
                        type: string
                      secure:
                        type: boolean
                    type: object
                  stateCookie:
                    description: StateCookie holds state cookie configuration.
                    properties:
                      domain:
                        type: string
                      path:
                        type: string
                      sameSite:
                        type: string
                      secure:
                        type: boolean
                    type: object
                type: object
              oidcGoogle:
                description: AccessControlPolicyOIDCGoogle holds the Google OIDC authentication
                  configuration.
                properties:
                  authParams:
                    additionalProperties:
                      type: string
                    type: object
                  clientId:
                    type: string
                  emails:
                    description: Emails are the allowed emails to connect.
                    items:
                      type: string
                    minItems: 1
                    type: array
                  forwardHeaders:
                    additionalProperties:
                      type: string
                    type: object
                  logoutUrl:
                    type: string
                  redirectUrl:
                    type: string
                  secret:
                    description: |-
                      SecretReference represents a Secret Reference. It has enough information to retrieve secret
                      in any namespace
                    properties:
                      name:
                        description: name is unique within a namespace to reference
                          a secret resource.
                        type: string
                      namespace:
                        description: namespace defines the space within which the
                          secret name must be unique.
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  session:
                    description: Session holds session configuration.
                    properties:
                      domain:
                        type: string
                      path:
                        type: string
                      refresh:
                        type: boolean
                      sameSite:
                        type: string
                      secure:
                        type: boolean
                    type: object
                  stateCookie:
                    description: StateCookie holds state cookie configuration.
                    properties:
                      domain:
                        type: string
                      path:
                        type: string
                      sameSite:
                        type: string
                      secure:
                        type: boolean
                    type: object
                type: object
            type: object
          status:
            description: The current status of this access control policy.
            properties:
              specHash:
                type: string
              syncedAt:
                format: date-time
                type: string
              version:
                type: string
            type: object
        type: object
    served: true
    storage: true
