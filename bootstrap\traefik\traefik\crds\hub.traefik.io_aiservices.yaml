---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: aiservices.hub.traefik.io
spec:
  group: hub.traefik.io
  names:
    kind: AIService
    listKind: AIServiceList
    plural: aiservices
    singular: aiservice
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: AIService is a Kubernetes-like Service to interact with a text-based
          LLM provider. It defines the parameters and credentials required to interact
          with various LLM providers.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: The desired behavior of this AIService.
            properties:
              anthropic:
                description: Anthropic configures Anthropic backend.
                properties:
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  token:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                type: object
              azureOpenai:
                description: AzureOpenAI configures AzureOpenAI.
                properties:
                  apiKeySecret:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                  baseUrl:
                    type: string
                  deploymentName:
                    type: string
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                required:
                - baseUrl
                - deploymentName
                type: object
              bedrock:
                description: Bedrock configures Bedrock backend.
                properties:
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  region:
                    type: string
                  systemMessage:
                    type: boolean
                type: object
              cohere:
                description: Cohere configures Cohere backend.
                properties:
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  token:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                type: object
              deepSeek:
                description: DeepSeek configures DeepSeek.
                properties:
                  baseUrl:
                    type: string
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  token:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                type: object
              gemini:
                description: Gemini configures Gemini backend.
                properties:
                  apiKey:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                type: object
              mistral:
                description: Mistral configures Mistral AI backend.
                properties:
                  apiKey:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                type: object
              ollama:
                description: Ollama configures Ollama backend.
                properties:
                  baseUrl:
                    type: string
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                required:
                - baseUrl
                type: object
              openai:
                description: OpenAI configures OpenAI.
                properties:
                  baseUrl:
                    type: string
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  token:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                type: object
              qWen:
                description: QWen configures QWen.
                properties:
                  baseUrl:
                    type: string
                  model:
                    type: string
                  params:
                    description: Params holds the LLM hyperparameters.
                    properties:
                      frequencyPenalty:
                        type: number
                      maxTokens:
                        type: integer
                      presencePenalty:
                        type: number
                      temperature:
                        type: number
                      topP:
                        type: number
                    type: object
                  token:
                    description: SecretReference references a kubernetes secret.
                    properties:
                      secretName:
                        maxLength: 253
                        type: string
                    required:
                    - secretName
                    type: object
                type: object
            type: object
        type: object
    served: true
    storage: true
