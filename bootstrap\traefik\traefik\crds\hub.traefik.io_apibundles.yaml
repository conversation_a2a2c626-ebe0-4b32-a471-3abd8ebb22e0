---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: apibundles.hub.traefik.io
spec:
  group: hub.traefik.io
  names:
    kind: APIBundle
    listKind: APIBundleList
    plural: apibundles
    singular: apibundle
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: APIBundle defines a set of APIs.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: The desired behavior of this APIBundle.
            properties:
              apiSelector:
                description: |-
                  APISelector selects the APIs that will be accessible to the configured audience.
                  Multiple APIBundles can select the same set of APIs.
                  This field is optional and follows standard label selector semantics.
                  An empty APISelector matches any API.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: |-
                        A label selector requirement is a selector that contains values, a key, and an operator that
                        relates the key and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: |-
                            operator represents a key's relationship to a set of values.
                            Valid operators are In, NotIn, Exists and DoesNotExist.
                          type: string
                        values:
                          description: |-
                            values is an array of string values. If the operator is In or NotIn,
                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                            the values array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: |-
                      matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                      map is equivalent to an element of matchExpressions, whose key field is "key", the
                      operator is "In", and the values array contains only "value". The requirements are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              apis:
                description: |-
                  APIs defines a set of APIs that will be accessible to the configured audience.
                  Multiple APIBundles can select the same APIs.
                  When combined with APISelector, this set of APIs is appended to the matching APIs.
                items:
                  description: APIReference references an API.
                  properties:
                    name:
                      description: Name of the API.
                      maxLength: 253
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 100
                type: array
                x-kubernetes-validations:
                - message: duplicated apis
                  rule: self.all(x, self.exists_one(y, x.name == y.name))
              title:
                description: Title is the human-readable name of the APIBundle that
                  will be used on the portal.
                maxLength: 253
                type: string
            type: object
          status:
            description: The current status of this APIBundle.
            properties:
              hash:
                description: Hash is a hash representing the APIBundle.
                type: string
              syncedAt:
                format: date-time
                type: string
              version:
                type: string
            type: object
        type: object
    served: true
    storage: true
