---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: apiportals.hub.traefik.io
spec:
  group: hub.traefik.io
  names:
    kind: APIPortal
    listKind: APIPortalList
    plural: apiportals
    singular: apiportal
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: APIPortal defines a developer portal for accessing the documentation
          of APIs.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: The desired behavior of this APIPortal.
            properties:
              auth:
                description: Auth references the APIPortalAuth resource for authentication
                  configuration.
                properties:
                  name:
                    description: Name is the name of the APIPortalAuth resource.
                    maxLength: 253
                    type: string
                required:
                - name
                type: object
              description:
                description: Description of the APIPortal.
                type: string
              title:
                description: Title is the public facing name of the APIPortal.
                type: string
              trustedUrls:
                description: TrustedURLs are the urls that are trusted by the OAuth
                  2.0 authorization server.
                items:
                  type: string
                maxItems: 1
                minItems: 1
                type: array
                x-kubernetes-validations:
                - message: must be a valid URLs
                  rule: self.all(x, isURL(x))
              ui:
                description: UI holds the UI customization options.
                properties:
                  logoUrl:
                    description: LogoURL is the public URL of the logo.
                    type: string
                type: object
            required:
            - trustedUrls
            type: object
          status:
            description: The current status of this APIPortal.
            properties:
              hash:
                description: Hash is a hash representing the APIPortal.
                type: string
              oidc:
                description: OIDC is the OIDC configuration for accessing the exposed
                  APIPortal WebUI.
                properties:
                  clientId:
                    description: ClientID is the OIDC ClientID for accessing the exposed
                      APIPortal WebUI.
                    type: string
                  companyClaim:
                    description: CompanyClaim is the name of the JWT claim containing
                      the user company.
                    type: string
                  emailClaim:
                    description: EmailClaim is the name of the JWT claim containing
                      the user email.
                    type: string
                  firstnameClaim:
                    description: FirstnameClaim is the name of the JWT claim containing
                      the user firstname.
                    type: string
                  generic:
                    description: Generic indicates whether or not the APIPortal authentication
                      relies on Generic OIDC.
                    type: boolean
                  groupsClaim:
                    description: GroupsClaim is the name of the JWT claim containing
                      the user groups.
                    type: string
                  issuer:
                    description: Issuer is the OIDC issuer for accessing the exposed
                      APIPortal WebUI.
                    type: string
                  lastnameClaim:
                    description: LastnameClaim is the name of the JWT claim containing
                      the user lastname.
                    type: string
                  scopes:
                    description: Scopes is the OIDC scopes for getting user attributes
                      during the authentication to the exposed APIPortal WebUI.
                    type: string
                  secretName:
                    description: SecretName is the name of the secret containing the
                      OIDC ClientSecret for accessing the exposed APIPortal WebUI.
                    type: string
                  syncedAttributes:
                    description: SyncedAttributes configure the user attributes to
                      sync.
                    items:
                      type: string
                    type: array
                  userIdClaim:
                    description: UserIDClaim is the name of the JWT claim containing
                      the user ID.
                    type: string
                type: object
              syncedAt:
                format: date-time
                type: string
              version:
                type: string
            type: object
        type: object
    served: true
    storage: true
