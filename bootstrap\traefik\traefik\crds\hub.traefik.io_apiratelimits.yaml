---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: apiratelimits.hub.traefik.io
spec:
  group: hub.traefik.io
  names:
    kind: APIRateLimit
    listKind: APIRateLimitList
    plural: apiratelimits
    singular: apiratelimit
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: APIRateLimit defines how group of consumers are rate limited
          on a set of APIs.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: The desired behavior of this APIRateLimit.
            properties:
              apiSelector:
                description: |-
                  APISelector selects the APIs that will be rate limited.
                  Multiple APIRateLimits can select the same set of APIs.
                  This field is optional and follows standard label selector semantics.
                  An empty APISelector matches any API.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: |-
                        A label selector requirement is a selector that contains values, a key, and an operator that
                        relates the key and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: |-
                            operator represents a key's relationship to a set of values.
                            Valid operators are In, NotIn, Exists and DoesNotExist.
                          type: string
                        values:
                          description: |-
                            values is an array of string values. If the operator is In or NotIn,
                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                            the values array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: |-
                      matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                      map is equivalent to an element of matchExpressions, whose key field is "key", the
                      operator is "In", and the values array contains only "value". The requirements are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              apis:
                description: |-
                  APIs defines a set of APIs that will be rate limited.
                  Multiple APIRateLimits can select the same APIs.
                  When combined with APISelector, this set of APIs is appended to the matching APIs.
                items:
                  description: APIReference references an API.
                  properties:
                    name:
                      description: Name of the API.
                      maxLength: 253
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 100
                type: array
                x-kubernetes-validations:
                - message: duplicated apis
                  rule: self.all(x, self.exists_one(y, x.name == y.name))
              everyone:
                description: |-
                  Everyone indicates that all users will, by default, be rate limited with this configuration.
                  If an APIRateLimit explicitly target a group, the default rate limit will be ignored.
                type: boolean
              groups:
                description: |-
                  Groups are the consumer groups that will be rate limited.
                  Multiple APIRateLimits can target the same set of consumer groups, the most restrictive one applies.
                  When a consumer belongs to multiple groups, the least restrictive APIRateLimit applies.
                items:
                  type: string
                type: array
              limit:
                description: Limit is the maximum number of token in the bucket.
                type: integer
                x-kubernetes-validations:
                - message: must be a positive number
                  rule: self >= 0
              period:
                description: Period is the unit of time for the Limit.
                format: duration
                type: string
                x-kubernetes-validations:
                - message: must be between 1s and 1h
                  rule: self >= duration('1s') && self <= duration('1h')
              strategy:
                description: |-
                  Strategy defines how the bucket state will be synchronized between the different Traefik Hub instances.
                  It can be, either "local" or "distributed".
                enum:
                - local
                - distributed
                type: string
            required:
            - limit
            type: object
            x-kubernetes-validations:
            - message: groups and everyone are mutually exclusive
              rule: '(has(self.everyone) && has(self.groups)) ? !(self.everyone &&
                self.groups.size() > 0) : true'
          status:
            description: The current status of this APIRateLimit.
            properties:
              hash:
                description: Hash is a hash representing the APIRateLimit.
                type: string
              syncedAt:
                format: date-time
                type: string
              version:
                type: string
            type: object
        type: object
    served: true
    storage: true
