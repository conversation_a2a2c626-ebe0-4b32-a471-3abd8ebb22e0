---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: ingressrouteudps.traefik.io
spec:
  group: traefik.io
  names:
    kind: IngressRouteUDP
    listKind: IngressRouteUDPList
    plural: ingressrouteudps
    singular: ingressrouteudp
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: IngressRouteUDP is a CRD implementation of a Traefik UDP Router.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: IngressRouteUDPSpec defines the desired state of a IngressRouteUDP.
            properties:
              entryPoints:
                description: |-
                  EntryPoints defines the list of entry point names to bind to.
                  Entry points have to be configured in the static configuration.
                  More info: https://doc.traefik.io/traefik/v3.5/routing/entrypoints/
                  Default: all.
                items:
                  type: string
                type: array
              routes:
                description: Routes defines the list of routes.
                items:
                  description: RouteUDP holds the UDP route configuration.
                  properties:
                    services:
                      description: Services defines the list of UDP services.
                      items:
                        description: ServiceUDP defines an upstream UDP service to
                          proxy traffic to.
                        properties:
                          name:
                            description: Name defines the name of the referenced Kubernetes
                              Service.
                            type: string
                          namespace:
                            description: Namespace defines the namespace of the referenced
                              Kubernetes Service.
                            type: string
                          nativeLB:
                            description: |-
                              NativeLB controls, when creating the load-balancer,
                              whether the LB's children are directly the pods IPs or if the only child is the Kubernetes Service clusterIP.
                              The Kubernetes Service itself does load-balance to the pods.
                              By default, NativeLB is false.
                            type: boolean
                          nodePortLB:
                            description: |-
                              NodePortLB controls, when creating the load-balancer,
                              whether the LB's children are directly the nodes internal IPs using the nodePort when the service type is NodePort.
                              It allows services to be reachable when Traefik runs externally from the Kubernetes cluster but within the same network of the nodes.
                              By default, NodePortLB is false.
                            type: boolean
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            description: |-
                              Port defines the port of a Kubernetes Service.
                              This can be a reference to a named port.
                            x-kubernetes-int-or-string: true
                          weight:
                            description: Weight defines the weight used when balancing
                              requests between multiple Kubernetes Service.
                            minimum: 0
                            type: integer
                        required:
                        - name
                        - port
                        type: object
                      type: array
                  type: object
                type: array
            required:
            - routes
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
