---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: tlsstores.traefik.io
spec:
  group: traefik.io
  names:
    kind: TLSStore
    listKind: TLSStoreList
    plural: tlsstores
    singular: tlsstore
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          TLSStore is the CRD implementation of a Traefik TLS Store.
          For the time being, only the TLSStore named default is supported.
          This means that you cannot have two stores that are named default in different Kubernetes namespaces.
          More info: https://doc.traefik.io/traefik/v3.5/https/tls/#certificates-stores
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: TLSStoreSpec defines the desired state of a TLSStore.
            properties:
              certificates:
                description: Certificates is a list of secret names, each secret holding
                  a key/certificate pair to add to the store.
                items:
                  description: Certificate holds a secret name for the TLSStore resource.
                  properties:
                    secretName:
                      description: SecretName is the name of the referenced Kubernetes
                        Secret to specify the certificate details.
                      type: string
                  required:
                  - secretName
                  type: object
                type: array
              defaultCertificate:
                description: DefaultCertificate defines the default certificate configuration.
                properties:
                  secretName:
                    description: SecretName is the name of the referenced Kubernetes
                      Secret to specify the certificate details.
                    type: string
                required:
                - secretName
                type: object
              defaultGeneratedCert:
                description: DefaultGeneratedCert defines the default generated certificate
                  configuration.
                properties:
                  domain:
                    description: Domain is the domain definition for the DefaultCertificate.
                    properties:
                      main:
                        description: Main defines the main domain name.
                        type: string
                      sans:
                        description: SANs defines the subject alternative domain names.
                        items:
                          type: string
                        type: array
                    type: object
                  resolver:
                    description: Resolver is the name of the resolver that will be
                      used to issue the DefaultCertificate.
                    type: string
                type: object
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
