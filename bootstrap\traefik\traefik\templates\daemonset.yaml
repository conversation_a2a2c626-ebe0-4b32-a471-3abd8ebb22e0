{{- if and .Values.deployment.enabled (eq .Values.deployment.kind "DaemonSet") -}}
  {{- with .Values.additionalArguments -}}
    {{- range . -}}
      {{- if contains ".acme." . -}}
        {{- fail (printf "ACME functionality is not supported when running Traefik as a DaemonSet") -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
  {{- if eq (default .Chart.AppVersion .Values.image.tag) "latest" }}
    {{- fail "\n\n ERROR: latest tag should not be used" }}
  {{- end }}
  {{- with .Values.updateStrategy }}
    {{- if and (eq (.type) "RollingUpdate") (.rollingUpdate) }}
      {{- if not (contains "%" (toString .rollingUpdate.maxUnavailable)) }}
        {{- if and ($.Values.hostNetwork) (lt (float64 .rollingUpdate.maxUnavailable) 1.0) }}
          {{- fail "maxUnavailable should be greater than 1 when using hostNetwork." }}
        {{- end }}
      {{- end }}
    {{- end }}
  {{- end }}

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "traefik.fullname" . }}
  namespace: {{ template "traefik.namespace" . }}
  labels:
  {{- include "traefik.labels" . | nindent 4 }}
  {{- with .Values.deployment.labels }}
  {{- toYaml . | nindent 4 }}
  {{- end }}
  annotations:
  {{- if and .Values.providers.file.enabled (not .Values.providers.file.watch) }}
    checksum/traefik-dynamic-conf: {{ include (print $.Template.BasePath "/provider-file-cm.yaml") . | sha256sum }}
  {{- end }}
  {{- with .Values.deployment.annotations }}
  {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "traefik.labelselector" . | nindent 6 }}
  {{- with .Values.updateStrategy }}
  updateStrategy:
    type: {{ .type }}
    {{- if (eq .type "RollingUpdate") }}
    rollingUpdate:
      maxUnavailable: {{ .rollingUpdate.maxUnavailable }}
      maxSurge: {{ .rollingUpdate.maxSurge }}
    {{- end }}
  {{- end }}
  minReadySeconds: {{ .Values.deployment.minReadySeconds }}
  {{- if .Values.deployment.revisionHistoryLimit }}
  revisionHistoryLimit: {{ .Values.deployment.revisionHistoryLimit }}
  {{- end }}
  template: {{ template "traefik.podTemplate" . }}
{{- end -}}
