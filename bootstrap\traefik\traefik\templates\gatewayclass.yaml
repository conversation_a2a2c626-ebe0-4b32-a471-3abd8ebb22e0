{{- if and (.Values.gatewayClass).enabled (.Values.providers.kubernetesGateway).enabled }}
---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: {{ default "traefik" .Values.gatewayClass.name }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
    {{- with .Values.gatewayClass.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  controllerName: traefik.io/gateway-controller
{{- end }}
