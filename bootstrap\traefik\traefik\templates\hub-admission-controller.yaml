{{- if .Values.hub.token -}}
{{- if and .Values.hub.apimanagement.enabled (not .Values.hub.offline) }}
{{- $cert := include "traefik-hub.webhook_cert" . | fromYaml }}
---
apiVersion: v1
kind: Secret
type: kubernetes.io/tls
metadata:
  name: {{ .Values.hub.apimanagement.admission.secretName }}
  namespace: {{ template "traefik.namespace" . }}
  labels:
  {{- include "traefik.labels" . | nindent 4 }}
data:
  tls.crt: {{ $cert.Cert }}
  tls.key: {{ $cert.Key  }}

---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: hub-acp-{{ template "traefik.instance-name" . }}
  labels:
  {{- include "traefik.labels" . | nindent 4 }}
webhooks:
  - name: admission.traefik.svc
    clientConfig:
      service:
        name: admission
        namespace: {{ template "traefik.namespace" . }}
        path: /acp
      caBundle: {{ $cert.Cert }}
    sideEffects: None
    admissionReviewVersions:
      - v1
    rules:
      - operations:
          - CREATE
          - UPDATE
          - DELETE
        apiGroups:
          - hub.traefik.io
        apiVersions:
          - v1alpha1
        resources:
          - accesscontrolpolicies

---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: hub-api-{{ template "traefik.instance-name" . }}
  labels:
  {{- include "traefik.labels" . | nindent 4 }}
webhooks:
  {{- $resources := list
      (dict "name" "hub-agent.traefik.api"                  "endpoint" "/api"                   "resource" "apis")
      (dict "name" "hub-agent.traefik.bundle"               "endpoint" "/api-bundle"            "resource" "apibundles")
      (dict "name" "hub-agent.traefik.catalog-item"         "endpoint" "/api-catalog-item"      "resource" "apicatalogitems")
      (dict "name" "hub-agent.traefik.managed-subscription" "endpoint" "/managed-subscription"  "resource" "managedsubscriptions")
      (dict "name" "hub-agent.traefik.plan"                 "endpoint" "/api-plan"              "resource" "apiplans")
      (dict "name" "hub-agent.traefik.portal"               "endpoint" "/api-portal"            "resource" "apiportals")
      (dict "name" "hub-agent.traefik.version"              "endpoint" "/api-version"           "resource" "apiversions")
  }}
  {{- range $resources }}
  - name: hub-agent.traefik.{{ .name }}
    clientConfig:
      service:
        name: admission
        namespace: {{ template "traefik.namespace" $ }}
        path: {{ .endpoint }}
      caBundle: {{ $cert.Cert }}
    sideEffects: None
    admissionReviewVersions:
      - v1
    rules:
      - operations:
          - CREATE
          - UPDATE
          - DELETE
        apiGroups:
          - hub.traefik.io
        apiVersions:
          - v1alpha1
        resources:
          - {{ .resource }}
    {{- if $.Values.hub.namespaces }}
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: In
          values:
            {{- toYaml (uniq (concat (include "traefik.namespace" $ | list) $.Values.hub.namespaces)) | nindent 12 }}
    {{- end }}
  {{- end }}

---
apiVersion: v1
kind: Service
metadata:
  name: admission
  namespace: {{ template "traefik.namespace" . }}
  labels:
  {{- include "traefik.labels" . | nindent 4 }}
spec:
  ports:
    - name: https
      port: 443
      targetPort: admission
  selector:
  {{- include "traefik.labelselector" . | nindent 4 }}
{{- end -}}
{{- end -}}
