{{- if .Values.ingressClass.enabled -}}
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  annotations:
    ingressclass.kubernetes.io/is-default-class: {{ .Values.ingressClass.isDefaultClass | quote }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
  name: {{ .Values.ingressClass.name | default (include "traefik.fullname" .) }}
spec:
  controller: traefik.io/ingress-controller
{{- end -}}
