{{ range $name, $config := .Values.ingressRoute }}
{{ if $config.enabled }}
{{ $ingressClassAnnotations := dict }}
{{- if and $.Values.ingressClass.enabled $.Values.providers.kubernetesCRD.enabled $.Values.providers.kubernetesCRD.ingressClass }}
  {{ $ingressClassAnnotations = dict "kubernetes.io/ingress.class" $.Values.providers.kubernetesCRD.ingressClass }}
{{- end }}
{{ $annotations := merge $ingressClassAnnotations (default $config.annotations dict) }}
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: {{ $.Release.Name }}-{{ $name }}
  namespace: {{ template "traefik.namespace" $ }}
  {{- with $annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "traefik.labels" $ | nindent 4 }}
    {{- with $config.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  entryPoints:
  {{- range $config.entryPoints }}
  - {{ . }}
  {{- end }}
  routes:
  - match: {{ $config.matchRule }}
    kind: Rule
    {{- with $config.services }}
    services:
      {{- toYaml . | nindent 6 }}
    {{- end -}}
    {{- with $config.middlewares }}
    middlewares:
      {{- toYaml . | nindent 6 }}
    {{- end -}}

  {{- with $config.tls }}
  tls:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end -}}
{{ end }}
