{{- if .Values.podDisruptionBudget.enabled -}}
{{- if .Capabilities.APIVersions.Has "policy/v1/PodDisruptionBudget" }}
apiVersion: policy/v1
{{- else }}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "traefik.fullname" . }}
  namespace: {{ template "traefik.namespace" . }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "traefik.labelselector" . | nindent 6 }}
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
{{- end -}}
