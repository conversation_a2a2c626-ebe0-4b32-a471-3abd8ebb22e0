{{- if .Values.metrics.prometheus }}
{{- if (.Values.metrics.prometheus.prometheusRule).enabled }}
  {{- if (not (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1")) }}
    {{- if (not (.Values.metrics.prometheus.disableAPICheck)) }}
      {{- fail "ERROR: You have to deploy monitoring.coreos.com/v1 first" }}
    {{- end }}
  {{- end }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "traefik.fullname" . }}
  namespace: {{ .Values.metrics.prometheus.prometheusRule.namespace | default (include "traefik.namespace" .) }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
    {{- with .Values.metrics.prometheus.prometheusRule.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- if .Values.metrics.prometheus.prometheusRule.rules }}
  groups:
  - name: {{ template "traefik.name" $ }}
    rules:
    {{- with .Values.metrics.prometheus.prometheusRule.rules }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}
