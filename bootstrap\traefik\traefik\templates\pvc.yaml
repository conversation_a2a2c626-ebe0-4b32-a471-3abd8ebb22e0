{{- if and .Values.persistence.enabled (not .Values.persistence.existingClaim) -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ template "traefik.fullname" . }}
  namespace: {{ template "traefik.namespace" . }}
  annotations:
  {{- with .Values.persistence.annotations  }}
  {{ toYaml . | nindent 4 }}
  {{- end }}
    helm.sh/resource-policy: keep
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.size | quote }}
  {{- if .Values.persistence.storageClass }}
  storageClassName: {{ .Values.persistence.storageClass | quote }}
  {{- end }}
  {{- if .Values.persistence.volumeName }}
  volumeName: {{ .Values.persistence.volumeName | quote }}
  {{- end }}
{{- end -}}
