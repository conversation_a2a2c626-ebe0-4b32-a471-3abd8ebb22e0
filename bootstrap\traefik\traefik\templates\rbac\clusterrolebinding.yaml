{{- if and .Values.rbac.enabled (not .Values.rbac.namespaced) }}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "traefik.clusterRoleName" . }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "traefik.clusterRoleName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "traefik.serviceAccountName" . }}
    namespace: {{ template "traefik.namespace" . }}
{{- end -}}
