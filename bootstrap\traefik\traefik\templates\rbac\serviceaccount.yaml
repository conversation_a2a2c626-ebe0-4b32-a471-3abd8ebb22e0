{{- if not .Values.serviceAccount.name -}}
kind: ServiceAccount
apiVersion: v1
metadata:
  name: {{ include "traefik.serviceAccountName" . }}
  namespace: {{ template "traefik.namespace" . }}
  labels:
    {{- include "traefik.labels" . | nindent 4 }}
  annotations:
  {{- with .Values.serviceAccountAnnotations }}
  {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end -}}
