{{- $services := .Values.service.additionalServices -}}
{{- $services = set $services "default" (omit .Values.service "additionalServices") }}

{{- range $name, $service := $services -}}
{{- if ne $service.enabled false -}}

{{- $fullname := include "traefik.service-name" (dict "root" $ "name" $name) }}

{{- $tcpPorts := dict -}}
{{- $udpPorts := dict -}}
{{- $exposedPorts := false -}}
{{- range $portName, $config := $.Values.ports -}}
  {{- if $config -}}
    {{- if ($config.http3).enabled -}}
      {{- if (not $config.tls.enabled) -}}
        {{- fail "ERROR: You cannot enable http3 without enabling tls" -}}
      {{- end -}}
      {{ $udpConfig := deepCopy $config -}}
      {{ $_ := set $udpConfig "protocol" "UDP" -}}
      {{ $_ := set $udpConfig "exposedPort" (default $config.exposedPort $config.http3.advertisedPort) -}}
      {{- if (not $service.single) }}
        {{ $_ := set $udpPorts (printf "%s-http3" $portName) $udpConfig -}}
      {{- else }}
        {{ $_ := set $tcpPorts (printf "%s-http3" $portName) $udpConfig -}}
      {{- end }}
    {{- end -}}
    {{- if eq (toString $config.protocol) "UDP" -}}
      {{ $_ := set $udpPorts $portName $config -}}
    {{- end -}}
    {{- if eq (toString (default "TCP" $config.protocol)) "TCP" -}}
      {{ $_ := set $tcpPorts $portName $config -}}
    {{- end -}}
    {{- if (index (default dict $config.expose) $name) -}}
      {{- $exposedPorts = true -}}
    {{- end -}}
  {{- end -}}
{{- end -}}

{{- if (eq $exposedPorts false) -}}
  {{- fail (printf "ERROR: Cannot create Service %s without ports" $fullname) -}}
{{- end -}}

{{- if and $exposedPorts (or $tcpPorts $service.single) }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullname }}
  namespace: {{ template "traefik.namespace" $ }}
  {{- template "traefik.service-metadata" (dict "root" $ "service" $service) }}
  annotations:
  {{- with (merge dict (default dict $service.annotationsTCP) (default dict $service.annotations)) }}
  {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- template "traefik.service-spec" (dict "root" $ "service" $service) }}
  ports:
  {{- template "traefik.service-ports" (dict "ports" $tcpPorts "serviceName" $name) }}
{{- if $service.single }}
  {{- template "traefik.service-ports" (dict "ports" $udpPorts "serviceName" $name) }}
{{- end }}
{{- end }}

{{- if and $exposedPorts (and $udpPorts (not $service.single)) }}
 {{- $ports := include "traefik.service-ports" (dict "ports" $udpPorts "serviceName" $name) }}
 {{- if not (empty $ports) }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullname }}-udp
  namespace: {{ template "traefik.namespace" $ }}
  {{- template "traefik.service-metadata" (dict "root" $ "service" $service) }}
  annotations:
  {{- with (merge dict (default dict $service.annotationsUDP) (default dict $service.annotations)) }}
  {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- template "traefik.service-spec" (dict "root" $ "service" $service) }}
  ports:
  {{- $ports }}
 {{- end }}
{{- end }}

{{- end -}}
{{- end -}}
