{{- if .Values.metrics.prometheus }}
{{- if (.Values.metrics.prometheus.serviceMonitor).enabled }}
  {{- if (not (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1")) }}
    {{- if (not (.Values.metrics.prometheus.disableAPICheck)) }}
      {{- fail "ERROR: You have to deploy monitoring.coreos.com/v1 first" }}
    {{- end }}
  {{- end }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "traefik.fullname" . }}
  namespace: {{ .Values.metrics.prometheus.serviceMonitor.namespace | default (include "traefik.namespace" .) }}
  labels:
    {{- if (.Values.metrics.prometheus.service).enabled }}
    {{- include "traefik.metricsservicelabels" . | nindent 4 }}
    {{- else }}
    {{- include "traefik.labels" . | nindent 4 }}
    {{- end }}
    {{- with .Values.metrics.prometheus.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  jobLabel: {{ .Values.metrics.prometheus.serviceMonitor.jobLabel | default .Release.Name }}
  endpoints:
    - targetPort: metrics
      path: /{{ .Values.metrics.prometheus.entryPoint }}
      {{- with .Values.metrics.prometheus.serviceMonitor.honorLabels }}
      honorLabels: {{ . }}
      {{- end }}
      {{- with .Values.metrics.prometheus.serviceMonitor.honorTimestamps }}
      honorTimestamps: {{ . }}
      {{- end }}
      {{- with .Values.metrics.prometheus.serviceMonitor.enableHttp2 }}
      enableHttp2: {{ . }}
      {{- end }}
      {{- with .Values.metrics.prometheus.serviceMonitor.followRedirects }}
      followRedirects: {{ . }}
      {{- end }}
      {{- with .Values.metrics.prometheus.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
      {{- with .Values.metrics.prometheus.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ . }}
      {{- end }}
{{- if .Values.metrics.prometheus.serviceMonitor.metricRelabelings }}
      metricRelabelings:
{{ tpl (toYaml .Values.metrics.prometheus.serviceMonitor.metricRelabelings | indent 6) . }}
{{- end }}
{{- if .Values.metrics.prometheus.serviceMonitor.relabelings }}
      relabelings:
{{ toYaml .Values.metrics.prometheus.serviceMonitor.relabelings | indent 6 }}
{{- end }}
  {{- if .Values.metrics.prometheus.serviceMonitor.namespaceSelector }}
  namespaceSelector:
{{ toYaml .Values.metrics.prometheus.serviceMonitor.namespaceSelector | indent 4 -}}
  {{ else }}
  namespaceSelector:
    matchNames:
      - {{ template "traefik.namespace" . }}
  {{- end }}
  selector:
    matchLabels:
      {{- if (.Values.metrics.prometheus.service).enabled }}
      {{- include "traefik.metricslabelselector" . | nindent 6 }}
      {{- else }}
      {{- include "traefik.labelselector" . | nindent 6 }}
      {{- end }}
{{- end }}
{{- end }}
