# Traefik Gateway Configuration for Bootstrap Environment
# <PERSON><PERSON><PERSON> hình Traefik Gateway cho môi trường bootstrap

# Configuration for the traefik chart dependency
traefik:
  # Global configuration
  global:
    checkNewVersion: false
    sendAnonymousUsage: false

  # Image configuration
  image:
    registry: docker.io
    repository: traefik
    tag: "v3.2.3"
    pullPolicy: IfNotPresent

  # Deployment configuration
  deployment:
    enabled: true
    kind: Deployment
    replicas: 1
    minReadySeconds: 0
    annotations: {}
    labels: {}
    podAnnotations: {}
    podLabels: {}

  # Service configuration with MetalLB
  service:
    enabled: true
    type: LoadBalancer
    annotations:
      metallb.universe.tf/address-pool: default
      metallb.universe.tf/allow-shared-ip: traefik-lb
    labels: {}
    spec:
      loadBalancerIP: ""  # MetalLB sẽ tự động assign IP
    loadBalancerSourceRanges: []
    externalIPs: []

  # Ports configuration
  ports:
    traefik:
      port: 9000
      expose:
        default: true
      exposedPort: 9000
      protocol: TCP
    web:
      port: 8000
      expose:
        default: true
      exposedPort: 80
      protocol: TCP
    websecure:
      port: 8443
      expose:
        default: true
      exposedPort: 443
      protocol: TCP
      tls:
        enabled: true
        options: default
    metrics:
      port: 8080
      expose:
        default: false
      protocol: TCP

  # IngressRoute for dashboard (disabled - using HTTPRoute instead)
  ingressRoute:
    dashboard:
      enabled: false
      annotations: {}
      labels: {}
      matchRule: Host(`traefik.local`)
      entryPoints: ["websecure"]
      middlewares: []
      tls: {}

  # Providers configuration
  providers:
    kubernetesCRD:
      enabled: true
      allowCrossNamespace: true
      allowExternalNameServices: false
      allowEmptyServices: false
      ingressClass: ""
      namespaces: []
      nativeLBByDefault: false

    kubernetesIngress:
      enabled: true
      allowExternalNameServices: false
      allowEmptyServices: false
      ingressClass: ""
      namespaces: []
      publishedService:
        enabled: false

    kubernetesGateway:
      enabled: true
      experimentalChannel: true
      namespaces: []
      labelselector: ""
      nativeLBByDefault: false

  # Gateway API configuration
  gateway:
    enabled: true
    name: traefik-gateway
    namespace: bootstrap
    annotations:
      cert-manager.io/issuer: letsencrypt-cloudflare
    infrastructure: {}
    listeners:
      web:
        port: 8000
        hostname: ""
        protocol: HTTP
        namespacePolicy:
          from: All
      websecure:
        port: 8443
        hostname: ""
        protocol: HTTPS
        namespacePolicy:
          from: All
        certificateRefs:
          - name: wildcard-ospgroup-tls
            namespace: bootstrap

  gatewayClass:
    enabled: true
    name: traefik
    labels: {}

  # Entry points configuration
  entryPoints:
    web:
      address: ":8000"
    websecure:
      address: ":8443"
      http:
        tls:
          options: default

  # TLS configuration
  tls:
    options:
      default:
        minVersion: "VersionTLS12"
        cipherSuites:
          - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
          - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
          - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
          - "TLS_RSA_WITH_AES_256_GCM_SHA384"
          - "TLS_RSA_WITH_AES_128_GCM_SHA256"
    stores:
      default:
        defaultGeneratedCert:
          resolver: letsencrypt
          domain:
            main: "*.ospgroup.vn"
            sans:
              - "dockerhub.ospgroup.vn"

  # TLS Options
  tlsOptions:
    default:
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_RSA_WITH_AES_128_GCM_SHA256"

  # TLS Store to use wildcard certificate as default
  tlsStore:
    default:
      defaultCertificate:
        secretName: wildcard-ospgroup-tls

  # Logs configuration
  logs:
    general:
      level: INFO
      format: common
    access:
      enabled: false

  # Metrics configuration
  metrics:
    prometheus:
      enabled: false
    service:
      enabled: false

  # Resources
  resources:
    requests:
      cpu: "100m"
      memory: "50Mi"
    limits:
      cpu: "300m"
      memory: "150Mi"

  # Autoscaling
  autoscaling:
    enabled: false

  # Security context
  securityContext:
    capabilities:
      drop: [ALL]
    readOnlyRootFilesystem: true
    runAsGroup: 65532
    runAsNonRoot: true
    runAsUser: 65532

  podSecurityContext:
    fsGroup: 65532

  # Node selector, affinity, tolerations
  nodeSelector: {}
  affinity: {}
  tolerations: []

  # Persistence for Let's Encrypt certificates
  persistence:
    enabled: true
    name: data
    accessMode: ReadWriteOnce
    size: 128Mi
    storageClass: "local-path"
    path: /data
    annotations: {}

  # Additional arguments - THIS IS THE KEY FIX!
  additionalArguments:
    - --api.dashboard=true
    - --api.insecure=true
    - --providers.kubernetesgateway=true
    - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
    - --certificatesresolvers.letsencrypt.acme.storage=/data/acme.json
    - --certificatesresolvers.letsencrypt.acme.dnschallenge.provider=cloudflare
    - --certificatesresolvers.letsencrypt.acme.dnschallenge.delaybeforecheck=30s
    - --certificatesresolvers.letsencrypt.acme.dnschallenge.resolvers=1.1.1.1:53,8.8.8.8:53
    # Increase timeouts for large uploads
    - --serversTransport.forwardingTimeouts.dialTimeout=30s
    - --serversTransport.forwardingTimeouts.responseHeaderTimeout=3600s
    - --serversTransport.forwardingTimeouts.idleConnTimeout=90s
    - --entryPoints.web.transport.respondingTimeouts.readTimeout=3600s
    - --entryPoints.web.transport.respondingTimeouts.writeTimeout=3600s
    - --entryPoints.web.transport.respondingTimeouts.idleTimeout=180s
    - --entryPoints.websecure.transport.respondingTimeouts.readTimeout=3600s
    - --entryPoints.websecure.transport.respondingTimeouts.writeTimeout=3600s
    - --entryPoints.websecure.transport.respondingTimeouts.idleTimeout=180s

  # Environment variables for Let's Encrypt DNS challenge
  env:
    - name: CF_API_EMAIL
      valueFrom:
        secretKeyRef:
          name: cloudflare-credentials
          key: CF_API_EMAIL
    - name: CF_API_KEY
      valueFrom:
        secretKeyRef:
          name: cloudflare-credentials
          key: CF_API_KEY

  # Volumes and volume mounts
  volumes: []
  volumeMounts: []
