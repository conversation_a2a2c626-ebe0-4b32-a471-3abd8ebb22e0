# HashiCorp Vault Configuration for Bootstrap Environment
# C<PERSON>u hình Vault cho môi trường bootstrap

global:
  enabled: true
  namespace: bootstrap

# Injector configuration - Disable for bootstrap
injector:
  enabled: false

# Server configuration
server:
  # Enable dev mode for easy setup (NOT for production)
  dev:
    enabled: true
    devRootToken: "bootstrap-root-token"

  # Image configuration
  image:
    repository: "hashicorp/vault"
    tag: "1.20.1"
    pullPolicy: IfNotPresent

  # Resource limits
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "512Mi"
      cpu: "500m"

  # Service configuration
  service:
    enabled: true
    type: ClusterIP
    port: 8200
    targetPort: 8200
    annotations: {}

  # Ingress disabled - will use Gateway API
  ingress:
    enabled: false

  # Storage configuration for dev mode
  dataStorage:
    enabled: false  # Dev mode uses in-memory storage

  # Audit storage disabled for dev mode
  auditStorage:
    enabled: false

  # Standalone configuration (fallback if dev mode fails)
  standalone:
    enabled: false
    config: |
      ui = true
      
      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }
      
      storage "file" {
        path = "/vault/data"
      }

  # HA configuration disabled
  ha:
    enabled: false

  # Security context
  securityContext:
    runAsNonRoot: true
    runAsGroup: 1000
    runAsUser: 100
    fsGroup: 1000

  # Readiness and liveness probes
  readinessProbe:
    enabled: true
    path: "/v1/sys/health?standbyok=true&sealedcode=204&uninitcode=204"
    initialDelaySeconds: 5
    periodSeconds: 5

  livenessProbe:
    enabled: true
    path: "/v1/sys/health?standbyok=true"
    initialDelaySeconds: 60
    periodSeconds: 5

  # Extra environment variables
  extraEnvironmentVars:
    VAULT_CACERT: /etc/ssl/certs/ca-certificates.crt

  # Volumes and volume mounts
  volumes: []
  volumeMounts: []

  # Affinity rules
  affinity: {}

  # Node selector
  nodeSelector: {}

  # Tolerations
  tolerations: []

# UI configuration
ui:
  enabled: true
  serviceType: "ClusterIP"
  externalPort: 8200

# CSI Provider disabled
csi:
  enabled: false

# Server telemetry
serverTelemetry:
  serviceMonitor:
    enabled: false
