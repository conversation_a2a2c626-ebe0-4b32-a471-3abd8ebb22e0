{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.csiEnabled" . -}}
{{- if .csiEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "vault.fullname" . }}-csi-provider-rolebinding
  namespace: {{ include "vault.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-csi-provider
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "vault.fullname" . }}-csi-provider-role
subjects:
- kind: ServiceAccount
  name: {{ template "vault.fullname" . }}-csi-provider
  namespace: {{ include "vault.namespace" . }}
{{- end }}
