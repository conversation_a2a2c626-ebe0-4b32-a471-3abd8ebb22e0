{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.injectorEnabled" . -}}
{{- if .injectorEnabled -}}
{{- if and (eq (.Values.injector.leaderElector.enabled | toString) "true") (gt (.Values.injector.replicas | int) 1) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "vault.fullname" . }}-agent-injector-leader-elector-role
  namespace: {{ include "vault.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-agent-injector
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
rules:
  - apiGroups: [""]
    resources: ["secrets", "configmaps"]
    verbs:
      - "create"
      - "get"
      - "watch"
      - "list"
      - "update"
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs:
      - "delete"
  - apiGroups: [""]
    resources: ["pods"]
    verbs:
      - "get"
      - "patch"
      - "delete"
{{- end }}
{{- end }}
