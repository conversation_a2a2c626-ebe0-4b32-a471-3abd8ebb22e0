{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.injectorEnabled" . -}}
{{- if .injectorEnabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "vault.fullname" . }}-agent-injector
  namespace: {{ include "vault.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-agent-injector
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  {{ template "injector.serviceAccount.annotations" . }}
{{ end }}
