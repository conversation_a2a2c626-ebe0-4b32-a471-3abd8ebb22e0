{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{ template "vault.mode" . }}
{{- if ne .mode "external" }}
{{- template "vault.serverServiceEnabled" . -}}
{{- if .serverServiceEnabled -}}
# Service for Vault cluster
apiVersion: v1
kind: Service
metadata:
  name: {{ template "vault.fullname" . }}-internal
  namespace: {{ include "vault.namespace" . }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    vault-internal: "true"
  annotations:
{{ template "vault.service.annotations" .}}
spec:
  {{- if (semverCompare ">= 1.23-0" .Capabilities.KubeVersion.Version) }}
  {{- if .Values.server.service.ipFamilyPolicy }}
  ipFamilyPolicy: {{ .Values.server.service.ipFamilyPolicy }}
  {{- end }}
  {{- if .Values.server.service.ipFamilies }}
  ipFamilies: {{ .Values.server.service.ipFamilies | toYaml | nindent 2 }}
  {{- end }}
  {{- end }}
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    - name: "{{ include "vault.scheme" . }}"
      port: {{ .Values.server.service.port }}
      targetPort: {{ .Values.server.service.targetPort }}
    - name: https-internal
      port: 8201
      targetPort: 8201
  selector:
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    component: server
{{- end }}
{{- end }}
