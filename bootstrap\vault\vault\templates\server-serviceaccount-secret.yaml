{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{ template "vault.serverServiceAccountSecretCreationEnabled" . }}
{{- if .serverServiceAccountSecretCreationEnabled -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "vault.serviceAccount.name" . }}-token
  namespace: {{ include "vault.namespace" . }}
  annotations:
    kubernetes.io/service-account.name: {{ template "vault.serviceAccount.name" . }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
type: kubernetes.io/service-account-token
{{ end }}