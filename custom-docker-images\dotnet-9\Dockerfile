# OSP Custom GitHub Runner with .NET 9 SDK - Improved Version
FROM ubuntu:22.04

# Metadata
LABEL maintainer="OSP DevOps Team"
LABEL description="OSP Custom GitHub Runner with .NET 9 SDK - Improved Build"
LABEL version="1.1.0"
LABEL org.opencontainers.image.source="https://github.com/ospgroupvn/k8s-deployment"
LABEL org.opencontainers.image.description="GitHub Actions self-hosted runner with .NET 9 SDK"

# Arguments with defaults
ARG DOTNET_VERSION=9.0
ARG RUNNER_VERSION=2.328.0
ARG TARGETPLATFORM=linux/amd64

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV RUNNER_VERSION=${RUNNER_VERSION}
ENV DOTNET_VERSION=${DOTNET_VERSION}
ENV DOTNET_ROOT=/usr/share/dotnet
ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV DOTNET_NOLOGO=1
ENV NUGET_XMLDOC_MODE=skip
ENV PATH="${PATH}:/usr/share/dotnet"

# Start as root for system setup
USER root

# Install basic dependencies with error handling
RUN set -e && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        wget \
        ca-certificates \
        apt-transport-https \
        software-properties-common \
        gnupg \
        lsb-release \
        jq \
        git \
        unzip \
        tar \
        sudo \
        libc6-dev \
        libgdiplus \
        libssl-dev \
        libicu-dev \
        build-essential \
        && rm -rf /var/lib/apt/lists/* \
        && apt-get clean

# Install Docker CLI
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli docker-buildx-plugin docker-compose-plugin \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft package signing key and repository with error handling
RUN set -e && \
    echo "Installing Microsoft package repository..." && \
    wget -q https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb && \
    dpkg -i packages-microsoft-prod.deb && \
    rm packages-microsoft-prod.deb && \
    apt-get update

# Install .NET 9 SDK with verification
RUN set -e && \
    echo "Installing .NET 9 SDK..." && \
    apt-get update && \
    apt-get install -y --no-install-recommends dotnet-sdk-9.0 && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean && \
    echo "Verifying .NET installation..." && \
    dotnet --version && \
    dotnet --list-sdks && \
    dotnet --list-runtimes && \
    echo ".NET 9 SDK installed and verified successfully"

# Create runner user and groups
RUN groupadd -g 999 docker || true \
    && groupadd -g 1000 runner \
    && useradd -m -s /bin/bash -u 1000 -g runner runner \
    && usermod -aG docker runner \
    && usermod -aG sudo runner \
    && echo "runner ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create GitHub Actions runner directory
RUN mkdir -p /actions-runner && chown runner:runner /actions-runner

# Download and install GitHub Actions runner
WORKDIR /actions-runner
RUN curl -o actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz -L https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && tar xzf ./actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && rm actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && chown -R runner:runner /actions-runner

# Install runner dependencies
RUN ./bin/installdependencies.sh

# Create working directories for .NET projects
RUN mkdir -p /workspace \
    && mkdir -p /home/<USER>/.nuget/NuGet \
    && mkdir -p /home/<USER>/.nuget/packages \
    && mkdir -p /home/<USER>/.local/share/NuGet/http-cache \
    && mkdir -p /home/<USER>/.local/share/NuGet/v3-cache \
    && mkdir -p /home/<USER>/.local/share/NuGet/plugins-cache \
    && mkdir -p /home/<USER>/.dotnet/NuGetFallbackFolder \
    && chown -R runner:runner /workspace \
    && chown -R runner:runner /home/<USER>
    && chmod -R 755 /home/<USER>/.nuget \
    && chmod -R 755 /home/<USER>/.local \
    && chmod -R 755 /home/<USER>/.dotnet

# Create NuGet.Config with OSP NuGet source
RUN echo '<?xml version="1.0" encoding="utf-8"?>' > /home/<USER>/.nuget/NuGet/NuGet.Config \
    && echo '<configuration>' >> /home/<USER>/.nuget/NuGet/NuGet.Config \
    && echo '  <packageSources>' >> /home/<USER>/.nuget/NuGet/NuGet.Config \
    && echo '    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />' >> /home/<USER>/.nuget/NuGet/NuGet.Config \
    && echo '  </packageSources>' >> /home/<USER>/.nuget/NuGet/NuGet.Config \
    && echo '</configuration>' >> /home/<USER>/.nuget/NuGet/NuGet.Config \
    && chown runner:runner /home/<USER>/.nuget/NuGet/NuGet.Config

# Create global.json template
RUN echo '{' > /workspace/global.json.template \
    && echo '  "sdk": {' >> /workspace/global.json.template \
    && echo '    "version": "9.0.x",' >> /workspace/global.json.template \
    && echo '    "rollForward": "latestMajor"' >> /workspace/global.json.template \
    && echo '  }' >> /workspace/global.json.template \
    && echo '}' >> /workspace/global.json.template

# Pre-warm .NET by creating a small project
WORKDIR /tmp
RUN dotnet new console -n warmup \
    && cd warmup \
    && dotnet restore \
    && dotnet build \
    && cd .. \
    && rm -rf warmup

# Create entrypoint script
RUN echo '#!/bin/bash' > /entrypoint.sh \
    && echo 'set -e' >> /entrypoint.sh \
    && echo '' >> /entrypoint.sh \
    && echo '# Fix permissions' >> /entrypoint.sh \
    && echo 'sudo chown -R runner:runner /home/<USER>/.nuget' >> /entrypoint.sh \
    && echo 'sudo chown -R runner:runner /home/<USER>/.local' >> /entrypoint.sh \
    && echo 'sudo chown -R runner:runner /home/<USER>/.dotnet' >> /entrypoint.sh \
    && echo '' >> /entrypoint.sh \
    && echo '# Verify .NET installation' >> /entrypoint.sh \
    && echo 'echo "=== .NET Information ==="' >> /entrypoint.sh \
    && echo 'dotnet --version' >> /entrypoint.sh \
    && echo 'dotnet --list-sdks' >> /entrypoint.sh \
    && echo 'dotnet --list-runtimes' >> /entrypoint.sh \
    && echo '' >> /entrypoint.sh \
    && echo '# Configure runner if environment variables are provided' >> /entrypoint.sh \
    && echo 'if [[ -n "$REPO_URL" && -n "$ACCESS_TOKEN" ]]; then' >> /entrypoint.sh \
    && echo '  echo "Configuring GitHub Actions runner..."' >> /entrypoint.sh \
    && echo '  ./config.sh --url "$REPO_URL" --token "$ACCESS_TOKEN" --name "${RUNNER_NAME:-dotnet9-runner}" --labels "${RUNNER_LABELS:-self-hosted,linux,x64,docker,dotnet,dotnet9}" --unattended --replace' >> /entrypoint.sh \
    && echo '  echo "Starting GitHub Actions runner..."' >> /entrypoint.sh \
    && echo '  ./run.sh' >> /entrypoint.sh \
    && echo 'else' >> /entrypoint.sh \
    && echo '  echo "Environment variables REPO_URL and ACCESS_TOKEN are required"' >> /entrypoint.sh \
    && echo '  echo "Usage: docker run -e REPO_URL=... -e ACCESS_TOKEN=... <image>"' >> /entrypoint.sh \
    && echo '  exit 1' >> /entrypoint.sh \
    && echo 'fi' >> /entrypoint.sh \
    && chmod +x /entrypoint.sh \
    && chown runner:runner /entrypoint.sh

# Switch to runner user
USER runner

# Set working directory
WORKDIR /actions-runner

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD dotnet --version || exit 1

# Entry point
ENTRYPOINT ["/entrypoint.sh"]
