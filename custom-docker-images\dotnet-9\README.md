# OSP Custom GitHub Runner with .NET 9 SDK

This is a custom Docker image that provides a GitHub Actions self-hosted runner with .NET 9 SDK pre-installed, designed for building and running .NET 9 applications in CI/CD pipelines.

## Features

- ✅ GitHub Actions runner (v2.328.0)
- ✅ .NET 9 SDK pre-installed
- ✅ Docker CLI support (for Docker-in-Docker scenarios)
- ✅ Ubuntu 22.04 base image
- ✅ Pre-configured NuGet sources
- ✅ Pre-warmed .NET environment for faster builds
- ✅ Health check monitoring
- ✅ Optimized for CI/CD workflows

## Image Structure

```
/actions-runner/          # GitHub Actions runner installation
/workspace/               # Working directory for projects
/home/<USER>/.nuget/      # NuGet configuration and cache
/home/<USER>/.local/      # Local NuGet cache
/home/<USER>/.dotnet/     # .NET configuration
```

## Environment Variables

### Required
- `REPO_URL`: GitHub repository URL (e.g., `https://github.com/owner/repo`)
- `ACCESS_TOKEN`: GitHub personal access token or runner registration token

### Optional
- `RUNNER_NAME`: Name for the runner (default: `dotnet9-runner`)
- `RUNNER_LABELS`: Comma-separated labels (default: `self-hosted,linux,x64,docker,dotnet,dotnet9`)

## Usage

### 1. Basic Usage

```bash
docker run -d \
  --name osp-dotnet9-runner \
  -e REPO_URL="https://github.com/your-org/your-repo" \
  -e ACCESS_TOKEN="your-github-token" \
  -e RUNNER_NAME="osp-dotnet9-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom,dotnet,dotnet9" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9:latest
```

### 2. Interactive Testing

```bash
docker run --rm -it dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9:latest /bin/bash
```

### 3. Test .NET Functionality

```bash
# Inside the container
dotnet --version
dotnet --list-sdks
dotnet --list-runtimes

# Test NuGet sources
dotnet nuget list source

# Create and run a test project
dotnet new console -n myapp --framework net9.0
cd myapp
dotnet restore
dotnet build
dotnet run
```

## GitHub Actions Workflow Example

```yaml
name: .NET 9 Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: [self-hosted, linux, x64, dotnet, dotnet9]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Test
      run: dotnet test --no-build --configuration Release --verbosity normal
    
    - name: Publish
      run: dotnet publish --no-build --configuration Release --output ./publish
```

## Supported .NET Commands

This image supports all .NET CLI commands:

```bash
# Project management
dotnet new <template>
dotnet restore
dotnet build
dotnet run
dotnet test
dotnet publish

# Package management
dotnet add package <package-name>
dotnet remove package <package-name>
dotnet list package
dotnet pack
dotnet nuget push <package.nupkg> --source <source-name>

# Tool management
dotnet tool install -g <tool-name>
dotnet tool list -g

# Solution management
dotnet sln add <project>
dotnet sln remove <project>
```

## Build Information

- **Base Image**: Ubuntu 22.04
- **.NET Version**: 9.0
- **Runner Version**: 2.328.0
- **Docker CLI**: Latest stable
- **Architecture**: linux/amd64

## Troubleshooting

### 1. Check .NET Installation

```bash
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9:latest dotnet --info
```

### 2. Verify Runner Registration

```bash
# Check runner logs
docker logs <container-name>
```

### 3. Test Docker Access

```bash
# Inside the container
docker --version
docker ps
```

## Security Notes

- The runner user has sudo access for system operations
- Docker socket is mounted for Docker-in-Docker scenarios
- NuGet packages are cached for performance
- Health checks ensure the container is running properly

## Maintenance

- Regular updates to base image and .NET SDK
- Security patches applied automatically
- Runner version updates as needed
- Performance optimizations based on usage patterns
