#!/bin/bash
set -e

# OSP Custom GitHub Runner with .NET 9 SDK Build Script

# Color setup for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Retry function with exponential backoff
retry_command() {
    local max_attempts=$1
    local delay=$2
    local command="${@:3}"
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "Attempt $attempt/$max_attempts: $command"

        if eval "$command"; then
            log_success "Command succeeded on attempt $attempt"
            return 0
        else
            if [ $attempt -eq $max_attempts ]; then
                log_error "Command failed after $max_attempts attempts"
                return 1
            fi

            log_warning "Command failed, retrying in ${delay}s..."
            sleep $delay
            delay=$((delay * 2))  # Exponential backoff
            attempt=$((attempt + 1))
        fi
    done
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Test base image accessibility
test_base_image() {
    log_info "Testing base image accessibility..."

    if retry_command 3 5 "docker pull ubuntu:22.04 > /dev/null 2>&1"; then
        log_success "Base image ubuntu:22.04 is accessible"
    else
        log_error "Cannot pull base image ubuntu:22.04"
        exit 1
    fi
}

# Configuration
REGISTRY="dockerhub.ospgroup.vn"
IMAGE_OWNER="osp-public"
IMAGE_NAME="osp-custom-runner-dotnet-9"
IMAGE_TAG="1.0.0"
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:${IMAGE_TAG}"
DOTNET_VERSION="9.0"
RUNNER_VERSION="2.328.0"

# Build configuration
PUSH_TO_REGISTRY=false
PLATFORM="linux/amd64"
NO_CACHE=false
BUILD_RETRIES=3
RETRY_DELAY=10
TEST_IMAGE=true
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        --dotnet-version)
            DOTNET_VERSION="$2"
            shift 2
            ;;
        --runner-version)
            RUNNER_VERSION="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --push                Push to registry after build"
            echo "  --no-cache           Build without cache"
            echo "  --platform PLATFORM  Target platform (default: linux/amd64)"
            echo "  --tag TAG            Image tag (default: 1.0.0)"
            echo "  --dotnet-version VER .NET version (default: 9.0)"
            echo "  --runner-version VER Runner version (default: 2.328.0)"
            echo "  --help               Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Docker is available
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    log_info "Docker is available and running"
}

# Main build function with retry logic
build_image() {
    log_info "Building OSP Custom GitHub Runner with .NET 9 SDK Docker image..."
    log_info "Registry: $REGISTRY"
    log_info "Image Owner: $IMAGE_OWNER"
    log_info "Image Name: $IMAGE_NAME"
    log_info "Full Image: $FULL_IMAGE_NAME"
    log_info "Platform: $PLATFORM"
    log_info ".NET Version: $DOTNET_VERSION"
    log_info "Runner Version: $RUNNER_VERSION"

    # Build arguments
    BUILD_ARGS=(
        "--platform" "$PLATFORM"
        "--build-arg" "DOTNET_VERSION=$DOTNET_VERSION"
        "--build-arg" "RUNNER_VERSION=$RUNNER_VERSION"
        "--tag" "$FULL_IMAGE_NAME"
        "--tag" "${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:latest"
    )

    # Add no-cache if specified
    if [[ "$NO_CACHE" == "true" ]]; then
        BUILD_ARGS+=("--no-cache")
        log_info "Building without cache"
    fi

    # Build the image with retry logic
    log_info "Starting Docker build with retry logic..."

    local build_command="docker build ${BUILD_ARGS[*]} ."

    if retry_command $BUILD_RETRIES $RETRY_DELAY "$build_command"; then
        log_success "Docker image built successfully: $FULL_IMAGE_NAME"

        # Show image information
        log_info "Image information:"
        docker images | grep "$IMAGE_NAME" | head -3

        # Get image size
        local image_size=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep "$FULL_IMAGE_NAME" | awk '{print $2}')
        log_info "Image size: $image_size"
    else
        log_error "Failed to build Docker image after $BUILD_RETRIES attempts"
        exit 1
    fi
}

# Test the built image with comprehensive checks
test_image() {
    if [[ "$TEST_IMAGE" != "true" ]]; then
        log_info "Skipping image testing (disabled)"
        return 0
    fi

    log_info "Testing the built image comprehensively..."

    # Test basic functionality with error handling (bypass entrypoint)
    log_info "Testing .NET version..."
    if ! docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" dotnet --version; then
        log_error ".NET version test failed"
        return 1
    fi

    log_info "Testing .NET SDKs..."
    if ! docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" dotnet --list-sdks; then
        log_error ".NET SDKs test failed"
        return 1
    fi

    log_info "Testing .NET runtimes..."
    if ! docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" dotnet --list-runtimes; then
        log_error ".NET runtimes test failed"
        return 1
    fi

    log_info "Testing NuGet sources..."
    if ! docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" dotnet nuget list source; then
        log_warning "NuGet sources test failed (non-critical)"
    fi

    # Test creating a simple project (bypass entrypoint)
    log_info "Testing .NET 9 project creation and build..."
    if docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" /bin/bash -c "
        set -e
        cd /tmp
        echo 'Creating .NET 9 console application...'
        dotnet new console -n test-app --framework net9.0
        cd test-app
        echo 'Restoring packages...'
        dotnet restore
        echo 'Building project...'
        dotnet build --configuration Release
        echo 'Running application...'
        dotnet run
        echo '.NET 9 test completed successfully!'
    "; then
        log_success "✅ .NET 9 project creation and build test passed"
    else
        log_error "❌ .NET 9 project creation and build test failed"
        return 1
    fi

    # Test Docker CLI availability (if installed)
    log_info "Testing Docker CLI availability..."
    if docker run --rm "$FULL_IMAGE_NAME" which docker > /dev/null 2>&1; then
        log_success "✅ Docker CLI is available in the image"
    else
        log_warning "⚠️ Docker CLI not found in image (may be intentional)"
    fi

    log_success "🎉 All image tests completed successfully!"
}

# Push image to registry
push_image() {
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        log_info "Pushing image to registry..."
        
        # Push versioned tag
        log_info "Pushing $FULL_IMAGE_NAME..."
        docker push "$FULL_IMAGE_NAME"
        
        # Push latest tag
        LATEST_TAG="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:latest"
        log_info "Pushing $LATEST_TAG..."
        docker push "$LATEST_TAG"
        
        log_success "Images pushed successfully to registry"
    else
        log_info "Skipping push to registry (use --push to enable)"
    fi
}

# Show usage example
show_usage() {
    log_info "=== Usage Example ==="
    echo ""
    echo "To run the built image:"
    echo ""
    echo "docker run -d \\"
    echo "  --name osp-dotnet9-runner \\"
    echo "  -e REPO_URL=\"https://github.com/your-org/your-dotnet-project\" \\"
    echo "  -e ACCESS_TOKEN=\"your-github-token\" \\"
    echo "  -e RUNNER_NAME=\"osp-dotnet9-runner\" \\"
    echo "  -e RUNNER_LABELS=\"self-hosted,linux,x64,docker,osp-custom,dotnet,dotnet9\" \\"
    echo "  -v /var/run/docker.sock:/var/run/docker.sock \\"
    echo "  $FULL_IMAGE_NAME"
    echo ""
    echo "For interactive testing:"
    echo ""
    echo "docker run --rm -it $FULL_IMAGE_NAME /bin/bash"
}

# Main execution with enhanced error handling
main() {
    log_info "🚀 Starting OSP Custom Runner .NET 9 build process..."
    log_info "Build configuration:"
    log_info "  - Retries: $BUILD_RETRIES"
    log_info "  - Retry delay: ${RETRY_DELAY}s"
    log_info "  - Test image: $TEST_IMAGE"
    log_info "  - Push to registry: $PUSH_TO_REGISTRY"
    echo ""

    # Check prerequisites
    log_info "📋 Step 1: Checking prerequisites..."
    check_prerequisites
    test_base_image
    echo ""

    # Build image
    log_info "🔨 Step 2: Building Docker image..."
    build_image
    echo ""

    # Test image
    log_info "🧪 Step 3: Testing built image..."
    if ! test_image; then
        log_error "Image testing failed"
        exit 1
    fi
    echo ""

    # Push if requested
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        log_info "📤 Step 4: Pushing to registry..."
        push_image
        echo ""
    fi

    log_success "🎉 Build process completed successfully!"
    log_info "📦 Final image: $FULL_IMAGE_NAME"

    # Show usage example
    show_usage
}

# Run main function
main "$@"
