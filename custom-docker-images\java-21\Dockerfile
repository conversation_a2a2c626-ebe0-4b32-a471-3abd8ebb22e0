# OSP Custom GitHub Runner with Java 21 and Maven
FROM ubuntu:22.04

# Metadata
LABEL maintainer="OSP DevOps Team"
LABEL description="OSP Custom GitHub Runner with Java 21 and Maven - Optimized Build"
LABEL version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/ospgroupvn/k8s-deployment"
LABEL org.opencontainers.image.description="GitHub Actions self-hosted runner with Java 21 and Maven"

# Arguments with defaults
ARG JAVA_VERSION=21
ARG MAVEN_VERSION=3.9.9
ARG RUNNER_VERSION=2.328.0
ARG TARGETPLATFORM=linux/amd64

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV JAVA_HOME=/opt/java/openjdk
ENV MAVEN_HOME=/opt/maven
ENV PATH="${JAVA_HOME}/bin:${MAVEN_HOME}/bin:${PATH}"
ENV RUNNER_ALLOW_RUNASROOT=1
ENV RUNNER_MANUALLY_TRAP_SIG=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
  apt-transport-https \
  ca-certificates \
  curl \
  gnupg \
  lsb-release \
  software-properties-common \
  wget \
  unzip \
  git \
  jq \
  build-essential \
  libssl-dev \
  libffi-dev \
  python3 \
  python3-pip \
  python3-venv \
  sudo \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Install Docker CLI (for Docker-in-Docker scenarios)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
  && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
  && apt-get update \
  && apt-get install -y --no-install-recommends docker-ce-cli \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Install kubectl (stable version)
RUN KUBECTL_VERSION=$(curl -L -s https://dl.k8s.io/release/stable.txt) \
  && curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
  && chmod +x kubectl \
  && mv kubectl /usr/local/bin/ \
  && kubectl version --client

# Install Java 21 (Eclipse Temurin)
RUN wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | tee /etc/apt/trusted.gpg.d/adoptium.asc \
  && echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
  && apt-get update \
  && apt-get install -y --no-install-recommends temurin-${JAVA_VERSION}-jdk \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Install Maven
RUN wget -q https://archive.apache.org/dist/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz \
  && tar -xzf apache-maven-${MAVEN_VERSION}-bin.tar.gz -C /opt \
  && mv /opt/apache-maven-${MAVEN_VERSION} /opt/maven \
  && rm apache-maven-${MAVEN_VERSION}-bin.tar.gz

# Create runner user
RUN useradd -m -s /bin/bash runner \
  && usermod -aG sudo runner \
  && echo "runner ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
  && usermod -aG docker runner

# Switch to runner user for GitHub Actions runner installation
USER runner
WORKDIR /home/<USER>

# Download and install GitHub Actions runner
RUN RUNNER_ARCH=$(case ${TARGETPLATFORM} in \
  "linux/amd64") echo "x64" ;; \
  "linux/arm64") echo "arm64" ;; \
  *) echo "x64" ;; \
  esac) \
  && curl -o actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
  -L https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
  && tar xzf actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
  && rm actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz

# Install runner dependencies
RUN sudo ./bin/installdependencies.sh

# Create working directories for Java/Maven projects
RUN mkdir -p /home/<USER>/workspace \
  && mkdir -p /home/<USER>/.m2/repository \
  && mkdir -p /home/<USER>/.m2/wrapper \
  && mkdir -p /home/<USER>/.cache/maven \
  && chmod -R 755 /home/<USER>/.m2 \
  && chmod -R 755 /home/<USER>/.cache

# Create Maven settings.xml with OSP repository configuration
RUN mkdir -p /home/<USER>/.m2 \
  && cat > /home/<USER>/.m2/settings.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
http://maven.apache.org/xsd/settings-1.0.0.xsd">

<localRepository>/home/<USER>/.m2/repository</localRepository>

<servers>
<server>
<id>osp-maven-releases</id>
<username>${env.OSP_PACKAGE_USERNAME}</username>
<password>${env.OSP_PACKAGE_PASSWORD}</password>
</server>
<server>
<id>osp-maven-snapshots</id>
<username>${env.OSP_PACKAGE_USERNAME}</username>
<password>${env.OSP_PACKAGE_PASSWORD}</password>
</server>
</servers>

<mirrors>
<mirror>
<id>central</id>
<name>Central Repository</name>
<url>https://repo1.maven.org/maven2</url>
<mirrorOf>central</mirrorOf>
</mirror>
</mirrors>

<profiles>
<profile>
<id>osp-repositories</id>
<repositories>
<repository>
<id>osp-maven-releases</id>
<name>OSP Maven Releases</name>
<url>https://package.ospgroup.io.vn/repository/maven-releases/</url>
<releases>
<enabled>true</enabled>
</releases>
<snapshots>
<enabled>false</enabled>
</snapshots>
</repository>
<repository>
<id>osp-maven-snapshots</id>
<name>OSP Maven Snapshots</name>
<url>https://package.ospgroup.io.vn/repository/maven-snapshots/</url>
<releases>
<enabled>false</enabled>
</releases>
<snapshots>
<enabled>true</enabled>
</snapshots>
</repository>
</repositories>
</profile>
</profiles>

<activeProfiles>
<activeProfile>osp-repositories</activeProfile>
</activeProfiles>
</settings>
EOF

# Pre-warm Maven by downloading common dependencies
RUN mvn help:evaluate -Dexpression=maven.version -q -DforceStdout || true

# Verify installations
RUN echo "=== Installation Verification ===" \
  && echo "Java version:" && java -version \
  && echo "Maven version:" && mvn -version \
  && echo "Docker version:" && docker --version \
  && echo "kubectl version:" && kubectl version --client \
  && echo "Runner version:" && ./config.sh --version

# Switch back to root for entrypoint setup
USER root

# Create entrypoint script
RUN echo '#!/bin/bash' > /entrypoint.sh \
  && echo 'set -e' >> /entrypoint.sh \
  && echo '' >> /entrypoint.sh \
  && echo '# Fix permissions' >> /entrypoint.sh \
  && echo 'chown -R runner:runner /home/<USER>/.m2' >> /entrypoint.sh \
  && echo 'chown -R runner:runner /home/<USER>/.cache' >> /entrypoint.sh \
  && echo 'chown -R runner:runner /home/<USER>/workspace' >> /entrypoint.sh \
  && echo '' >> /entrypoint.sh \
  && echo '# Switch to runner user' >> /entrypoint.sh \
  && echo 'cd /home/<USER>' >> /entrypoint.sh \
  && echo 'su - runner -c "cd /home/<USER>/entrypoint-runner.sh"' >> /entrypoint.sh \
  && chmod +x /entrypoint.sh

# Create runner-specific entrypoint
RUN echo '#!/bin/bash' > /entrypoint-runner.sh \
  && echo 'set -e' >> /entrypoint-runner.sh \
  && echo '' >> /entrypoint-runner.sh \
  && echo '# Verify installations' >> /entrypoint-runner.sh \
  && echo 'echo "=== Java Information ==="' >> /entrypoint-runner.sh \
  && echo 'java -version' >> /entrypoint-runner.sh \
  && echo 'echo "=== Maven Information ==="' >> /entrypoint-runner.sh \
  && echo 'mvn -version' >> /entrypoint-runner.sh \
  && echo '' >> /entrypoint-runner.sh \
  && echo '# Configure runner if environment variables are provided' >> /entrypoint-runner.sh \
  && echo 'if [[ -n "$REPO_URL" && -n "$ACCESS_TOKEN" ]]; then' >> /entrypoint-runner.sh \
  && echo '  echo "Configuring GitHub Actions runner..."' >> /entrypoint-runner.sh \
  && echo '  ./config.sh --url "$REPO_URL" --token "$ACCESS_TOKEN" --name "${RUNNER_NAME:-java21-runner}" --labels "${RUNNER_LABELS:-self-hosted,linux,x64,docker,java,java21,maven}" --unattended --replace' >> /entrypoint-runner.sh \
  && echo '  echo "Starting GitHub Actions runner..."' >> /entrypoint-runner.sh \
  && echo '  ./run.sh' >> /entrypoint-runner.sh \
  && echo 'else' >> /entrypoint-runner.sh \
  && echo '  echo "Environment variables REPO_URL and ACCESS_TOKEN are required"' >> /entrypoint-runner.sh \
  && echo '  echo "Usage: docker run -e REPO_URL=... -e ACCESS_TOKEN=... <image>"' >> /entrypoint-runner.sh \
  && echo '  exit 1' >> /entrypoint-runner.sh \
  && echo 'fi' >> /entrypoint-runner.sh \
  && chmod +x /entrypoint-runner.sh \
  && chown runner:runner /entrypoint-runner.sh

# Set working directory
WORKDIR /home/<USER>

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD java -version && mvn -version || exit 1

# Entry point
ENTRYPOINT ["/entrypoint.sh"]
