# OSP Custom GitHub Runner with Java 21 and Maven

This is a custom Docker image that provides a GitHub Actions self-hosted runner with Java 21 and Maven pre-installed, designed for building and running Java applications in CI/CD pipelines.

## Features

- ✅ GitHub Actions runner (v2.328.0)
- ✅ Java 21 (Eclipse Temurin) pre-installed
- ✅ Maven 3.9.9 pre-installed
- ✅ Docker CLI support (for Docker-in-Docker scenarios)
- ✅ kubectl for Kubernetes deployments
- ✅ Ubuntu 22.04 base image
- ✅ Pre-configured Maven settings with OSP repositories
- ✅ Pre-warmed Maven environment for faster builds
- ✅ Health check monitoring
- ✅ Optimized for CI/CD workflows

## Image Structure

```
dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0
├── Java 21 (Eclipse Temurin)
├── Maven 3.9.9
├── GitHub Actions Runner 2.328.0
├── Docker CLI
├── kubectl
├── Pre-configured Maven settings
└── OSP repository access
```

## Pre-installed Software

| Software              | Version              | Purpose                                    |
| --------------------- | -------------------- | ------------------------------------------ |
| Java                  | 21 (Eclipse Temurin) | Java development and runtime               |
| Maven                 | 3.9.9                | Build automation and dependency management |
| GitHub Actions Runner | 2.328.0              | CI/CD execution                            |
| Docker CLI            | Latest               | Container operations                       |
| kubectl               | Latest stable        | Kubernetes deployments                     |
| Git                   | Latest               | Version control                            |
| Python3               | 3.10+                | Scripting and tools                        |

## Usage

### Basic Usage

```bash
docker run -d \
  --name osp-java21-runner \
  -e REPO_URL="https://github.com/your-org/your-repo" \
  -e ACCESS_TOKEN="your-github-token" \
  -e RUNNER_NAME="java21-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,java,java21,maven" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0
```

### Environment Variables

| Variable               | Required | Description                                  | Example                                            |
| ---------------------- | -------- | -------------------------------------------- | -------------------------------------------------- |
| `REPO_URL`             | Yes      | GitHub repository URL                        | `https://github.com/ospgroupvn/osp-common-be-java` |
| `ACCESS_TOKEN`         | Yes      | GitHub personal access token or runner token | `ghp_xxxxxxxxxxxx`                                 |
| `RUNNER_NAME`          | No       | Custom runner name                           | `java21-runner`                                    |
| `RUNNER_LABELS`        | No       | Runner labels (comma-separated)              | `self-hosted,linux,x64,docker,java,java21,maven`   |
| `OSP_PACKAGE_USERNAME` | No       | OSP Maven repository username                | `package-readonly`                                 |
| `OSP_PACKAGE_PASSWORD` | No       | OSP Maven repository password                | `z2nwhRbg7VpFHdT2`                                 |

### Kubernetes Deployment

Use with the existing GitHub runner Helm chart:

```yaml
# values-osp-common-be-java.yaml
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21
  tag: "1.0.0"
  pullPolicy: Always

runner:
  labels: "self-hosted,linux,x64,docker,java,java21,maven"
```

## Maven Configuration

The image comes with pre-configured Maven settings that include:

- **OSP Maven Repository**: Access to `https://package.ospgroup.io.vn/repository/maven-releases/`
- **Central Repository**: Standard Maven Central mirror
- **Local Repository**: `/home/<USER>/.m2/repository`
- **Environment-based Authentication**: Uses `OSP_PACKAGE_USERNAME` and `OSP_PACKAGE_PASSWORD`

### Maven Settings Location

- Global settings: `/home/<USER>/.m2/settings.xml`
- Local repository: `/home/<USER>/.m2/repository`
- Wrapper cache: `/home/<USER>/.m2/wrapper`

## Build Instructions

### Prerequisites

- Docker installed and running
- Access to OSP Docker registry (for pushing)

### Build Locally

```bash
# Clone the repository
git clone https://github.com/ospgroupvn/k8s-deployment.git
cd k8s-deployment/custom-docker-images/java-21

# Make build script executable
chmod +x build.sh

# Build the image
./build.sh

# Build and push to registry
./build.sh --push

# Build with custom versions
./build.sh --java-version 21 --maven-version 3.9.9 --tag 1.0.1
```

### Build Options

```bash
./build.sh [OPTIONS]

Options:
  --push                Push to registry after build
  --no-cache           Build without cache
  --platform PLATFORM  Target platform (default: linux/amd64)
  --tag TAG            Image tag (default: 1.0.0)
  --java-version VER   Java version (default: 21)
  --maven-version VER  Maven version (default: 3.9.9)
  --runner-version VER Runner version (default: 2.328.0)
  --verbose            Enable verbose logging
  --help               Show help message
```

## Testing

The build script includes comprehensive testing:

```bash
# Test Java installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0 java -version

# Test Maven installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0 mvn -version

# Test Docker CLI
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0 docker --version

# Test kubectl
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-java-21:1.0.0 kubectl version --client
```

## Troubleshooting

### Common Issues

1. **Permission Issues**
   ```bash
   # Fix permissions for Maven directories
   docker exec -it <container> sudo chown -R runner:runner /home/<USER>/.m2
   ```

2. **Maven Repository Access**
   ```bash
   # Check Maven settings
   docker exec -it <container> cat /home/<USER>/.m2/settings.xml
   ```

3. **Runner Registration**
   ```bash
   # Check runner logs
   docker logs <container>
   ```

### Health Check

The image includes a health check that verifies:
- Java installation and version
- Maven installation and version

```bash
# Check container health
docker inspect --format='{{.State.Health.Status}}' <container>
```

## Security Considerations

- Runner runs as non-root user (`runner`)
- Docker socket access is optional (mount only if needed)
- Environment variables for sensitive data (tokens, passwords)
- Regular security updates through base image updates

## Maintenance

### Updating the Image

1. Update versions in `build.sh`
2. Test locally
3. Build and push new version
4. Update Kubernetes deployments

### Version History

- `1.0.0` - Initial release with Java 21, Maven 3.9.9, and GitHub Actions Runner 2.328.0

## Support

For issues and questions:
- Create an issue in the [k8s-deployment repository](https://github.com/ospgroupvn/k8s-deployment)
- Contact OSP DevOps team
