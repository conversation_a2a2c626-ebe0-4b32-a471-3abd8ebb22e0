#!/bin/bash
set -e

# OSP Custom GitHub Runner with Java 21 Build Script

# Color setup for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $1"
    fi
}

# Configuration
REGISTRY="dockerhub.ospgroup.vn"
IMAGE_OWNER="osp-public"
IMAGE_NAME="osp-custom-runner-java-21"
IMAGE_TAG="1.0.0"
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:${IMAGE_TAG}"
JAVA_VERSION="21"
MAVEN_VERSION="3.9.9"
RUNNER_VERSION="2.328.0"

# Build configuration
PUSH_TO_REGISTRY=false
PLATFORM="linux/amd64"
NO_CACHE=false
BUILD_RETRIES=3
RETRY_DELAY=10
TEST_IMAGE=true
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        --java-version)
            JAVA_VERSION="$2"
            shift 2
            ;;
        --maven-version)
            MAVEN_VERSION="$2"
            shift 2
            ;;
        --runner-version)
            RUNNER_VERSION="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --push                Push to registry after build"
            echo "  --no-cache           Build without cache"
            echo "  --platform PLATFORM  Target platform (default: linux/amd64)"
            echo "  --tag TAG            Image tag (default: 1.0.0)"
            echo "  --java-version VER   Java version (default: 21)"
            echo "  --maven-version VER  Maven version (default: 3.9.9)"
            echo "  --runner-version VER Runner version (default: 2.328.0)"
            echo "  --verbose            Enable verbose logging"
            echo "  --help               Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check prerequisites
check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    log_success "Docker is available and running"
    
    # Check if we can access the registry (if pushing)
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        log_info "Testing registry access..."
        if ! docker pull hello-world &> /dev/null; then
            log_warning "Cannot pull test image, registry access might be limited"
        else
            log_success "Registry access confirmed"
        fi
    fi
}

# Test base image availability
test_base_image() {
    log_step "Testing base image availability..."
    if docker pull ubuntu:22.04 &> /dev/null; then
        log_success "Base image ubuntu:22.04 is available"
    else
        log_error "Cannot pull base image ubuntu:22.04"
        exit 1
    fi
}

# Main build function with retry logic
build_image() {
    log_step "Building OSP Custom GitHub Runner with Java 21 Docker image..."
    log_info "Registry: $REGISTRY"
    log_info "Image Owner: $IMAGE_OWNER"
    log_info "Image Name: $IMAGE_NAME"
    log_info "Full Image: $FULL_IMAGE_NAME"
    log_info "Platform: $PLATFORM"
    log_info "Java Version: $JAVA_VERSION"
    log_info "Maven Version: $MAVEN_VERSION"
    log_info "Runner Version: $RUNNER_VERSION"

    # Build arguments
    BUILD_ARGS=(
        "--platform" "$PLATFORM"
        "--build-arg" "JAVA_VERSION=$JAVA_VERSION"
        "--build-arg" "MAVEN_VERSION=$MAVEN_VERSION"
        "--build-arg" "RUNNER_VERSION=$RUNNER_VERSION"
        "--tag" "$FULL_IMAGE_NAME"
        "--tag" "${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:latest"
    )

    # Add no-cache if specified
    if [[ "$NO_CACHE" == "true" ]]; then
        BUILD_ARGS+=("--no-cache")
        log_info "Building without cache"
    fi

    # Add current directory as build context
    BUILD_ARGS+=(".")

    # Build with retry logic
    local attempt=1
    while [[ $attempt -le $BUILD_RETRIES ]]; do
        log_info "Build attempt $attempt/$BUILD_RETRIES..."
        
        if [[ "$VERBOSE" == "true" ]]; then
            log_debug "Build command: docker build ${BUILD_ARGS[*]}"
        fi
        
        if docker build "${BUILD_ARGS[@]}"; then
            log_success "Docker image built successfully: $FULL_IMAGE_NAME"
            return 0
        else
            log_warning "Build attempt $attempt failed"
            if [[ $attempt -lt $BUILD_RETRIES ]]; then
                log_info "Retrying in ${RETRY_DELAY} seconds..."
                sleep $RETRY_DELAY
            fi
        fi
        
        ((attempt++))
    done
    
    log_error "Failed to build Docker image after $BUILD_RETRIES attempts"
    exit 1
}

# Test the built image
test_image() {
    log_step "Testing built image..."
    
    # Test basic functionality
    log_info "Testing Java installation..."
    if docker run --rm --platform "$PLATFORM" "$FULL_IMAGE_NAME" java -version; then
        log_success "Java test passed"
    else
        log_error "Java test failed"
        return 1
    fi
    
    log_info "Testing Maven installation..."
    if docker run --rm --platform "$PLATFORM" "$FULL_IMAGE_NAME" mvn -version; then
        log_success "Maven test passed"
    else
        log_error "Maven test failed"
        return 1
    fi
    
    log_info "Testing Docker CLI availability..."
    if docker run --rm --platform "$PLATFORM" "$FULL_IMAGE_NAME" docker --version; then
        log_success "Docker CLI test passed"
    else
        log_error "Docker CLI test failed"
        return 1
    fi
    
    log_info "Testing kubectl availability..."
    if docker run --rm --platform "$PLATFORM" "$FULL_IMAGE_NAME" kubectl version --client; then
        log_success "kubectl test passed"
    else
        log_error "kubectl test failed"
        return 1
    fi
    
    log_success "All image tests passed"
    return 0
}

# Push image to registry
push_image() {
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        log_step "Pushing image to registry..."
        
        # Push versioned tag
        log_info "Pushing $FULL_IMAGE_NAME..."
        if docker push "$FULL_IMAGE_NAME"; then
            log_success "Successfully pushed $FULL_IMAGE_NAME"
        else
            log_error "Failed to push $FULL_IMAGE_NAME"
            exit 1
        fi
        
        # Push latest tag
        LATEST_IMAGE="${REGISTRY}/${IMAGE_OWNER}/${IMAGE_NAME}:latest"
        log_info "Pushing $LATEST_IMAGE..."
        if docker push "$LATEST_IMAGE"; then
            log_success "Successfully pushed $LATEST_IMAGE"
        else
            log_error "Failed to push $LATEST_IMAGE"
            exit 1
        fi
        
        log_success "All images pushed successfully"
    else
        log_info "Skipping push to registry (use --push to enable)"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    # Remove dangling images
    docker image prune -f &> /dev/null || true
    log_success "Cleanup completed"
}

# Main execution with enhanced error handling
main() {
    log_info "🚀 Starting OSP Custom Runner Java 21 build process..."
    log_info "Build configuration:"
    log_info "  - Retries: $BUILD_RETRIES"
    log_info "  - Retry delay: ${RETRY_DELAY}s"
    log_info "  - Test image: $TEST_IMAGE"
    log_info "  - Push to registry: $PUSH_TO_REGISTRY"
    log_info "  - Verbose: $VERBOSE"
    echo ""

    # Check prerequisites
    log_info "📋 Step 1: Checking prerequisites..."
    check_prerequisites
    test_base_image
    echo ""

    # Build image
    log_info "🔨 Step 2: Building Docker image..."
    build_image
    echo ""

    # Test image
    if [[ "$TEST_IMAGE" == "true" ]]; then
        log_info "🧪 Step 3: Testing built image..."
        if ! test_image; then
            log_error "Image testing failed"
            exit 1
        fi
        echo ""
    fi

    # Push image
    log_info "📤 Step 4: Publishing image..."
    push_image
    echo ""

    # Cleanup
    log_info "🧹 Step 5: Cleanup..."
    cleanup
    echo ""

    log_success "Build process completed successfully!"
    log_info "Image: $FULL_IMAGE_NAME"

    # Show usage example
    echo ""
    log_info "=== Usage Example ==="
    echo "docker run -d \\"
    echo "  --name osp-java21-runner \\"
    echo "  -e REPO_URL=\"https://github.com/ospgroupvn/osp-common-be-java\" \\"
    echo "  -e ACCESS_TOKEN=\"your-token\" \\"
    echo "  -e RUNNER_NAME=\"java21-runner\" \\"
    echo "  -e RUNNER_LABELS=\"self-hosted,linux,x64,docker,java,java21,maven\" \\"
    echo "  -v /var/run/docker.sock:/var/run/docker.sock \\"
    echo "  $FULL_IMAGE_NAME"
}

# Set trap for cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
