# Keycloak với Custom Themes
# Dựa trên Bitnami Keycloak image

ARG KEYCLOAK_VERSION=24.0.4
FROM bitnami/keycloak:${KEYCLOAK_VERSION}

# Metadata
LABEL maintainer="OSP Group <<EMAIL>>"
LABEL description="Keycloak với custom themes"
LABEL version="1.0.0"

# Chuy<PERSON>n sang user root để copy themes
USER root

# Tạo thư mục providers nếu chưa có (cho theme JAR)
RUN mkdir -p /opt/bitnami/keycloak/providers

# Copy theme JAR files vào providers directory
# Đây là vị trí đúng cho theme JAR theo Keycloak documentation
COPY themes/ /opt/bitnami/keycloak/providers/

# Đặt quyền sở hữu cho keycloak user
RUN chown -R 1001:root /opt/bitnami/keycloak/providers/

# Chạy build để Keycloak nhận diện themes từ JAR
RUN /opt/bitnami/keycloak/bin/kc.sh build

# Chuyển về user keycloak
USER 1001

# Expose port mặc định
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=2m --retries=3 \
    CMD curl -f http://localhost:8080/health/ready || exit 1