# Keycloak Custom Themes Image

Docker image cho Keycloak với custom themes được tích hợp sẵn.

## Cách sử dụng

### 1. Chuẩn bị themes

Copy theme JAR files hoặc theme folders vào thư mục `themes/`:

```bash
# Copy theme JAR
cp /path/to/your/theme.jar themes/

# Hoặc copy theme folder
cp -r /path/to/your/theme-folder themes/
```

### 2. Build image

```bash
# Build local
./build.sh

# Build và push lên registry
./build.sh --push
```

### 3. Update Keycloak deployment

Sau khi build, script sẽ hiển thị tag của image mới. Update `values.yaml` trong chart Keycloak:

```yaml
keycloak:
  image:
    registry: dockerhub.ospgroup.vn
    repository: osp-public/keycloak-custom-themes
    tag: "20231201-123456-abc123"  # Thay bằng tag từ script
```

### 4. Commit và push

```bash
git add .
git commit -m "Add custom Keycloak theme"
git push
```

ArgoCD sẽ tự động sync và deploy Keycloak với theme mới.

## Cấu trúc thư mục

```
keycloak-themes/
├── Dockerfile          # Docker build instructions
├── build.sh           # Build script
├── .dockerignore      # Docker ignore patterns
├── themes/            # Thư mục chứa custom themes
│   └── your-theme.jar # Theme files
└── README.md          # Tài liệu này
```

## Lưu ý

- Theme files sẽ được copy vào `/opt/bitnami/keycloak/themes/` trong container
- Image sử dụng base image Bitnami Keycloak
- Tag được tạo tự động dựa trên timestamp và commit SHA
- Health check được cấu hình sẵn