#!/bin/bash

# <PERSON>ript build Keycloak với Custom Themes
# Sử dụng: ./build.sh [--push]

set -euo pipefail

# <PERSON><PERSON>u hình
KEYCLOAK_VERSION="24.0.4"
REGISTRY="dockerhub.ospgroup.vn"
IMAGE_NAME="osp-public/keycloak-custom-themes"
# Sử dụng timestamp + short commit SHA làm tag để đảm bảo unique
COMMIT_SHA=$(git rev-parse --short HEAD 2>/dev/null || echo "local")
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
TAG="${TIMESTAMP}-${COMMIT_SHA}"
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "🔨 Building Keycloak với Custom Themes..."
echo "📦 Image: ${FULL_IMAGE_NAME}"
echo "🏷️  Keycloak Version: ${KEYCLOAK_VERSION}"
echo "📅 Timestamp: ${TIMESTAMP}"
echo "🔗 Commit SHA: ${COMMIT_SHA}"
echo ""

# Kiểm tra có theme files không
if [ -z "$(ls -A themes/ 2>/dev/null)" ]; then
    echo "⚠️  Cảnh báo: Không tìm thấy theme files trong thư mục themes/"
    echo "   Vui lòng copy theme JAR files vào thư mục themes/ trước khi build"
    echo ""
fi

# Build image
echo "🔨 Building Docker image for amd64 platform..."
docker buildx build \
    --platform linux/amd64 \
    --build-arg KEYCLOAK_VERSION="${KEYCLOAK_VERSION}" \
    -t "${FULL_IMAGE_NAME}" \
    -t "${REGISTRY}/${IMAGE_NAME}:latest" \
    --load \
    .

echo "✅ Build completed successfully!"
echo ""

# Kiểm tra image đã được tạo
echo "📋 Image information:"
docker images "${REGISTRY}/${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
echo ""

# Push nếu được yêu cầu
if [[ "${1:-}" == "--push" ]]; then
    echo "📤 Pushing image to registry..."
    docker push "${FULL_IMAGE_NAME}"
    docker push "${REGISTRY}/${IMAGE_NAME}:latest"
    echo "✅ Push completed successfully!"
    echo ""
fi

# Test chạy container local (tùy chọn)
echo "🧪 Testing container locally..."
CONTAINER_NAME="keycloak-test-$$"

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up test container..."
    docker rm -f "${CONTAINER_NAME}" >/dev/null 2>&1 || true
}
trap cleanup EXIT

echo "🚀 Starting test container..."
docker run -d --name "${CONTAINER_NAME}" -p 8080:8080 \
    -e KEYCLOAK_ADMIN=admin \
    -e KEYCLOAK_ADMIN_PASSWORD=admin123 \
    "${FULL_IMAGE_NAME}"

echo "⏳ Waiting for Keycloak to start..."
sleep 30

# Kiểm tra health
if curl -f http://localhost:8080/health/ready >/dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    docker logs "${CONTAINER_NAME}"
    exit 1
fi

echo "✅ Local test completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Update values.yaml với image tag: ${TAG}"
echo "2. Commit và push changes"
echo "3. ArgoCD sẽ tự động sync deployment"