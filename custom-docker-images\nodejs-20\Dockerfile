# OSP Custom GitHub Runner with Node.js 20, NPM, and PNPM
FROM ubuntu:22.04

# Metadata
LABEL maintainer="OSP DevOps Team"
LABEL description="OSP Custom GitHub Runner with Node.js 20, NPM, and PNPM - Optimized Build"
LABEL version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/ospgroupvn/k8s-deployment"
LABEL org.opencontainers.image.description="GitHub Actions self-hosted runner with Node.js 20, NPM, and PNPM"

# Arguments with defaults
ARG NODE_VERSION=20
ARG PNPM_VERSION=10
ARG RUNNER_VERSION=2.328.0
ARG TARGETPLATFORM=linux/amd64

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=production
ENV NPM_CONFIG_CACHE=/home/<USER>/.npm
ENV PNPM_HOME=/opt/pnpm
ENV PATH="${PNPM_HOME}:${PATH}"
ENV RUNNER_ALLOW_RUNASROOT=1
ENV RUNNER_MANUALLY_TRAP_SIG=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    software-properties-common \
    wget \
    unzip \
    git \
    jq \
    build-essential \
    libssl-dev \
    libffi-dev \
    python3 \
    python3-pip \
    python3-venv \
    sudo \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Docker CLI (for Docker-in-Docker scenarios)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y --no-install-recommends docker-ce-cli \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install kubectl (stable version)
RUN KUBECTL_VERSION=$(curl -L -s https://dl.k8s.io/release/stable.txt) \
    && curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/ \
    && kubectl version --client

# Install Node.js 20 using NodeSource repository
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install PNPM globally
RUN npm install -g pnpm@${PNPM_VERSION} \
    && pnpm config set store-dir /home/<USER>/.pnpm-store \
    && pnpm config set cache-dir /home/<USER>/.pnpm-cache

# Pre-install common packages globally (as root)
RUN npm install -g --registry https://registry.npmjs.org/ typescript@latest \
    && npm install -g --registry https://registry.npmjs.org/ @types/node@latest \
    && npm install -g --registry https://registry.npmjs.org/ eslint@latest \
    && npm install -g --registry https://registry.npmjs.org/ prettier@latest \
    && npm cache clean --force

# Create docker group and runner user
RUN groupadd -g 999 docker || true \
    && useradd -m -s /bin/bash runner \
    && usermod -aG sudo runner \
    && echo "runner ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && usermod -aG docker runner \
    && chown -R runner:runner /home/<USER>

# Switch to runner user for GitHub Actions runner installation
USER runner
WORKDIR /home/<USER>

# Download and install GitHub Actions runner
RUN RUNNER_ARCH=$(case ${TARGETPLATFORM} in \
    "linux/amd64") echo "x64" ;; \
    "linux/arm64") echo "arm64" ;; \
    *) echo "x64" ;; \
    esac) \
    && curl -o actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
    -L https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
    && tar xzf actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz \
    && rm actions-runner-linux-${RUNNER_ARCH}-${RUNNER_VERSION}.tar.gz

# Install runner dependencies
RUN sudo ./bin/installdependencies.sh

# Create working directories for Node.js projects
RUN mkdir -p /home/<USER>/workspace \
    && mkdir -p /home/<USER>/.npm \
    && mkdir -p /home/<USER>/.pnpm-store \
    && mkdir -p /home/<USER>/.pnpm-cache \
    && mkdir -p /home/<USER>/.cache/pnpm \
    && chmod -R 755 /home/<USER>/.npm \
    && chmod -R 755 /home/<USER>/.pnpm-store \
    && chmod -R 755 /home/<USER>/.pnpm-cache \
    && chmod -R 755 /home/<USER>/.cache

# Create .npmrc with OSP registry configuration
RUN cat > /home/<USER>/.npmrc << 'EOF'
# OSP NPM Registry Configuration
registry=https://package.ospgroup.io.vn/repository/npm-hosted/
//package.ospgroup.io.vn/repository/npm-hosted/:always-auth=true

# Fallback to npm registry for public packages
@*:registry=https://registry.npmjs.org/

# Cache and performance settings
cache=/home/<USER>/.npm
audit-level=moderate
fund=false
update-notifier=false
EOF

# Configure PNPM with OSP registry
RUN pnpm config set registry https://package.ospgroup.io.vn/repository/npm-hosted/ \
    && pnpm config set //package.ospgroup.io.vn/repository/npm-hosted/:always-auth true \
    && pnpm config set store-dir /home/<USER>/.pnpm-store \
    && pnpm config set cache-dir /home/<USER>/.pnpm-cache \
    && pnpm config set audit-level moderate \
    && pnpm config set fund false \
    && pnpm config set update-notifier false

# Pre-warm PNPM by creating a sample project (using public registry)
RUN mkdir -p /tmp/pnpm-warmup \
    && cd /tmp/pnpm-warmup \
    && pnpm init \
    && pnpm config set registry https://registry.npmjs.org/ \
    && pnpm add react@latest react-dom@latest typescript@latest \
    && cd /home/<USER>
    && rm -rf /tmp/pnpm-warmup

# Verify installations
RUN echo "=== Installation Verification ===" \
    && echo "Node.js version:" && node --version \
    && echo "NPM version:" && npm --version \
    && echo "PNPM version:" && pnpm --version \
    && echo "TypeScript version:" && tsc --version \
    && echo "Docker version:" && docker --version \
    && echo "kubectl version:" && kubectl version --client \
    && echo "Runner version:" && ./config.sh --version

# Switch back to root for entrypoint setup
USER root

# Create entrypoint script
RUN echo '#!/bin/bash' > /entrypoint.sh \
    && echo 'set -e' >> /entrypoint.sh \
    && echo '' >> /entrypoint.sh \
    && echo '# Fix permissions' >> /entrypoint.sh \
    && echo 'chown -R runner:runner /home/<USER>/.npm' >> /entrypoint.sh \
    && echo 'chown -R runner:runner /home/<USER>/.pnpm-store' >> /entrypoint.sh \
    && echo 'chown -R runner:runner /home/<USER>/.pnpm-cache' >> /entrypoint.sh \
    && echo 'chown -R runner:runner /home/<USER>/.cache' >> /entrypoint.sh \
    && echo 'chown -R runner:runner /home/<USER>/workspace' >> /entrypoint.sh \
    && echo '' >> /entrypoint.sh \
    && echo '# Switch to runner user' >> /entrypoint.sh \
    && echo 'cd /home/<USER>' >> /entrypoint.sh \
    && echo 'su - runner -c "cd /home/<USER>/entrypoint-runner.sh"' >> /entrypoint.sh \
    && chmod +x /entrypoint.sh

# Create runner-specific entrypoint
RUN echo '#!/bin/bash' > /entrypoint-runner.sh \
    && echo 'set -e' >> /entrypoint-runner.sh \
    && echo '' >> /entrypoint-runner.sh \
    && echo '# Set up NPM authentication if credentials are provided' >> /entrypoint-runner.sh \
    && echo 'if [[ -n "$OSP_PACKAGE_USERNAME" && -n "$OSP_PACKAGE_PASSWORD" ]]; then' >> /entrypoint-runner.sh \
    && echo '  echo "Setting up NPM authentication..."' >> /entrypoint-runner.sh \
    && echo '  AUTH_TOKEN=$(echo -n "$OSP_PACKAGE_USERNAME:$OSP_PACKAGE_PASSWORD" | base64)' >> /entrypoint-runner.sh \
    && echo '  echo "//package.ospgroup.io.vn/repository/npm-hosted/:_auth=$AUTH_TOKEN" >> ~/.npmrc' >> /entrypoint-runner.sh \
    && echo '  pnpm config set //package.ospgroup.io.vn/repository/npm-hosted/:_auth "$AUTH_TOKEN"' >> /entrypoint-runner.sh \
    && echo 'fi' >> /entrypoint-runner.sh \
    && echo '' >> /entrypoint-runner.sh \
    && echo '# Verify installations' >> /entrypoint-runner.sh \
    && echo 'echo "=== Node.js Information ==="' >> /entrypoint-runner.sh \
    && echo 'node --version' >> /entrypoint-runner.sh \
    && echo 'npm --version' >> /entrypoint-runner.sh \
    && echo 'pnpm --version' >> /entrypoint-runner.sh \
    && echo '' >> /entrypoint-runner.sh \
    && echo '# Configure runner if environment variables are provided' >> /entrypoint-runner.sh \
    && echo 'if [[ -n "$REPO_URL" && -n "$ACCESS_TOKEN" ]]; then' >> /entrypoint-runner.sh \
    && echo '  echo "Configuring GitHub Actions runner..."' >> /entrypoint-runner.sh \
    && echo '  ./config.sh --url "$REPO_URL" --token "$ACCESS_TOKEN" --name "${RUNNER_NAME:-nodejs20-runner}" --labels "${RUNNER_LABELS:-self-hosted,linux,x64,docker,nodejs,node20,npm,pnpm}" --unattended --replace' >> /entrypoint-runner.sh \
    && echo '  echo "Starting GitHub Actions runner..."' >> /entrypoint-runner.sh \
    && echo '  ./run.sh' >> /entrypoint-runner.sh \
    && echo 'else' >> /entrypoint-runner.sh \
    && echo '  echo "Environment variables REPO_URL and ACCESS_TOKEN are required"' >> /entrypoint-runner.sh \
    && echo '  echo "Usage: docker run -e REPO_URL=... -e ACCESS_TOKEN=... <image>"' >> /entrypoint-runner.sh \
    && echo '  exit 1' >> /entrypoint-runner.sh \
    && echo 'fi' >> /entrypoint-runner.sh \
    && chmod +x /entrypoint-runner.sh \
    && chown runner:runner /entrypoint-runner.sh

# Set working directory
WORKDIR /home/<USER>

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node --version && npm --version && pnpm --version || exit 1

# Entry point
ENTRYPOINT ["/entrypoint.sh"]
