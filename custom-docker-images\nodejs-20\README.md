# OSP Custom GitHub Runner with Node.js 20, NPM, and PNPM

This is a custom Docker image that provides a GitHub Actions self-hosted runner with Node.js 20, NPM, and PNPM pre-installed, designed for building and running frontend applications in CI/CD pipelines.

## Features

- ✅ GitHub Actions runner (v2.328.0)
- ✅ Node.js 20 (LTS) pre-installed
- ✅ NPM (latest) pre-installed
- ✅ PNPM 10 pre-installed
- ✅ TypeScript, ESLint, Prettier pre-installed globally
- ✅ Docker CLI support (for Docker-in-Docker scenarios)
- ✅ kubectl for Kubernetes deployments
- ✅ Ubuntu 22.04 base image
- ✅ Pre-configured NPM/PNPM settings with OSP repositories
- ✅ Pre-warmed Node.js environment for faster builds
- ✅ Health check monitoring
- ✅ Optimized for CI/CD workflows

## Image Structure

```
dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0
├── Node.js 20 (LTS)
├── NPM (latest)
├── PNPM 10
├── TypeScript, ESLint, Prettier
├── GitHub Actions Runner 2.328.0
├── Docker CLI
├── kubectl
├── Pre-configured NPM/PNPM settings
└── OSP repository access
```

## Pre-installed Software

| Software              | Version       | Purpose                                    |
| --------------------- | ------------- | ------------------------------------------ |
| Node.js               | 20 (LTS)      | JavaScript runtime                         |
| NPM                   | Latest        | Package manager                            |
| PNPM                  | 10            | Fast, disk space efficient package manager |
| TypeScript            | Latest        | TypeScript compiler                        |
| ESLint                | Latest        | Code linting                               |
| Prettier              | Latest        | Code formatting                            |
| GitHub Actions Runner | 2.328.0       | CI/CD execution                            |
| Docker CLI            | Latest        | Container operations                       |
| kubectl               | Latest stable | Kubernetes deployments                     |
| Git                   | Latest        | Version control                            |
| Python3               | 3.10+         | Scripting and tools                        |

## Usage

### Basic Usage

```bash
docker run -d \
  --name osp-nodejs20-runner \
  -e REPO_URL="https://github.com/your-org/your-repo" \
  -e ACCESS_TOKEN="your-github-token" \
  -e RUNNER_NAME="nodejs20-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,nodejs,node20,npm,pnpm" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0
```

### Environment Variables

| Variable               | Required | Description                                  | Example                                               |
| ---------------------- | -------- | -------------------------------------------- | ----------------------------------------------------- |
| `REPO_URL`             | Yes      | GitHub repository URL                        | `https://github.com/ospgroupvn/fe-osp-shared`         |
| `ACCESS_TOKEN`         | Yes      | GitHub personal access token or runner token | `ghp_xxxxxxxxxxxx`                                    |
| `RUNNER_NAME`          | No       | Custom runner name                           | `nodejs20-runner`                                     |
| `RUNNER_LABELS`        | No       | Runner labels (comma-separated)              | `self-hosted,linux,x64,docker,nodejs,node20,npm,pnpm` |
| `OSP_PACKAGE_USERNAME` | No       | OSP NPM repository username                  | `package-readonly`                                    |
| `OSP_PACKAGE_PASSWORD` | No       | OSP NPM repository password                  | `z2nwhRbg7VpFHdT2`                                    |

### Kubernetes Deployment

Use with the existing GitHub runner Helm chart:

```yaml
# values-fe-osp-shared.yaml
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20
  tag: "1.0.0"
  pullPolicy: Always

runner:
  labels: "self-hosted,linux,x64,docker,nodejs,node20,npm,pnpm"
```

## NPM/PNPM Configuration

The image comes with pre-configured NPM and PNPM settings that include:

- **OSP NPM Repository**: Access to `https://package.ospgroup.io.vn/repository/npm-hosted/`
- **NPM Registry Fallback**: Standard NPM registry for public packages
- **Environment-based Authentication**: Uses `OSP_PACKAGE_USERNAME` and `OSP_PACKAGE_PASSWORD`
- **Optimized Cache Settings**: Configured for CI/CD performance

### Configuration Files

- **NPM config**: `/home/<USER>/.npmrc`
- **PNPM config**: Global PNPM configuration
- **Cache directories**:
  - NPM: `/home/<USER>/.npm`
  - PNPM store: `/home/<USER>/.pnpm-store`
  - PNPM cache: `/home/<USER>/.pnpm-cache`

### Registry Configuration

```bash
# NPM configuration
registry=https://package.ospgroup.io.vn/repository/npm-hosted/
//package.ospgroup.io.vn/repository/npm-hosted/:always-auth=true

# PNPM configuration
pnpm config set registry https://package.ospgroup.io.vn/repository/npm-hosted/
pnpm config set //package.ospgroup.io.vn/repository/npm-hosted/:always-auth true
```

## Build Instructions

### Prerequisites

- Docker installed and running
- Access to OSP Docker registry (for pushing)

### Build Locally

```bash
# Clone the repository
git clone https://github.com/ospgroupvn/k8s-deployment.git
cd k8s-deployment/custom-docker-images/nodejs-20

# Make build script executable
chmod +x build.sh

# Build the image
./build.sh

# Build and push to registry
./build.sh --push

# Build with custom versions
./build.sh --node-version 20 --pnpm-version 10 --tag 1.0.1
```

### Build Options

```bash
./build.sh [OPTIONS]

Options:
  --push                Push to registry after build
  --no-cache           Build without cache
  --platform PLATFORM  Target platform (default: linux/amd64)
  --tag TAG            Image tag (default: 1.0.0)
  --node-version VER   Node.js version (default: 20)
  --pnpm-version VER   PNPM version (default: 10)
  --runner-version VER Runner version (default: 2.328.0)
  --verbose            Enable verbose logging
  --help               Show help message
```

## Testing

The build script includes comprehensive testing:

```bash
# Test Node.js installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 node --version

# Test NPM installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 npm --version

# Test PNPM installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 pnpm --version

# Test TypeScript installation
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 tsc --version

# Test Docker CLI
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 docker --version

# Test kubectl
docker run --rm dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0 kubectl version --client
```

## Troubleshooting

### Common Issues

1. **Permission Issues**
   ```bash
   # Fix permissions for NPM/PNPM directories
   docker exec -it <container> sudo chown -R runner:runner /home/<USER>/.npm
   docker exec -it <container> sudo chown -R runner:runner /home/<USER>/.pnpm-store
   ```

2. **NPM/PNPM Registry Access**
   ```bash
   # Check NPM configuration
   docker exec -it <container> cat /home/<USER>/.npmrc
   
   # Check PNPM configuration
   docker exec -it <container> pnpm config list
   ```

3. **Runner Registration**
   ```bash
   # Check runner logs
   docker logs <container>
   ```

### Health Check

The image includes a health check that verifies:
- Node.js installation and version
- NPM installation and version
- PNPM installation and version

```bash
# Check container health
docker inspect --format='{{.State.Health.Status}}' <container>
```

## Performance Optimization

### Pre-warmed Environment

The image includes:
- Pre-installed global packages (TypeScript, ESLint, Prettier)
- Pre-warmed NPM cache
- Pre-warmed PNPM store
- Optimized cache configurations

### Build Performance Tips

1. **Use PNPM for faster installs**: `pnpm install` instead of `npm install`
2. **Leverage cache**: Configure proper cache directories in workflows
3. **Use frozen lockfiles**: `pnpm install --frozen-lockfile` for reproducible builds

## Security Considerations

- Runner runs as non-root user (`runner`)
- Docker socket access is optional (mount only if needed)
- Environment variables for sensitive data (tokens, passwords)
- Regular security updates through base image updates

## Maintenance

### Updating the Image

1. Update versions in `build.sh`
2. Test locally
3. Build and push new version
4. Update Kubernetes deployments

### Version History

- `1.0.0` - Initial release with Node.js 20, NPM latest, PNPM 10, and GitHub Actions Runner 2.328.0

## Support

For issues and questions:
- Create an issue in the [k8s-deployment repository](https://github.com/ospgroupvn/k8s-deployment)
- Contact OSP DevOps team
