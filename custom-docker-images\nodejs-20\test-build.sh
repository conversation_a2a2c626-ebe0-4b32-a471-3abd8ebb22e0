#!/bin/bash
# Test script to build Node.js 20 custom runner image locally (without pushing)

set -e

echo "🧪 Testing Node.js 20 Custom Runner Image Build"
echo "==============================================="

# Change to the nodejs-20 directory
cd "$(dirname "$0")"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running"
    exit 1
fi

echo "✅ Docker is available and running"

# Build the image using the build script (without pushing)
echo ""
echo "🔨 Building Node.js 20 custom runner image..."
echo "This may take several minutes..."

# Run the build script without pushing
if ./build.sh --verbose; then
    echo ""
    echo "🎉 Build completed successfully!"
    echo ""
    
    # Test the built image
    echo "🧪 Testing the built image..."
    
    IMAGE_NAME="dockerhub.ospgroup.vn/osp-public/osp-custom-runner-nodejs-20:1.0.0"
    
    echo "Testing Node.js version..."
    if docker run --rm --platform linux/amd64 "$IMAGE_NAME" node --version; then
        echo "✅ Node.js test passed"
    else
        echo "❌ Node.js test failed"
        exit 1
    fi
    
    echo "Testing NPM version..."
    if docker run --rm --platform linux/amd64 "$IMAGE_NAME" npm --version; then
        echo "✅ NPM test passed"
    else
        echo "❌ NPM test failed"
        exit 1
    fi
    
    echo "Testing PNPM version..."
    if docker run --rm --platform linux/amd64 "$IMAGE_NAME" pnpm --version; then
        echo "✅ PNPM test passed"
    else
        echo "❌ PNPM test failed"
        exit 1
    fi
    
    echo ""
    echo "🎉 All tests passed! Image is ready for use."
    echo ""
    echo "📋 Image details:"
    echo "Name: $IMAGE_NAME"
    echo "Platform: linux/amd64"
    echo ""
    echo "📝 To push to registry, run:"
    echo "./build.sh --push"
    
else
    echo ""
    echo "❌ Build failed! Please check the logs above."
    exit 1
fi