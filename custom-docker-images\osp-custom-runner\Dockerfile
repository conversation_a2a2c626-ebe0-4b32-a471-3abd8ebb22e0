# OSP Custom GitHub Actions Runner
# Sử dụng Ubuntu base image với Docker CLI support (không dùng DinD để tránh conflict)

ARG RUNNER_VERSION=2.328.0
ARG DOCKER_VERSION=25.0.5

FROM ubuntu:22.04

# Metadata
LABEL maintainer="OSP Group <<EMAIL>>"
LABEL description="OSP Custom GitHub Actions Runner với Docker CLI support"
LABEL version="${RUNNER_VERSION}"
LABEL org.opencontainers.image.source="https://github.com/ospgroupvn/k8s-deployment"

# Arguments
ARG RUNNER_VERSION
ARG DOCKER_VERSION

# Environment variables for runner configuration
ENV RUNNER_VERSION=${RUNNER_VERSION}
ENV RUNNER_NAME=""
ENV RUNNER_WORKDIR="_work"
ENV RUNNER_LABELS=""
ENV REPO_URL=""
# Note: ACCESS_TOKEN and RUNNER_TOKEN should be provided at runtime
# ENV ACCESS_TOKEN=""
# ENV RUNNER_TOKEN=""
ENV RUNNER_GROUP="default"
ENV RUNNER_SCOPE="repo"
ENV ORG_NAME=""
ENV ENTERPRISE_NAME=""
ENV RUN_AS_ROOT="false"
ENV CONFIGURED_ACTIONS_RUNNER_FILES_DIR="/actions-runner"
ENV DISABLE_AUTO_DEREGISTRATION="false"
ENV RUNNER_ALLOW_RUNASROOT="1"
ENV ACTIONS_RUNNER_PRINT_LOG_TO_STDOUT="1"

# Docker environment variables (sử dụng host Docker socket)
ENV DOCKER_HOST=unix:///var/run/docker.sock

# Cài đặt dependencies cơ bản cho Ubuntu
RUN apt-get update && apt-get install -y \
    bash \
    curl \
    wget \
    unzip \
    tar \
    git \
    jq \
    build-essential \
    ca-certificates \
    software-properties-common \
    apt-transport-https \
    gnupg \
    lsb-release \
    sudo \
    vim \
    xxd \
    && rm -rf /var/lib/apt/lists/*

# Cài đặt Docker CLI từ official Docker repository (chỉ CLI, không cài daemon)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli docker-buildx-plugin docker-compose-plugin \
    && rm -rf /var/lib/apt/lists/*

# Cài đặt .NET 8.0 runtime từ Microsoft official repository
RUN wget -q https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb \
    && dpkg -i packages-microsoft-prod.deb \
    && rm packages-microsoft-prod.deb \
    && apt-get update \
    && apt-get install -y --no-install-recommends dotnet-runtime-8.0 aspnetcore-runtime-8.0 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Cài đặt kubectl (stable version)
RUN KUBECTL_VERSION=$(curl -L -s https://dl.k8s.io/release/stable.txt) \
    && curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/ \
    && kubectl version --client

# Cài đặt Helm (latest stable)
RUN curl -fsSL https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash \
    && helm version

# Cài đặt GitHub CLI
RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
    && chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
    && apt-get update \
    && apt-get install -y --no-install-recommends gh \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Tạo docker group và user runner cho Ubuntu
RUN groupadd -g 999 docker || true \
    && groupadd -g 1000 runner \
    && useradd -m -s /bin/bash -u 1000 -g runner runner \
    && usermod -aG docker runner \
    && usermod -aG sudo runner \
    && echo "runner ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && echo "runner:runner" | chpasswd

# Tạo thư mục cho Actions Runner
RUN mkdir -p /actions-runner && chown runner:runner /actions-runner
WORKDIR /actions-runner

# Download GitHub Actions Runner
RUN curl -o actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    -L https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && tar xzf actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && rm actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz \
    && chown -R runner:runner /actions-runner



# Copy scripts
COPY entrypoint.sh /entrypoint.sh
COPY register-runner.sh /register-runner.sh

COPY prestop-hook.sh /prestop-hook.sh

# Set permissions
RUN chmod +x /entrypoint.sh /register-runner.sh /prestop-hook.sh \
    && chown runner:runner /entrypoint.sh /register-runner.sh /prestop-hook.sh

# Tạo thư mục work cho runner
RUN mkdir -p /_work && chown runner:runner /_work

# Tạo script để kiểm tra Docker socket availability
RUN echo '#!/bin/bash' > /check-docker.sh \
    && echo 'set -e' >> /check-docker.sh \
    && echo 'echo "Checking Docker socket availability..."' >> /check-docker.sh \
    && echo 'if [ -S /var/run/docker.sock ]; then' >> /check-docker.sh \
    && echo '    echo "Docker socket found at /var/run/docker.sock"' >> /check-docker.sh \
    && echo '    if docker info >/dev/null 2>&1; then' >> /check-docker.sh \
    && echo '        echo "Docker daemon is accessible and running"' >> /check-docker.sh \
    && echo '        docker version' >> /check-docker.sh \
    && echo '        exit 0' >> /check-docker.sh \
    && echo '    else' >> /check-docker.sh \
    && echo '        echo "Docker socket exists but daemon is not accessible"' >> /check-docker.sh \
    && echo '        exit 1' >> /check-docker.sh \
    && echo '    fi' >> /check-docker.sh \
    && echo 'else' >> /check-docker.sh \
    && echo '    echo "Docker socket not found. Make sure to mount /var/run/docker.sock"' >> /check-docker.sh \
    && echo '    exit 1' >> /check-docker.sh \
    && echo 'fi' >> /check-docker.sh \
    && chmod +x /check-docker.sh

# Cấu hình sudo để runner có thể chạy Docker commands
RUN echo "runner ALL=(ALL) NOPASSWD: /check-docker.sh" > /etc/sudoers.d/runner \
    && echo "runner ALL=(ALL) NOPASSWD: /bin/chown" >> /etc/sudoers.d/runner \
    && echo "runner ALL=(ALL) NOPASSWD: /bin/chmod" >> /etc/sudoers.d/runner \
    && chmod 440 /etc/sudoers.d/runner

# Switch to runner user
USER runner

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD pgrep -f "Runner.Listener" > /dev/null || exit 1

# Set default working directory
WORKDIR /actions-runner

# Expose any necessary ports
EXPOSE 8080

ENTRYPOINT ["/entrypoint.sh"]