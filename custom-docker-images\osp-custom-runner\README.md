# OSP Custom GitHub Actions Runner

Custom Docker image cho GitHub Actions self-hosted runner với tính năng auto-deregistration và Docker support thông qua host socket mounting.

## Tính năng

✅ **Auto Registration**: Tự động đăng ký với GitHub Actions
✅ **Auto Deregistration**: Tự động hủy đăng ký khi container/pod stop
✅ **Docker Support**: Hỗ trợ chạy Docker commands thông qua host socket mounting (v2.0)
✅ **Enhanced Logging**: Logging với màu sắc và error handling cải thiện (v2.0)
✅ **System Requirements Check**: <PERSON><PERSON><PERSON> tra tự động các yêu cầu hệ thống (v2.0)
✅ **Kubernetes Ready**: Có preStop hook cho Kubernetes deployment
✅ **Tool Integration**: Kubectl, Helm, Git, GitHub CLI và các tools DevOps khác
✅ **Graceful Shutdown**: Xử lý shutdown signals một cách graceful

## Quick Start

### 1. Build Image

```bash
cd custom-docker-images/osp-custom-runner
chmod +x build.sh
./build.sh
```

### 2. Test Local

```bash
chmod +x test-runner.sh
./test-runner.sh

# Test với GitHub token thật
chmod +x test-with-token.sh
./test-with-token.sh your-github-token
```

### 3. Push to Registry

```bash
./build.sh --push
```

## Environment Variables

### Required Variables

| Variable       | Description                  | Example                                        |
| -------------- | ---------------------------- | ---------------------------------------------- |
| `REPO_URL`     | GitHub repository URL        | `https://github.com/ospgroupvn/k8s-deployment` |
| `ACCESS_TOKEN` | GitHub Personal Access Token | `ghp_xxxxxxxxxxxx`                             |

### Optional Variables

| Variable                      | Default                                   | Description                            |
| ----------------------------- | ----------------------------------------- | -------------------------------------- |
| `RUNNER_NAME`                 | auto-generated                            | Tên của runner                         |
| `RUNNER_LABELS`               | `self-hosted,linux,x64,docker,osp-custom` | Labels cho runner                      |
| `RUNNER_GROUP`                | `default`                                 | Group của runner                       |
| `RUNNER_SCOPE`                | `repo`                                    | Scope: `repo`, `org`, `enterprise`     |
| `ORG_NAME`                    | -                                         | Organization name (nếu scope=org)      |
| `ENTERPRISE_NAME`             | -                                         | Enterprise name (nếu scope=enterprise) |
| `DISABLE_AUTO_DEREGISTRATION` | `false`                                   | Tắt auto-deregistration                |
| `RUNNER_WORKDIR`              | `_work`                                   | Working directory cho jobs             |

## Usage Examples

### Docker Run

**Lệnh Docker run được cập nhật (v2.0):**

```bash
docker run -d \
  --name osp-runner \
  -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
  -e ACCESS_TOKEN="your-token-here" \
  -e RUNNER_NAME="osp-production-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,production" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner:2.0
```

**Thay đổi quan trọng từ v1.0:**
- ❌ Không cần `--privileged` flag nữa
- ❌ Không cần mount volume cho `/_work` (tùy chọn)
- ✅ Chỉ cần mount Docker socket: `-v /var/run/docker.sock:/var/run/docker.sock`
- ✅ Đơn giản và ổn định hơn

### Docker Compose

```yaml
version: '3.8'
services:
  github-runner:
    image: dockerhub.ospgroup.vn/osp-public/osp-custom-runner:2.0
    container_name: osp-runner
    environment:
      - REPO_URL=https://github.com/ospgroupvn/k8s-deployment
      - ACCESS_TOKEN=your-token-here
      - RUNNER_NAME=osp-docker-runner
      - RUNNER_LABELS=self-hosted,linux,x64,docker,compose
      - DISABLE_AUTO_DEREGISTRATION=false
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: osp-github-runner
spec:
  replicas: 1
  selector:
    matchLabels:
      app: osp-github-runner
  template:
    metadata:
      labels:
        app: osp-github-runner
    spec:
      containers:
      - name: runner
        image: dockerhub.ospgroup.vn/osp-public/osp-custom-runner:2.0
        env:
        - name: REPO_URL
          value: "https://github.com/ospgroupvn/k8s-deployment"
        - name: ACCESS_TOKEN
          valueFrom:
            secretKeyRef:
              name: github-token
              key: token
        - name: RUNNER_NAME
          value: "osp-k8s-runner"
        - name: RUNNER_LABELS
          value: "self-hosted,linux,x64,docker,kubernetes"
        - name: DISABLE_AUTO_DEREGISTRATION
          value: "false"
        lifecycle:
          preStop:
            exec:
              command: ["/prestop-hook.sh"]
        volumeMounts:
        - name: docker-sock
          mountPath: /var/run/docker.sock
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 2
            memory: 4Gi
      volumes:
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
      terminationGracePeriodSeconds: 60
```

## Auto-Deregistration

### Docker

Auto-deregistration được kích hoạt khi:
- Container nhận SIGTERM signal
- Container được stop bằng `docker stop`
- Container exit do lỗi

### Kubernetes

Auto-deregistration được kích hoạt khi:
- Pod được delete hoặc scale down
- Node bị drain
- Deployment được update hoặc delete

**PreStop Hook**: Sử dụng `/prestop-hook.sh` để đảm bảo cleanup trước khi pod terminate.

## Security Notes

⚠️ **Personal Access Token**: Không commit token vào code. Sử dụng Kubernetes Secrets hoặc environment variables.

⚠️ **Docker Socket**: Mount Docker socket với cẩn thận. Container có thể access toàn bộ Docker daemon.

⚠️ **Privileged Access**: Runner chạy với root privileges để access Docker.

## Monitoring

### Health Check

```bash
# Kiểm tra container health
docker exec container-name pgrep -f "Runner.Listener"

# Xem logs
docker logs -f container-name

# Kiểm tra registration status
docker exec container-name cat /actions-runner/.runner
```

### GitHub UI

Kiểm tra runners tại: `https://github.com/your-org/your-repo/settings/actions/runners`

## Troubleshooting

### Runner không đăng ký

1. Kiểm tra ACCESS_TOKEN có quyền `repo` hoặc `admin:org`
2. Kiểm tra REPO_URL đúng format
3. Xem logs container để debug

### Docker không hoạt động trong workflow

1. Đảm bảo Docker socket được mount: `-v /var/run/docker.sock:/var/run/docker.sock`
2. Kiểm tra Docker daemon đang chạy trên host

### Auto-deregistration không hoạt động

1. Kiểm tra `DISABLE_AUTO_DEREGISTRATION=false`
2. Đảm bảo ACCESS_TOKEN có quyền remove runners
3. Kiểm tra logs cleanup process

## Build Arguments

| Argument         | Default   | Description                   |
| ---------------- | --------- | ----------------------------- |
| `RUNNER_VERSION` | `2.328.0` | GitHub Actions Runner version |
| `UBUNTU_VERSION` | `22.04`   | Base Ubuntu version           |

## Development

### Local Testing

```bash
# Build image
./build.sh

# Test với repository
./test-runner.sh

# Debug container
docker exec -it container-name bash
```

### Custom Build

```bash
# Build với custom runner version
docker build --build-arg RUNNER_VERSION=2.330.0 -t custom-runner .

# Build multi-platform
docker buildx build --platform linux/amd64,linux/arm64 -t custom-runner .
```

## Changelog

### v2.0 (Current) - Major Architecture Update
- ✅ **Chuyển từ Docker-in-Docker sang host Docker socket mounting**
- ✅ **Cải thiện error handling và logging với màu sắc**
- ✅ **Thêm comprehensive system requirements checking**
- ✅ **Tối ưu hóa dependencies và giảm image size**
- ✅ **Thêm retry logic cho runner registration**
- ✅ **Cải thiện graceful shutdown và auto-deregistration**
- ✅ **Thêm test script với GitHub token thật**
- ✅ **Sửa lỗi Docker group creation**
- ✅ **Enhanced documentation và usage examples**

### v1.0 - Initial Release
- Initial release với Docker-in-Docker approach
- Basic GitHub Actions runner functionality
- Cài đặt các công cụ cơ bản (kubectl, helm, gh CLI)

## Compatibility

- **Docker Desktop trên Windows**: ✅ Tested và working
- **Docker trên Linux servers**: ✅ Compatible
- **Kubernetes**: ✅ Có thể deploy như StatefulSet hoặc Deployment
- **GitHub Actions**: ✅ Fully compatible với tất cả GitHub Actions features

## License

MIT License - See LICENSE file for details.

## Support

- GitHub Issues: [k8s-deployment/issues](https://github.com/ospgroupvn/k8s-deployment/issues)
- Contact: <EMAIL>