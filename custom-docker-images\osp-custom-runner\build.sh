#!/bin/bash
set -e

# OSP Custom GitHub Runner Build Script

# Thiết lập màu sắc cho log
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
IMAGE_NAME="dockerhub.ospgroup.vn/osp-public/osp-custom-runner"
IMAGE_TAG="1.0.4"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
RUNNER_VERSION="2.328.0"
DOCKER_VERSION="25.0.5"

# Parse arguments
PUSH_TO_REGISTRY=false
PLATFORM="linux/amd64"

while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --push              Push image to registry after build"
            echo "  --platform PLATFORM Set build platform (default: linux/amd64)"
            echo "  --tag TAG           Set image tag (default: 1.0)"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Main build function
build_image() {
    log_info "Building OSP Custom GitHub Runner Docker image..."
    log_info "Image: $FULL_IMAGE_NAME"
    log_info "Platform: $PLATFORM"
    log_info "Runner Version: $RUNNER_VERSION"

    # Build the image
    docker build \
        --platform "$PLATFORM" \
        --build-arg RUNNER_VERSION="$RUNNER_VERSION" \
        --build-arg DOCKER_VERSION="$DOCKER_VERSION" \
        --tag "$FULL_IMAGE_NAME" \
        --tag "${IMAGE_NAME}:latest" \
        .

    if [[ $? -eq 0 ]]; then
        log_success "Docker image built successfully: $FULL_IMAGE_NAME"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Test image function
test_image() {
    log_info "Testing Docker image..."

    # Test basic image functionality
    log_info "Testing image startup..."
    docker run --rm --entrypoint="" "$FULL_IMAGE_NAME" //bin/bash -c "
        echo 'Testing basic functionality...'
        which docker && echo '✓ Docker CLI available'
        which kubectl && echo '✓ kubectl available'
        which helm && echo '✓ Helm available'
        which git && echo '✓ Git available'
        which jq && echo '✓ jq available'
        ls -la /actions-runner/ | head -5 && echo '✓ Actions runner installed'
        echo 'Basic tests completed successfully'
    "

    if [[ $? -eq 0 ]]; then
        log_success "Image testing completed successfully"
    else
        log_error "Image testing failed"
        exit 1
    fi
}

# Push to registry function
push_image() {
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        log_info "Pushing image to registry..."

        docker push "$FULL_IMAGE_NAME"
        docker push "${IMAGE_NAME}:latest"

        if [[ $? -eq 0 ]]; then
            log_success "Image pushed successfully to registry"
        else
            log_error "Failed to push image to registry"
            exit 1
        fi
    else
        log_info "Skipping push to registry (use --push to enable)"
    fi
}

# Main execution
main() {
    log_info "=== OSP Custom GitHub Runner Build Script ==="

    # Change to script directory
    cd "$(dirname "$0")"

    # Verify Dockerfile exists
    if [[ ! -f "Dockerfile" ]]; then
        log_error "Dockerfile not found in current directory"
        exit 1
    fi

    # Build image
    build_image

    # Test image
    test_image

    # Push if requested
    push_image

    log_success "Build process completed successfully!"
    log_info "Image: $FULL_IMAGE_NAME"

    # Show usage example
    echo ""
    log_info "=== Usage Example ==="
    echo "docker run -d \\"
    echo "  --name osp-runner \\"
    echo "  -e REPO_URL=\"https://github.com/ospgroupvn/k8s-deployment\" \\"
    echo "  -e ACCESS_TOKEN=\"your-token\" \\"
    echo "  -e RUNNER_NAME=\"osp-test-runner\" \\"
    echo "  -e RUNNER_LABELS=\"self-hosted,linux,x64,docker,osp-custom\" \\"
    echo "  -v /var/run/docker.sock:/var/run/docker.sock \\"
    echo "  $FULL_IMAGE_NAME"
}

# Run main function
main "$@"