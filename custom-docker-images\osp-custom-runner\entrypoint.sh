#!/bin/bash
set -e

# OSP Custom GitHub Actions Runner Entrypoint (Ubuntu)
# Tự động đăng ký và chạy GitHub Actions runner trên Ubuntu base image

echo "=== OSP Custom GitHub Actions Runner (Ubuntu) ==="
echo "Đang khởi động GitHub Actions Runner với .NET 8.0 trên Ubuntu..."

# Thiết lập màu sắc cho log
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[THÔNG TIN]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[THÀNH CÔNG]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[CẢNH BÁO]${NC} $1"
}

log_error() {
    echo -e "${RED}[LỖI]${NC} $1"
}

# Kiểm tra các biến môi trường bắt buộc
check_required_vars() {
    log_info "Đang kiểm tra các biến môi trường bắt buộc..."

    # Debug và trim whitespace và newline từ ACCESS_TOKEN và RUNNER_TOKEN ngay từ đầu
    if [[ -n "$ACCESS_TOKEN" ]]; then
        log_info "ACCESS_TOKEN trước khi trim:"
        echo "Length: ${#ACCESS_TOKEN}"
        echo "Raw value (with xxd): $(echo -n "$ACCESS_TOKEN" | xxd -l 100)"
        echo "Ends with: $(echo -n "$ACCESS_TOKEN" | tail -c 5 | xxd)"
        
        ACCESS_TOKEN=$(echo "$ACCESS_TOKEN" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        export ACCESS_TOKEN
        
        log_info "ACCESS_TOKEN sau khi trim:"
        echo "Length: ${#ACCESS_TOKEN}"
        echo "First 20 chars: ${ACCESS_TOKEN:0:20}..."
        echo "Last 10 chars: ...${ACCESS_TOKEN: -10}"
        log_info "Đã trim ACCESS_TOKEN"
    fi

    if [[ -n "$RUNNER_TOKEN" ]]; then
        log_info "RUNNER_TOKEN trước khi trim:"
        echo "Length: ${#RUNNER_TOKEN}"
        echo "Raw value (with xxd): $(echo -n "$RUNNER_TOKEN" | xxd -l 100)"
        
        RUNNER_TOKEN=$(echo "$RUNNER_TOKEN" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        export RUNNER_TOKEN
        
        log_info "RUNNER_TOKEN sau khi trim:"
        echo "Length: ${#RUNNER_TOKEN}"
        echo "First 20 chars: ${RUNNER_TOKEN:0:20}..."
        log_info "Đã trim RUNNER_TOKEN"
    fi

    # REPO_URL là bắt buộc
    if [[ -z "$REPO_URL" ]]; then
        log_error "Biến môi trường REPO_URL là bắt buộc"
        log_info "Ví dụ: REPO_URL=https://github.com/ospgroupvn/k8s-deployment"
        exit 1
    fi

    # Cần ít nhất một trong các token
    if [[ -z "$ACCESS_TOKEN" && -z "$RUNNER_TOKEN" ]]; then
        log_error "Phải cung cấp ACCESS_TOKEN hoặc RUNNER_TOKEN"
        log_info "ACCESS_TOKEN: Personal Access Token (khuyến nghị cho automation)"
        log_info "RUNNER_TOKEN: Registration token từ GitHub (hết hạn sau 1 giờ)"
        exit 1
    fi

    log_success "Các biến môi trường bắt buộc đã được thiết lập"
}

# Thiết lập default values
setup_defaults() {
    log_info "Đang thiết lập các giá trị mặc định..."

    # Thiết lập RUNNER_NAME nếu chưa có
    if [[ -z "$RUNNER_NAME" ]]; then
        if [[ -n "$RUNNER_NAME_PREFIX" ]]; then
            RUNNER_NAME="${RUNNER_NAME_PREFIX}-$(hostname)"
        else
            RUNNER_NAME="osp-runner-$(date +%s)"
        fi
        export RUNNER_NAME
        log_info "Đã tạo tên runner: $RUNNER_NAME"
    fi

    # Thiết lập RUNNER_WORKDIR
    if [[ -z "$RUNNER_WORKDIR" ]]; then
        RUNNER_WORKDIR="_work"
        export RUNNER_WORKDIR
    fi

    # Thiết lập labels mặc định
    if [[ -z "$RUNNER_LABELS" ]]; then
        RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom"
        export RUNNER_LABELS
        log_info "Sử dụng labels mặc định: $RUNNER_LABELS"
    fi

    log_success "Đã cấu hình các giá trị mặc định"
}

# Cleanup function
cleanup() {
    log_info "=== BẮT ĐẦU QUÁ TRÌNH DỌN DẸP ==="

    # Stop the runner listener gracefully if it's running
    if [[ -n "$RUNNER_PID" ]] && kill -0 "$RUNNER_PID" 2>/dev/null; then
        log_info "Đang dừng GitHub Actions Runner (PID: $RUNNER_PID)..."
        kill -TERM "$RUNNER_PID" 2>/dev/null || true

        # Wait for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 "$RUNNER_PID" 2>/dev/null; then
                log_success "Runner đã dừng một cách nhẹ nhàng"
                break
            fi
            log_info "Đang đợi runner dừng lại... ($i/10)"
            sleep 2
        done

        # Force kill if still running
        if kill -0 "$RUNNER_PID" 2>/dev/null; then
            log_warning "Buộc phải kill process runner"
            kill -KILL "$RUNNER_PID" 2>/dev/null || true
        fi
    else
        log_info "Không tìm thấy runner process hoặc đã dừng"
    fi

    # Auto-deregistration
    if [[ "$DISABLE_AUTO_DEREGISTRATION" != "true" ]]; then
        log_info "Auto-deregistering runner from GitHub..."

        # Change to actions-runner directory for deregistration
        cd /actions-runner || {
            log_error "Failed to change to /actions-runner directory"
            return 1
        }

        # Check if runner is configured
        if [[ -f ".runner" ]]; then
            local runner_name
            runner_name=$(jq -r '.agentName // "unknown"' .runner 2>/dev/null)
            log_info "Deregistering runner: $runner_name"

            # Get registration token for deregistration
            local dereg_token
            dereg_token=$(/register-runner.sh get_registration_token 2>/dev/null)
            
            if [[ -n "$dereg_token" && "$dereg_token" != "ERROR" ]]; then
                log_info "Attempting to remove runner registration..."
                if ./config.sh remove --token "$dereg_token" --unattended; then
                    log_success "Runner deregistered successfully"
                else
                    log_warning "Failed to deregister runner (may already be removed)"
                fi
            else
                log_warning "Could not get registration token for deregistration"
            fi
        else
            log_info "No runner configuration found, skipping deregistration"
        fi
    else
        log_info "Auto-deregistration disabled, skipping..."
    fi

    # Cleanup temporary files
    log_info "Cleaning up temporary files..."
    rm -rf /tmp/github-* /tmp/runner-* 2>/dev/null || true

    log_success "=== CLEANUP PROCESS COMPLETED ==="
}

# Graceful shutdown function
graceful_shutdown() {
    log_warning "=== RECEIVED SHUTDOWN SIGNAL ==="
    log_info "Initiating graceful shutdown..."

    # Set flag to prevent new jobs
    SHUTDOWN_INITIATED=true
    export SHUTDOWN_INITIATED

    cleanup
    exit 0
}

# Setup signal handlers for graceful shutdown
trap graceful_shutdown SIGTERM SIGINT SIGQUIT


# Kiểm tra Docker socket availability
check_docker() {
    log_info "Checking Docker socket availability..."

    if command -v docker >/dev/null 2>&1; then
        # Kiểm tra Docker socket có tồn tại không
        if [ ! -S /var/run/docker.sock ]; then
            log_error "Docker socket not found at /var/run/docker.sock"
            log_error "Make sure to mount Docker socket: -v /var/run/docker.sock:/var/run/docker.sock"
            exit 1
        fi

        # Kiểm tra quyền truy cập Docker socket
        if ! docker info >/dev/null 2>&1; then
            log_warning "Docker socket exists but not accessible, trying to fix permissions..."

            # Thử fix quyền Docker socket
            sudo chown root:docker /var/run/docker.sock 2>/dev/null || true
            sudo chmod 660 /var/run/docker.sock 2>/dev/null || true

            # Kiểm tra lại
            if docker info >/dev/null 2>&1; then
                log_success "Docker socket permissions fixed"
            else
                log_error "Cannot access Docker daemon. Please check:"
                log_error "1. Docker daemon is running on host"
                log_error "2. Docker socket is properly mounted"
                log_error "3. User has permission to access Docker socket"
                exit 1
            fi
        else
            log_success "Docker is accessible and working"
        fi

        # Hiển thị thông tin Docker
        log_info "Docker version information:"
        docker version --format "Client: {{.Client.Version}}, Server: {{.Server.Version}}" 2>/dev/null || docker version

    else
        log_error "Docker CLI not found"
        exit 1
    fi
}

# Kiểm tra và tạo thư mục làm việc
setup_workdir() {
    log_info "Setting up working directory..."

    local workdir="/actions-runner/$RUNNER_WORKDIR"

    if [[ ! -d "$workdir" ]]; then
        mkdir -p "$workdir"
        log_info "Created working directory: $workdir"
    fi

    log_success "Working directory ready: $workdir"
}

# In thông tin cấu hình
print_config() {
    log_info "=== Runner Configuration ==="
    echo "  Repository URL: $REPO_URL"
    echo "  Runner Name: $RUNNER_NAME"
    echo "  Runner Labels: $RUNNER_LABELS"
    echo "  Working Directory: $RUNNER_WORKDIR"
    echo "  Runner Group: $RUNNER_GROUP"
    echo "  Runner Scope: $RUNNER_SCOPE"

    if [[ -n "$ORG_NAME" ]]; then
        echo "  Organization: $ORG_NAME"
    fi

    if [[ -n "$ENTERPRISE_NAME" ]]; then
        echo "  Enterprise: $ENTERPRISE_NAME"
    fi

    echo "  Auto-deregistration: $([ "$DISABLE_AUTO_DEREGISTRATION" = "true" ] && echo "Disabled" || echo "Enabled")"
    echo "==================================="
}

# Kiểm tra system requirements
check_system_requirements() {
    log_info "Checking system requirements..."

    # Kiểm tra .NET runtime
    if dotnet --version >/dev/null 2>&1; then
        local dotnet_version=$(dotnet --version)
        log_success ".NET runtime available: $dotnet_version"
    else
        log_warning ".NET runtime not found - runner may have issues"
    fi

    # Kiểm tra các tools cần thiết
    local tools=("git" "curl" "jq" "kubectl" "helm" "gh")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=$($tool --version 2>/dev/null | head -1 || echo "unknown")
            log_success "$tool available: $version"
        else
            log_warning "$tool not found"
        fi
    done

    # Kiểm tra disk space
    local available_space=$(df -h /actions-runner | awk 'NR==2 {print $4}')
    log_info "Available disk space: $available_space"

    # Kiểm tra memory
    local available_memory=$(free -h | awk 'NR==2{printf "%.1fG", $7/1024}')
    log_info "Available memory: $available_memory"
}

# Main execution
main() {
    log_info "OSP Custom GitHub Actions Runner starting..."
    log_info "Container started at: $(date)"
    log_info "Hostname: $(hostname)"
    log_info "User: $(whoami)"

    # Kiểm tra requirements
    check_required_vars
    setup_defaults
    check_system_requirements
    print_config

    # Kiểm tra môi trường
    check_docker
    setup_workdir

    # Đăng ký runner
    log_info "Registering runner with GitHub..."
    if ! /register-runner.sh; then
        log_error "Failed to register runner"
        log_error "Please check:"
        log_error "1. ACCESS_TOKEN has correct permissions (repo or admin:org)"
        log_error "2. REPO_URL is correct and accessible"
        log_error "3. Network connectivity to GitHub"
        exit 1
    fi

    log_success "Runner registered successfully"

    # Verify registration
    if [[ -f "/actions-runner/.runner" ]]; then
        log_info "Runner configuration:"
        cat /actions-runner/.runner | jq '.' 2>/dev/null || cat /actions-runner/.runner
    fi

    # Chạy runner
    log_info "Starting GitHub Actions Runner listener..."
    log_info "Runner is now ready to accept jobs from GitHub Actions"

    # Change to runner directory and start
    cd /actions-runner || {
        log_error "Failed to change to /actions-runner directory"
        exit 1
    }

    # Start runner in background để có thể nhận signals
    ./run.sh &
    RUNNER_PID=$!

    # Wait for runner process, cho phép signal interruption
    wait $RUNNER_PID
    
    # Nếu đến đây, runner đã thoát, chạy cleanup
    log_info "Runner process exited, running cleanup..."
    cleanup
}

# Chạy main function
main "$@"