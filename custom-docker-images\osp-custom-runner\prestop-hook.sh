#!/bin/bash
set -e

# OSP Custom GitHub Actions Runner PreStop Hook
# Dùng trong Kubernetes preStop lifecycle hook để cleanup tr<PERSON><PERSON><PERSON> khi pod terminate

# Thiế<PERSON> lập màu sắc cho log
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[PRESTOP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PRESTOP]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[PRESTOP]${NC} $1"
}

log_error() {
    echo -e "${RED}[PRESTOP]${NC} $1"
}

# Main prestop function
prestop_cleanup() {
    log_info "=== KUBERNETES PRESTOP HOOK STARTED ==="

    # Wait a moment for any running jobs to potentially complete
    log_info "Waiting 10 seconds for any running jobs to complete..."
    sleep 10

    # Check if runner is currently processing a job
    if pgrep -f "Runner.Worker" > /dev/null 2>&1; then
        log_warning "Runner is currently processing a job, waiting up to 60 seconds..."

        # Wait up to 60 seconds for job to complete
        for i in {1..12}; do
            if ! pgrep -f "Runner.Worker" > /dev/null 2>&1; then
                log_success "Job completed, proceeding with cleanup"
                break
            fi
            log_info "Still processing job, waiting... ($((i*5))/60 seconds)"
            sleep 5
        done

        # If still processing after 60 seconds, proceed anyway
        if pgrep -f "Runner.Worker" > /dev/null 2>&1; then
            log_warning "Job still running after 60 seconds, proceeding with cleanup anyway"
        fi
    fi

    # Send SIGTERM to main process to trigger graceful shutdown
    local main_pid=""
    main_pid=$(pgrep -f "/entrypoint.sh" 2>/dev/null | head -1)

    if [[ -n "$main_pid" ]]; then
        log_info "Sending SIGTERM to main process (PID: $main_pid)"
        kill -TERM "$main_pid" 2>/dev/null || true

        # Wait for graceful shutdown
        for i in {1..15}; do
            if ! kill -0 "$main_pid" 2>/dev/null; then
                log_success "Main process terminated gracefully"
                break
            fi
            log_info "Waiting for graceful shutdown... ($i/15)"
            sleep 2
        done
    fi

    # If auto-deregistration is enabled, ensure it happens
    if [[ "$DISABLE_AUTO_DEREGISTRATION" != "true" ]]; then
        log_info "Ensuring runner deregistration..."

        # Change to actions-runner directory
        cd /actions-runner 2>/dev/null || {
            log_warning "Could not change to /actions-runner directory"
            exit 0
        }

        # Check if runner is configured
        if [[ -f ".runner" ]]; then
            local runner_name
            runner_name=$(jq -r '.agentName // "unknown"' .runner 2>/dev/null)
            log_info "Deregistering runner: $runner_name"

            # Run deregistration
            if /deregister-runner.sh; then
                log_success "Runner deregistered successfully"
            else
                log_warning "Failed to deregister runner"
            fi
        else
            log_info "No runner configuration found, skipping deregistration"
        fi
    else
        log_info "Auto-deregistration disabled"
    fi

    log_success "=== KUBERNETES PRESTOP HOOK COMPLETED ==="
}

# Main execution
main() {
    # Set timeout for the entire prestop process (Kubernetes default terminationGracePeriodSeconds is 30s)
    timeout 25s prestop_cleanup || {
        log_warning "PreStop hook timed out after 25 seconds"
        exit 0
    }
}

# Run main function
main "$@"