#!/bin/bash
set -e

# OSP Custom GitHub Actions Runner Registration Script
# Đăng ký runner v<PERSON>i GitHub Actions

# Thiết lập màu sắc cho log
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function để lấy registration token từ GitHub API
get_registration_token() {
    local url=""
    local api_url=""
    local token_field="token"

    # Redirect log output to stderr to avoid contaminating the token
    log_info "Fetching registration token from GitHub API..." >&2

    # Debug và trim whitespace và newline từ ACCESS_TOKEN
    log_info "ACCESS_TOKEN debug info trước khi trim:" >&2
    echo "Length: ${#ACCESS_TOKEN}" >&2
    echo "Raw value (with xxd): $(echo -n "$ACCESS_TOKEN" | xxd -l 100)" >&2
    echo "Ends with: $(echo -n "$ACCESS_TOKEN" | tail -c 5 | xxd)" >&2
    
    ACCESS_TOKEN=$(echo "$ACCESS_TOKEN" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    
    log_info "ACCESS_TOKEN debug info sau khi trim:" >&2
    echo "Length: ${#ACCESS_TOKEN}" >&2
    echo "First 20 chars: ${ACCESS_TOKEN:0:20}..." >&2
    echo "Last 10 chars: ...${ACCESS_TOKEN: -10}" >&2

    # Xác định API endpoint dựa trên scope
    case "$RUNNER_SCOPE" in
        "repo")
            # Repository level
            local repo_path=$(echo "$REPO_URL" | sed 's|https://github.com/||' | sed 's|/$||')
            api_url="https://api.github.com/repos/${repo_path}/actions/runners/registration-token"
            ;;
        "org")
            # Organization level
            if [[ -z "$ORG_NAME" ]]; then
                log_error "ORG_NAME must be set when RUNNER_SCOPE=org"
                return 1
            fi
            api_url="https://api.github.com/orgs/${ORG_NAME}/actions/runners/registration-token"
            ;;
        "enterprise")
            # Enterprise level
            if [[ -z "$ENTERPRISE_NAME" ]]; then
                log_error "ENTERPRISE_NAME must be set when RUNNER_SCOPE=enterprise"
                return 1
            fi
            api_url="https://api.github.com/enterprises/${ENTERPRISE_NAME}/actions/runners/registration-token"
            ;;
        *)
            log_error "Invalid RUNNER_SCOPE: $RUNNER_SCOPE. Must be 'repo', 'org', or 'enterprise'"
            return 1
            ;;
    esac

    log_info "Using API endpoint: $api_url" >&2
    log_info "ACCESS_TOKEN length: ${#ACCESS_TOKEN}" >&2
    log_info "ACCESS_TOKEN starts with: ${ACCESS_TOKEN:0:4}..." >&2

    # Gọi GitHub API để lấy registration token
    local response
    log_info "Calling GitHub API..." >&2
    response=$(curl -s -X POST \
        -H "Accept: application/vnd.github.v3+json" \
        -H "Authorization: token $ACCESS_TOKEN" \
        -H "User-Agent: OSP-Custom-Runner/1.0" \
        "$api_url" 2>&1)

    local curl_exit_code=$?
    log_info "Curl exit code: $curl_exit_code" >&2
    
    if [[ $curl_exit_code -ne 0 ]]; then
        log_error "Failed to call GitHub API (curl exit code: $curl_exit_code)" >&2
        log_error "Response: $response" >&2
        return 1
    fi

    log_info "GitHub API response received (length: ${#response})" >&2
    log_info "Response preview: ${response:0:200}..." >&2

    # Kiểm tra response có chứa error không
    local error_message
    error_message=$(echo "$response" | jq -r '.message // empty' 2>/dev/null)

    if [[ -n "$error_message" ]]; then
        log_error "GitHub API error: $error_message" >&2
        log_error "Full response: $response" >&2
        
        # Kiểm tra xem có phải lỗi authentication không
        local documentation_url
        documentation_url=$(echo "$response" | jq -r '.documentation_url // empty' 2>/dev/null)
        if [[ -n "$documentation_url" ]]; then
            log_error "Documentation URL: $documentation_url" >&2
        fi
        
        return 1
    fi

    # Lấy token từ response
    local registration_token
    registration_token=$(echo "$response" | jq -r ".${token_field} // empty" 2>/dev/null)

    if [[ -z "$registration_token" || "$registration_token" == "null" ]]; then
        log_error "Failed to extract registration token from API response" >&2
        log_error "Response: $response" >&2
        return 1
    fi

    # Debug và trim registration token để loại bỏ newline characters
    log_info "Registration token debug info trước khi trim:" >&2
    echo "Length: ${#registration_token}" >&2
    echo "Raw value (with xxd): $(echo -n "$registration_token" | xxd -l 100)" >&2
    
    # Trim whitespace và newline từ registration token
    registration_token=$(echo "$registration_token" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    
    log_info "Registration token debug info sau khi trim:" >&2
    echo "Length: ${#registration_token}" >&2
    echo "Token: ${registration_token}" >&2

    # Return only the clean token to stdout
    echo "$registration_token"
    return 0
}

# Function để đăng ký runner
register_runner() {
    local registration_token=""

    # Lấy registration token
    if [[ -n "$RUNNER_TOKEN" ]]; then
        log_info "Using provided RUNNER_TOKEN"
        
        # Debug RUNNER_TOKEN trước khi trim
        log_info "RUNNER_TOKEN debug info trước khi trim:"
        echo "Length: ${#RUNNER_TOKEN}"
        echo "Raw value (with xxd): $(echo -n "$RUNNER_TOKEN" | xxd -l 100)"
        
        # Trim whitespace và newline từ RUNNER_TOKEN
        RUNNER_TOKEN=$(echo "$RUNNER_TOKEN" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        
        log_info "RUNNER_TOKEN debug info sau khi trim:"
        echo "Length: ${#RUNNER_TOKEN}"
        echo "First 20 chars: ${RUNNER_TOKEN:0:20}..."
        
        registration_token="$RUNNER_TOKEN"
    elif [[ -n "$ACCESS_TOKEN" ]]; then
        log_info "Fetching registration token using ACCESS_TOKEN"
        # Capture only stdout from get_registration_token, let stderr go to console
        registration_token=$(get_registration_token)
        local get_token_exit_code=$?
        
        if [[ $get_token_exit_code -ne 0 || -z "$registration_token" ]]; then
            log_error "Failed to get registration token"
            return 1
        fi
        
        # Trim registration token để loại bỏ newline characters
        registration_token=$(echo "$registration_token" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        log_success "Registration token obtained and cleaned"
        log_info "Token length: ${#registration_token}"
    else
        log_error "No authentication token provided"
        return 1
    fi

    # Chuẩn bị arguments cho config.sh
    local config_args=()

    # URL và token - ensure token is properly escaped
    config_args+=("--url" "$REPO_URL")
    
    # Debug token trước khi truyền vào config.sh
    log_info "Final token validation trước khi config:"
    echo "Token length: ${#registration_token}"
    echo "Token (first 10): ${registration_token:0:10}..."
    echo "Token (last 10): ...${registration_token: -10}"
    echo "Token hex dump:"
    echo -n "$registration_token" | xxd | head -3
    
    # Check for any invisible characters
    local cleaned_token
    cleaned_token=$(echo -n "$registration_token" | tr -cd '[:alnum:]')
    if [[ "$cleaned_token" != "$registration_token" ]]; then
        log_warning "Token contains non-alphanumeric characters, cleaning..."
        registration_token="$cleaned_token"
        log_info "Cleaned token length: ${#registration_token}"
    fi
    
    config_args+=("--token" "$registration_token")

    # Runner name
    config_args+=("--name" "$RUNNER_NAME")

    # Runner group (nếu có)
    if [[ -n "$RUNNER_GROUP" && "$RUNNER_GROUP" != "default" ]]; then
        config_args+=("--runnergroup" "$RUNNER_GROUP")
    fi

    # Labels
    if [[ -n "$RUNNER_LABELS" ]]; then
        config_args+=("--labels" "$RUNNER_LABELS")
    fi

    # Working directory
    config_args+=("--work" "$RUNNER_WORKDIR")

    # Unattended mode
    config_args+=("--unattended")

    # Replace existing runner with same name
    config_args+=("--replace")

    log_info "Configuring runner with arguments: ${config_args[*]}"

    # Kiểm tra .NET runtime trước khi chạy config.sh
    if ! dotnet --version >/dev/null 2>&1; then
        log_warning ".NET runtime not found, config.sh may fail"
    fi

    # Chạy config.sh với timeout và retry logic
    local max_attempts=3
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "Registration attempt $attempt/$max_attempts"

        # Debug command line before execution
        log_info "About to execute config.sh with following arguments:"
        for i in "${!config_args[@]}"; do
            if [[ "${config_args[$i]}" == "--token" ]]; then
                echo "  [$i]: ${config_args[$i]}"
                echo "  [$((i+1))]: ***hidden***"
                i=$((i+1))
            else
                echo "  [$i]: ${config_args[$i]}"
            fi
        done
        
        # Save token to file for debugging
        echo -n "$registration_token" > /tmp/debug_token.txt
        log_info "Token saved to /tmp/debug_token.txt for inspection"
        log_info "Token file size: $(wc -c < /tmp/debug_token.txt) bytes"
        
        # Unset ACCESS_TOKEN để tránh config.sh sử dụng nó
        unset ACCESS_TOKEN

        if timeout 120 ./config.sh "${config_args[@]}" 2>&1; then
            log_success "Runner configured successfully"

            # Verify configuration files were created
            if [[ -f ".runner" && -f ".credentials" ]]; then
                log_success "Configuration files created successfully"
                
                # In thông tin runner đã tạo
                log_info "Runner configuration details:"
                if command -v jq >/dev/null 2>&1; then
                    cat .runner | jq '.' 2>/dev/null || cat .runner
                else
                    cat .runner
                fi
                
                return 0
            else
                log_warning "Configuration completed but files not found"
                log_info "Files in directory:"
                ls -la
            fi
        else
            local exit_code=$?
            log_warning "Registration attempt $attempt failed (exit code: $exit_code)"

            if [ $attempt -lt $max_attempts ]; then
                log_info "Waiting 10 seconds before retry..."
                sleep 10
            fi
        fi

        attempt=$((attempt + 1))
    done

    log_error "Failed to configure runner after $max_attempts attempts"
    return 1
}

# Kiểm tra xem runner đã được cấu hình chưa
check_existing_config() {
    if [[ -f ".runner" ]]; then
        log_warning "Runner configuration already exists"
        local existing_name
        existing_name=$(jq -r '.agentName // empty' .runner 2>/dev/null)
        if [[ -n "$existing_name" ]]; then
            log_info "Existing runner name: $existing_name"
        fi

        # Nếu tên runner khác, reconfigure
        if [[ "$existing_name" != "$RUNNER_NAME" ]]; then
            log_info "Runner name changed, reconfiguring..."
            rm -f .runner .credentials
            return 1
        fi

        log_info "Runner configuration is up to date"
        return 0
    fi

    return 1
}

# Main execution
main() {
    # Handle special case for getting registration token only
    if [[ "$1" == "get_registration_token" ]]; then
        get_registration_token
        exit $?
    fi

    log_info "Starting runner registration process..."

    # Chuyển đến thư mục actions-runner
    cd /actions-runner || {
        log_error "Failed to change to /actions-runner directory"
        exit 1
    }

    # Kiểm tra cấu hình hiện tại
    if check_existing_config; then
        log_success "Runner already configured"
        exit 0
    fi

    # Đăng ký runner
    if register_runner; then
        log_success "Runner registration completed successfully"

        # Hiển thị thông tin cấu hình
        if [[ -f ".runner" ]]; then
            log_info "Runner configuration:"
            cat .runner | jq '.' 2>/dev/null || cat .runner
        fi

        exit 0
    else
        log_error "Runner registration failed"
        exit 1
    fi
}

# Chạy main function
main "$@"