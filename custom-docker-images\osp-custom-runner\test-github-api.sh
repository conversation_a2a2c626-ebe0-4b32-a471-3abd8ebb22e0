#!/bin/bash

# Test GitHub API connectivity from within container

echo "=== Testing GitHub API from Container ==="

# Test basic API access
echo "Test 1: Basic GitHub API access"
curl -s -w "Status: %{http_code}\n" https://api.github.com/ | head -1

echo
echo "Test 2: Test with User-Agent"
curl -s -H "User-Agent: OSP-Test/1.0" -w "Status: %{http_code}\n" https://api.github.com/ | head -1

echo
echo "Test 3: Test repository access (no auth)"
curl -s -w "Status: %{http_code}\n" https://api.github.com/repos/ospgroupvn/k8s-deployment | head -1

echo
echo "Test 4: Test rate limit"
curl -s https://api.github.com/rate_limit | jq '.rate' 2>/dev/null || echo "Rate limit check failed"

# Test with ACCESS_TOKEN if provided
if [[ -n "$ACCESS_TOKEN" ]]; then
    echo
    echo "Test 5: Test with ACCESS_TOKEN"
    
    # Debug ACCESS_TOKEN
    echo "ACCESS_TOKEN debug info:"
    echo "Length: ${#ACCESS_TOKEN}"
    echo "Raw value (with xxd): $(echo -n "$ACCESS_TOKEN" | xxd -l 100)"
    echo "Starts with: ${ACCESS_TOKEN:0:4}..."
    echo "Ends with: ...${ACCESS_TOKEN: -4}"
    
    # Trim ACCESS_TOKEN
    ACCESS_TOKEN_CLEAN=$(echo "$ACCESS_TOKEN" | tr -d '\n\r' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    echo "After cleaning - Length: ${#ACCESS_TOKEN_CLEAN}"
    
    # Test authenticated API call
    echo "Testing authenticated repository access:"
    response=$(curl -s -H "Authorization: token $ACCESS_TOKEN_CLEAN" \
        -H "User-Agent: OSP-Test/1.0" \
        -w "\nStatus: %{http_code}\n" \
        https://api.github.com/repos/ospgroupvn/k8s-deployment)
    
    echo "$response"
    
    # Test registration token endpoint
    echo
    echo "Test 6: Test registration token endpoint"
    curl -s -X POST \
        -H "Accept: application/vnd.github.v3+json" \
        -H "Authorization: token $ACCESS_TOKEN_CLEAN" \
        -H "User-Agent: OSP-Test/1.0" \
        -w "\nStatus: %{http_code}\n" \
        https://api.github.com/repos/ospgroupvn/k8s-deployment/actions/runners/registration-token
        
else
    echo
    echo "Test 5-6: Skipped - No ACCESS_TOKEN provided"
fi

echo "=== GitHub API Test Complete ==="
