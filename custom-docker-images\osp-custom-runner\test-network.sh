#!/bin/bash

# Test network connectivity to GitHub API

echo "=== Testing Network Connectivity to GitHub API ==="

echo "Test 1: Basic connectivity to GitHub"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" https://api.github.com/

echo
echo "Test 2: Check GitHub API rate limit (no auth)"
curl -s https://api.github.com/rate_limit | jq '.'

echo
echo "Test 3: Test DNS resolution"
nslookup api.github.com

echo
echo "Test 4: Test HTTPS connectivity"
openssl s_client -connect api.github.com:443 -servername api.github.com < /dev/null 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "SSL test failed"

echo "=== Network Test Complete ==="
