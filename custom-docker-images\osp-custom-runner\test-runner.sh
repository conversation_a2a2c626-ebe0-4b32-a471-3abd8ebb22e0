#!/bin/bash
set -e

# OSP Custom Runner Test Script
echo "=== Testing OSP Custom Runner với .NET 8.0 trên Ubuntu ==="

IMAGE_NAME="dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[THÔNG TIN]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[THÀNH CÔNG]${NC} $1"
}

log_error() {
    echo -e "${RED}[LỖI]${NC} $1"
}

# Test dependencies
test_dependencies() {
    log_info "Kiểm tra dependencies cơ bản..."
    
    docker run --rm --entrypoint="" "$IMAGE_NAME" bash -c "
        echo 'Checking .NET Runtime:'
        dotnet --version && echo '✓ .NET runtime'
        
        echo 'Checking Docker:'
        docker --version && echo '✓ Docker CLI'
        
        echo 'Checking GitHub Actions Runner:'
        ls /actions-runner/config.sh && echo '✓ Actions runner'
    "
}

# Test runner registration
test_registration() {
    log_info "Test registration với GitHub token..."
    
    if [[ -n "$GITHUB_TOKEN" ]]; then
        docker run --rm --privileged \
            -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
            -e ACCESS_TOKEN="$GITHUB_TOKEN" \
            -e RUNNER_NAME="test-runner-$(date +%s)" \
            "$IMAGE_NAME" bash -c "timeout 30 /register-runner.sh || echo 'Registration test done'"
    else
        log_error "Cần GITHUB_TOKEN để test registration"
    fi
}

# Main
main() {
    if ! docker image inspect "$IMAGE_NAME" >/dev/null 2>&1; then
        log_error "Image $IMAGE_NAME chưa được build"
        exit 1
    fi
    
    test_dependencies
    test_registration
    
    log_success "Test hoàn thành!"
}

main "$@"
