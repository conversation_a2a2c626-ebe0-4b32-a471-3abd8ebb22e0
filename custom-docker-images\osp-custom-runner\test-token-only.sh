#!/bin/bash

# Test script to get registration token only

export ACCESS_TOKEN="****************************************"
export REPO_URL="https://github.com/ospgroupvn/k8s-deployment"

echo "=== Testing Registration Token Retrieval ==="

# Test GitHub API call
echo "Test 1: Direct curl test"
curl -s -X POST \
    -H "Accept: application/vnd.github.v3+json" \
    -H "Authorization: token $ACCESS_TOKEN" \
    -H "User-Agent: OSP-Test/1.0" \
    https://api.github.com/repos/ospgroupvn/k8s-deployment/actions/runners/registration-token

echo
echo
echo "Test 2: With jq parsing"
response=$(curl -s -X POST \
    -H "Accept: application/vnd.github.v3+json" \
    -H "Authorization: token $ACCESS_TOKEN" \
    -H "User-Agent: OSP-Test/1.0" \
    https://api.github.com/repos/ospgroupvn/k8s-deployment/actions/runners/registration-token)

echo "Response: $response"
echo

token=$(echo "$response" | jq -r '.token // empty' 2>/dev/null)
echo "Extracted token: $token"
echo "Token length: ${#token}"
echo "Token with xxd:"
echo -n "$token" | xxd
echo

echo "Test 3: Test ASCII validation"
if [[ "$token" =~ [^[:ascii:]] ]]; then
    echo "Token contains non-ASCII characters!"
else
    echo "Token contains only ASCII characters"
fi

echo "Test 4: Character analysis"
echo -n "$token" | od -c