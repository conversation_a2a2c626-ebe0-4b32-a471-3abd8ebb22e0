@echo off
REM Test script cho OSP Custom GitHub Runner với token thật trên Windows CLI
REM Sử dụng: test-with-token-win.bat <GITHUB_TOKEN>

setlocal enabledelayedexpansion

REM Cấu hình
set IMAGE_NAME=dockerhub.ospgroup.vn/osp-public/osp-custom-runner:2.0
set CONTAINER_NAME=osp-runner-test
set REPO_URL=https://github.com/ospgroupvn/k8s-deployment
set RUNNER_NAME=osp-test-runner-%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set RUNNER_LABELS=self-hosted,windows,x64,docker,osp-custom,test

REM Hàm log
set RED=
set GREEN=
set YELLOW=
set BLUE=
set NC=

:log_info
    echo [INFO] %~1
    goto :eof
:log_success
    echo [SUCCESS] %~1
    goto :eof
:log_warning
    echo [WARNING] %~1
    goto :eof
:log_error
    echo [ERROR] %~1
    goto :eof

REM Kiểm tra tham số
if "%~1"=="" (
    call :log_error "Usage: %~nx0 <GITHUB_TOKEN>"
    call :log_info "Example: %~nx0 ghp_xxxxxxxxxxxxxxxxxxxx"
    exit /b 1
)
set ACCESS_TOKEN=%~1

REM Cleanup container nếu tồn tại
for /f "tokens=1*" %%i in ('docker ps -a --format "{{.Names}}"') do (
    if "%%i"=="%CONTAINER_NAME%" (
        call :log_info "Stopping and removing container: %CONTAINER_NAME%"
        docker stop %CONTAINER_NAME% >nul 2>&1
        docker rm %CONTAINER_NAME% >nul 2>&1
    )
)

call :log_info "=== OSP Custom GitHub Runner Test ==="
call :log_info "Image: %IMAGE_NAME%"
call :log_info "Repository: %REPO_URL%"
call :log_info "Runner Name: %RUNNER_NAME%"
call :log_info "Labels: %RUNNER_LABELS%"
echo.

REM Kiểm tra image
docker image inspect %IMAGE_NAME% >nul 2>&1
if errorlevel 1 (
    call :log_error "Image %IMAGE_NAME% not found. Please build nó trước."
    exit /b 1
)

REM Start container
call :log_info "Starting container..."
docker run -d --name %CONTAINER_NAME% --privileged -e REPO_URL="%REPO_URL%" -e ACCESS_TOKEN="%ACCESS_TOKEN%" -e RUNNER_NAME="%RUNNER_NAME%" -e RUNNER_LABELS="%RUNNER_LABELS%" -e RUNNER_WORKDIR="/_work" -v \\var\\run\\docker.sock:/var/run/docker.sock -v "%cd%\\runner-work:/actions-runner/_work" "%IMAGE_NAME%"
if errorlevel 1 (
    call :log_error "Không thể khởi động container!"
    exit /b 1
)
call :log_success "Container started: %CONTAINER_NAME%"

REM Đợi container khởi tạo
call :log_info "Waiting for container to initialize..."
choice /C Y /N /T 5 /D Y >nul

REM Kiểm tra trạng thái container
for /f "tokens=1,2*" %%i in ('docker ps --format "{{.Names}} {{.Status}}"') do (
    if "%%i"=="%CONTAINER_NAME%" (
        set CONTAINER_STATUS=%%j
    )
)
if not defined CONTAINER_STATUS (
    call :log_error "Container is not running!"
    call :log_info "Container logs:"
    docker logs %CONTAINER_NAME%
    exit /b 1
)
call :log_success "Container is running"

REM Hiển thị logs trong 30 giây
call :log_info "Monitoring container logs for 30 seconds..."
echo --- Container Logs ---
start "Container Logs" cmd /c "timeout /t 30 & exit"
docker logs -f %CONTAINER_NAME% >nul 2>&1
REM Không có lệnh timeout trực tiếp cho docker logs trên Windows, nên chỉ hiển thị logs

echo --- End of Logs ---

REM Kiểm tra runner đăng ký
call :log_info "Checking if runner is registered..."
docker logs %CONTAINER_NAME% | findstr /C:"Runner successfully added" >nul && call :log_success "Runner successfully registered with GitHub!"
docker logs %CONTAINER_NAME% | findstr /C:"Listening for Jobs" >nul && call :log_success "Runner is listening for jobs!"

REM Test Docker CLI trong container
call :log_info "Testing Docker functionality inside container..."
docker exec %CONTAINER_NAME% docker --version >nul 2>&1 && call :log_success "Docker CLI is working inside container" || call :log_error "Docker CLI is not working inside container"

REM Test các tool khác
call :log_info "Testing other tools..."
docker exec %CONTAINER_NAME% kubectl version --client --short 2>nul || call :log_warning "kubectl test failed"
docker exec %CONTAINER_NAME% helm version --short 2>nul || call :log_warning "helm test failed"
docker exec %CONTAINER_NAME% gh --version 2>nul || call :log_warning "gh CLI test failed"

echo.
call :log_info "=== Test Summary ==="
call :log_info "Container Name: %CONTAINER_NAME%"
call :log_info "Runner Name: %RUNNER_NAME%"
call :log_info "To view live logs: docker logs -f %CONTAINER_NAME%"
call :log_info "To stop container: docker stop %CONTAINER_NAME%"
call :log_info "To remove container: docker rm %CONTAINER_NAME%"
echo.
call :log_success "Test completed! Check GitHub repository settings để kiểm tra runner đã đăng ký chưa."

endlocal
