#!/bin/bash

# Test script cho OSP Custom GitHub Runner v<PERSON><PERSON> token thật
# Sử dụng: ./test-with-token.sh <GITHUB_TOKEN>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="dockerhub.ospgroup.vn/osp-public/osp-custom-runner:2.0"
CONTAINER_NAME="osp-runner-test"
REPO_URL="https://github.com/ospgroupvn/k8s-deployment"
RUNNER_NAME="osp-test-runner-$(date +%s)"
RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom,test"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

cleanup() {
    log_info "Cleaning up..."
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Stopping and removing container: ${CONTAINER_NAME}"
        docker stop "${CONTAINER_NAME}" >/dev/null 2>&1 || true
        docker rm "${CONTAINER_NAME}" >/dev/null 2>&1 || true
    fi
}

# Check arguments
if [ $# -ne 1 ]; then
    log_error "Usage: $0 <GITHUB_TOKEN>"
    log_info "Example: $0 ghp_xxxxxxxxxxxxxxxxxxxx"
    exit 1
fi

ACCESS_TOKEN="$1"

# Validate token format
if [[ ! "$ACCESS_TOKEN" =~ ^gh[ps]_[A-Za-z0-9_]{36,}$ ]]; then
    log_warning "Token format may be invalid. Expected format: ghp_... or ghs_..."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Cleanup any existing container
cleanup

# Trap to cleanup on exit
trap cleanup EXIT

log_info "=== OSP Custom GitHub Runner Test ==="
log_info "Image: ${IMAGE_NAME}"
log_info "Repository: ${REPO_URL}"
log_info "Runner Name: ${RUNNER_NAME}"
log_info "Labels: ${RUNNER_LABELS}"
echo

# Check if image exists
if ! docker image inspect "${IMAGE_NAME}" >/dev/null 2>&1; then
    log_error "Image ${IMAGE_NAME} not found. Please build it first."
    exit 1
fi

# Start container
log_info "Starting container..."
docker run -d \
    --name "${CONTAINER_NAME}" \
    --privileged \
    -e REPO_URL="${REPO_URL}" \
    -e ACCESS_TOKEN="${ACCESS_TOKEN}" \
    -e RUNNER_NAME="${RUNNER_NAME}" \
    -e RUNNER_LABELS="${RUNNER_LABELS}" \
    -e RUNNER_WORKDIR="/_work" \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v "$(pwd)/runner-work:/actions-runner/_work" \
    "${IMAGE_NAME}"

log_success "Container started: ${CONTAINER_NAME}"

# Wait for container to initialize
log_info "Waiting for container to initialize..."
sleep 5

# Check container status
if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "^${CONTAINER_NAME}"; then
    log_error "Container is not running!"
    log_info "Container logs:"
    docker logs "${CONTAINER_NAME}"
    exit 1
fi

log_success "Container is running"

# Monitor logs for 30 seconds
log_info "Monitoring container logs for 30 seconds..."
echo "--- Container Logs ---"
timeout 30s docker logs -f "${CONTAINER_NAME}" || true
echo "--- End of Logs ---"

# Check final status
log_info "Checking final container status..."
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "^${CONTAINER_NAME}"; then
    log_success "Container is still running"
    
    # Check if runner is registered
    log_info "Checking if runner is registered..."
    if docker logs "${CONTAINER_NAME}" 2>&1 | grep -q "Runner successfully added"; then
        log_success "Runner successfully registered with GitHub!"
    elif docker logs "${CONTAINER_NAME}" 2>&1 | grep -q "Listening for Jobs"; then
        log_success "Runner is listening for jobs!"
    else
        log_warning "Runner registration status unclear. Check logs above."
    fi
    
    # Test Docker functionality
    log_info "Testing Docker functionality inside container..."
    if docker exec "${CONTAINER_NAME}" docker --version >/dev/null 2>&1; then
        log_success "Docker CLI is working inside container"
        docker exec "${CONTAINER_NAME}" docker --version
    else
        log_error "Docker CLI is not working inside container"
    fi
    
    # Test other tools
    log_info "Testing other tools..."
    docker exec "${CONTAINER_NAME}" kubectl version --client --short 2>/dev/null || log_warning "kubectl test failed"
    docker exec "${CONTAINER_NAME}" helm version --short 2>/dev/null || log_warning "helm test failed"
    docker exec "${CONTAINER_NAME}" gh --version 2>/dev/null || log_warning "gh CLI test failed"
    
else
    log_error "Container has stopped!"
    log_info "Final container logs:"
    docker logs "${CONTAINER_NAME}"
    exit 1
fi

echo
log_info "=== Test Summary ==="
log_info "Container Name: ${CONTAINER_NAME}"
log_info "Runner Name: ${RUNNER_NAME}"
log_info "To view live logs: docker logs -f ${CONTAINER_NAME}"
log_info "To stop container: docker stop ${CONTAINER_NAME}"
log_info "To remove container: docker rm ${CONTAINER_NAME}"
echo
log_success "Test completed! Check GitHub repository settings to see if runner is registered."
