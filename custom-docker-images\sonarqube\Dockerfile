# SonarQube với Community Branch Plugin
# Dựa trên hướng dẫn từ https://github.com/mc1arke/sonarqube-community-branch-plugin

ARG SONARQUBE_VERSION=10.7
ARG PLUGIN_VERSION=1.22.0

FROM sonarqube:${SONARQUBE_VERSION}-community

# Metadata
LABEL maintainer="OSP Group <<EMAIL>>"
LABEL description="SonarQube Community Edition với Community Branch Plugin"
LABEL version="${PLUGIN_VERSION}"

# <PERSON><PERSON><PERSON><PERSON> sang user root để cài đặt plugin
USER root

# Cài đặt unzip
RUN apt-get update && apt-get install -y unzip && rm -rf /var/lib/apt/lists/*

# Tạo thư mục tạm thời
RUN mkdir -p /tmp/plugin-install

# Download plugin JAR
ARG PLUGIN_VERSION
RUN curl -L -o /tmp/plugin-install/sonarqube-community-branch-plugin-${PLUGIN_VERSION}.jar \
    "https://github.com/mc1arke/sonarqube-community-branch-plugin/releases/download/${PLUGIN_VERSION}/sonarqube-community-branch-plugin-${PLUGIN_VERSION}.jar"

# Copy plugin JAR vào thư mục extensions/plugins
RUN cp /tmp/plugin-install/sonarqube-community-branch-plugin-${PLUGIN_VERSION}.jar \
    /opt/sonarqube/extensions/plugins/

# Cấu hình javaagent cho web và compute engine
ENV SONAR_WEB_JAVAADDITIONALOPTS="-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-${PLUGIN_VERSION}.jar=web"
ENV SONAR_CE_JAVAADDITIONALOPTS="-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-${PLUGIN_VERSION}.jar=ce"

# Đặt quyền sở hữu cho sonarqube user
RUN chown -R sonarqube:root /opt/sonarqube/extensions/plugins/

# Dọn dẹp
RUN rm -rf /tmp/plugin-install

# Chuyển về user sonarqube
USER sonarqube

# Expose port mặc định
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5m --retries=3 \
    CMD curl -f http://localhost:9000/api/system/status | grep -q '"status":"UP"' || exit 1
