# SonarQube với Community Branch Plugin

Docker image tùy chỉnh của SonarQube Community Edition với [Community Branch Plugin](https://github.com/mc1arke/sonarqube-community-branch-plugin) để hỗ trợ phân tích branch và pull request decoration.

## Thông tin phiên bản

- **SonarQube**: 10.6 Community Edition
- **Community Branch Plugin**: 1.21.0
- **Base Image**: `sonarqube:10.6-community`

## Tính năng

✅ Phân tích branch  
✅ Pull Request decoration  
✅ Hỗ trợ GitHub, GitLab, Bitbucket  
✅ Tương thích với SonarQube Community Edition  

## GitHub Actions CI/CD

Image này được tự động build và push lên Harbor Registry khi có thay đổi trong thư mục `custom-docker-images/sonarqube/`.

### Workflow Jobs:
1. **🐳 Setup Docker Environment**: Kiểm tra và cài đặt Docker nếu cần
2. **🔧 Build and Push SonarQube Image**: Build multi-platform, test và push image

### Workflow triggers:
- **Push** to `main` hoặc `develop` branch
- **Pull Request** targeting `main` hoặc `develop` branch
- **Manual trigger** từ GitHub Actions UI với các tùy chọn:
  - SonarQube version (default: 10.6)
  - Community Branch Plugin version (default: 1.21.0)
  - Force rebuild without cache (default: false)

### Image registry:
- **Registry**: `dockerhub.ospgroup.vn` (Harbor)
- **Image name**: `dockerhub.ospgroup.vn/[owner]/sonarqube-community-branch`
- **Platforms**: `linux/amd64`, `linux/arm64` (multi-architecture)
- **Authentication**: Requires `OSP_REGISTRY_USERNAME` and `OSP_REGISTRY_PASSWORD` secrets
- **Tags**:
  - `latest` (for main branch)
  - `[branch-name]` (for other branches)
  - `[branch-name]-[sha]` (specific commit)

### Docker Buildx Features:
- **Multi-platform builds**: Tự động build cho AMD64 và ARM64
- **Buildx builder**: Tạo dedicated builder instance cho container builds
- **Local testing**: Build single platform (AMD64) cho test trước khi push
- **Multi-platform push**: Build và push cả hai platform cùng lúc
- **Build cache**: Sử dụng buildx cache để tối ưu build time

### Docker Environment Setup:
- Tự động kiểm tra Docker trên self-hosted runner
- Cài đặt Docker nếu chưa có (Ubuntu/Debian)
- Start Docker service nếu chưa chạy
- Setup Docker Buildx builder
- Verify installation với hello-world test

### Security scanning:
- Trivy vulnerability scanner tự động chạy sau khi build
- Security report hiển thị trong workflow logs

### Repository secrets cần thiết:
- `OSP_REGISTRY_USERNAME`: Username cho Harbor registry
- `OSP_REGISTRY_PASSWORD`: Password cho Harbor registry
- `OSP_REGISTRY`: Registry URL (dockerhub.ospgroup.vn)
- `OSP_IMAGE_OWNER`: Image owner/namespace

### Manual build từ GitHub UI:
1. Vào repository trên GitHub
2. Chọn tab "Actions"
3. Chọn workflow "Build and Push SonarQube Docker Image"
4. Click "Run workflow"
5. Điền các tham số tùy chọn (hoặc để mặc định)
6. Click "Run workflow" để bắt đầu build

## Build và Test (Manual)

### 1. Build image

```bash
cd custom-docker-images/sonarqube
chmod +x build.sh
./build.sh
```

### 2. Build và push lên registry

```bash
./build.sh --push
```

### 3. Test với Docker Compose

```bash
docker-compose up -d
```

Truy cập: http://localhost:9000  
Credentials mặc định: `admin/admin`

## Sử dụng

### Docker Run

```bash
docker run -d \
  --name sonarqube \
  -p 9000:9000 \
  -e SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true \
  dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sonarqube
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sonarqube
  template:
    metadata:
      labels:
        app: sonarqube
    spec:
      containers:
      - name: sonarqube
        image: dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
        ports:
        - containerPort: 9000
        env:
        - name: SONAR_ES_BOOTSTRAP_CHECKS_DISABLE
          value: "true"
```

## Cấu hình Branch Analysis

### Phân tích Branch

```bash
sonar-scanner \
  -Dsonar.projectKey=my-project \
  -Dsonar.sources=. \
  -Dsonar.host.url=http://localhost:9000 \
  -Dsonar.login=your-token \
  -Dsonar.branch.name=feature/my-feature
```

### Phân tích Pull Request

```bash
sonar-scanner \
  -Dsonar.projectKey=my-project \
  -Dsonar.sources=. \
  -Dsonar.host.url=http://localhost:9000 \
  -Dsonar.login=your-token \
  -Dsonar.pullrequest.key=123 \
  -Dsonar.pullrequest.branch=feature/my-feature \
  -Dsonar.pullrequest.base=main
```

## Cấu hình Database

Để sử dụng trong production, cấu hình database external:

```bash
docker run -d \
  --name sonarqube \
  -p 9000:9000 \
  -e SONAR_JDBC_URL=******************************* \
  -e SONAR_JDBC_USERNAME=sonar \
  -e SONAR_JDBC_PASSWORD=sonar \
  dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
```

## Troubleshooting

### Kiểm tra plugin đã load

```bash
curl http://localhost:9000/api/plugins/installed | jq '.plugins[] | select(.key=="community-branch")'
```

### Xem logs

```bash
docker logs sonarqube
```

### Health check

```bash
curl http://localhost:9000/api/system/status
```

## Lưu ý quan trọng

⚠️ **Plugin không được hỗ trợ chính thức bởi SonarSource**  
⚠️ **Không có upgrade path chính thức lên Commercial Edition**  
⚠️ **Chỉ sử dụng cho Community Edition**  

## Tài liệu tham khảo

- [SonarQube Community Branch Plugin](https://github.com/mc1arke/sonarqube-community-branch-plugin)
- [SonarQube Documentation](https://docs.sonarqube.org/)
- [Branch Analysis Documentation](https://docs.sonarqube.org/latest/branches/overview/)
