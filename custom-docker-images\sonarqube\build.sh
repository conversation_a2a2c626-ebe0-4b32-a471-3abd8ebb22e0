#!/bin/bash

# Script build SonarQube với Community Branch Plugin
# Sử dụng: ./build.sh [--push]

set -euo pipefail

# C<PERSON>u hình
SONARQUBE_VERSION="10.7"
PLUGIN_VERSION="1.22.0"
REGISTRY="dockerhub.ospgroup.vn"
IMAGE_NAME="osp-public/sonarqube-community-branch"
TAG="${PLUGIN_VERSION}"
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "🔨 Building SonarQube với Community Branch Plugin..."
echo "📦 Image: ${FULL_IMAGE_NAME}"
echo "🏷️  SonarQube Version: ${SONARQUBE_VERSION}"
echo "🔌 Plugin Version: ${PLUGIN_VERSION}"
echo ""

# Build image
echo "🔨 Building Docker image for amd64 platform..."
docker buildx build \
    --platform linux/amd64 \
    --build-arg SONARQUBE_VERSION="${SONARQUBE_VERSION}" \
    --build-arg PLUGIN_VERSION="${PLUGIN_VERSION}" \
    -t "${FULL_IMAGE_NAME}" \
    -t "${REGISTRY}/${IMAGE_NAME}:latest" \
    --load \
    .

echo "✅ Build completed successfully!"
echo ""

# Kiểm tra image đã được tạo
echo "📋 Image information:"
docker images "${REGISTRY}/${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
echo ""

# Test chạy container local
echo "🧪 Testing container locally..."
CONTAINER_NAME="sonarqube-test-$$"

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up test container..."
    docker rm -f "${CONTAINER_NAME}" >/dev/null 2>&1 || true
}
trap cleanup EXIT

# Chạy container test
docker run -d \
    --name "${CONTAINER_NAME}" \
    -p 9000:9000 \
    -e SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true \
    "${FULL_IMAGE_NAME}"

echo "⏳ Waiting for SonarQube to start..."
sleep 30

# Kiểm tra health
MAX_ATTEMPTS=30
ATTEMPT=1
while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if curl -s http://localhost:9000/api/system/status | grep -q '"status":"UP"'; then
        echo "✅ SonarQube is running successfully!"
        break
    fi
    
    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
        echo "❌ SonarQube failed to start within timeout"
        echo "📋 Container logs:"
        docker logs "${CONTAINER_NAME}" --tail 50
        exit 1
    fi
    
    echo "⏳ Attempt ${ATTEMPT}/${MAX_ATTEMPTS} - waiting for SonarQube..."
    sleep 10
    ((ATTEMPT++))
done

# Kiểm tra plugin đã được load
echo "🔌 Checking if Community Branch Plugin is loaded..."
if curl -s http://localhost:9000/api/plugins/installed | grep -q "community-branch"; then
    echo "✅ Community Branch Plugin is loaded successfully!"
else
    echo "⚠️  Community Branch Plugin status unclear - check manually"
fi

echo ""
echo "🎉 Local test completed successfully!"
echo "🌐 SonarQube is accessible at: http://localhost:9000"
echo "👤 Default credentials: admin/admin"
echo ""

# Push nếu được yêu cầu
if [[ "${1:-}" == "--push" ]]; then
    echo "🚀 Pushing image to registry..."
    docker push "${FULL_IMAGE_NAME}"
    docker push "${REGISTRY}/${IMAGE_NAME}:latest"
    echo "✅ Push completed successfully!"
    echo ""
    echo "📦 Image pushed: ${FULL_IMAGE_NAME}"
    echo "📦 Image pushed: ${REGISTRY}/${IMAGE_NAME}:latest"
else
    echo "💡 To push the image to registry, run: ./build.sh --push"
fi

echo ""
echo "🎯 Build Summary:"
echo "   Image: ${FULL_IMAGE_NAME}"
echo "   SonarQube: ${SONARQUBE_VERSION}"
echo "   Plugin: ${PLUGIN_VERSION}"
echo "   Status: ✅ Ready to use"
a