version: '3.8'

services:
  sonarqube:
    build:
      context: .
      args:
        SONARQUBE_VERSION: "10.6"
        PLUGIN_VERSION: "1.21.0"
    image: dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
    container_name: sonarqube-community-branch
    ports:
      - "9000:9000"
    environment:
      # Disable Elasticsearch bootstrap checks for development
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
      # Database configuration (sử dụng H2 embedded cho test)
      - SONAR_JDBC_URL=jdbc:h2:mem:sonar
      - SONAR_JDBC_USERNAME=sonar
      - SONAR_JDBC_PASSWORD=sonar
      # JVM options
      - SONAR_WEB_JAVAADDITIONALOPTS=-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-1.21.0.jar=web
      - SONAR_CE_JAVAADDITIONALOPTS=-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-1.21.0.jar=ce
    volumes:
      # Persist data
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/api/system/status"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 5m
    restart: unless-stopped

volumes:
  sonarqube_data:
    driver: local
  sonarqube_logs:
    driver: local
  sonarqube_extensions:
    driver: local
