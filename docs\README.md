# Documentation - Kubernetes Deployment Project

## Tổng quan

Đây là tài liệu chính cho dự án Kubernetes Deployment của OSP Group. Tài liệu được tổ chức theo cấu trúc logic để dễ dàng truy cập và tìm kiếm thông tin.

## Cấu trúc thư mục

```
docs/
├── README.md                           # File này - Tổng quan và chỉ dẫn
├── getting-started/                    # Hướng dẫn bắt đầu
│   ├── OVERVIEW.md                     # Tổng quan về dự án
│   └── QUICK_START.md                  # Hướng dẫn nhanh
├── architecture/                       # Tài liệu kiến trúc
│   ├── SYSTEM_ARCHITECTURE.md          # Kiến trúc hệ thống
│   ├── BOOTSTRAP_SERVICES.md           # Dịch vụ bootstrap
│   └── PLATFORM_SERVICES.md            # Dịch vụ nền tảng
├── guides/                             # Hướng dẫn chi tiết
│   ├── DEPLOYMENT_GUIDES.md            # Hướng dẫn triển khai
│   ├── GITHUB_RUNNERS.md               # GitHub Runners
│   ├── GATEWAY_API.md                  # Gateway API
│   └── REUSABLE_WORKFLOWS.md           # Reusable workflows
├── standards/                          # Tiêu chuẩn và quy tắc
│   ├── CODING_GUIDELINES.md            # Quy tắc coding
│   ├── CODING_TEMPLATES.md             # Templates và checklist
│   └── NAMING_CONVENTIONS.md           # Quy tắc đặt tên
├── cicd/                               # CI/CD
│   ├── PIPELINE_GUIDE.md               # Hướng dẫn pipeline
│   ├── SECRETS_MANAGEMENT.md           # Quản lý secrets
│   └── TROUBLESHOOTING.md              # Khắc phục sự cố
├── docker/                             # Docker
│   ├── BUILD_GUIDE.md                  # Hướng dẫn build
│   ├── CUSTOM_IMAGES.md                # Custom images
│   └── BEST_PRACTICES.md               # Best practices
├── services/                           # Tài liệu về dịch vụ cụ thể
│   ├── harbor/                         # Harbor registry
│   ├── postgresql/                     # PostgreSQL
│   ├── sonarqube/                      # SonarQube
│   └── label-studio/                   # Label Studio
├── reports/                            # Báo cáo và audit
│   ├── BOOTSTRAP_REPORT.md             # Báo cáo bootstrap
│   ├── PLATFORM_AUDIT.md               # Audit platform services
│   └── MIGRATION_REPORTS.md            # Báo cáo migration
└── diagrams/                           # Sơ đồ
    └── system_architecture.drawio.svg  # Sơ đồ kiến trúc hệ thống
```

## Cách sử dụng tài liệu

### 1. Người mới bắt đầu
- Bắt đầu với [`getting-started/OVERVIEW.md`](getting-started/OVERVIEW.md) để hiểu tổng quan
- Đọc [`getting-started/QUICK_START.md`](getting-started/QUICK_START.md) để thiết lập nhanh

### 2. Nhà phát triển
- Xem [`standards/CODING_GUIDELINES.md`](standards/CODING_GUIDELINES.md) để hiểu quy tắc coding
- Sử dụng [`standards/CODING_TEMPLATES.md`](standards/CODING_TEMPLATES.md) cho templates

### 3. DevOps Engineer
- Đọc [`architecture/SYSTEM_ARCHITECTURE.md`](architecture/SYSTEM_ARCHITECTURE.md) để hiểu kiến trúc
- Xem [`guides/DEPLOYMENT_GUIDES.md`](guides/DEPLOYMENT_GUIDES.md) cho hướng dẫn triển khai
- Sử dụng [`cicd/PIPELINE_GUIDE.md`](cicd/PIPELINE_GUIDE.md) cho CI/CD

### 4. System Administrator
- Xem [`services/`](services/) để hiểu các dịch vụ cụ thể
- Đọc [`reports/`](reports/) cho các báo cáo và audit

## Nguyên tắc GitOps

Dự án này tuân thủ nghiêm ngặt nguyên tắc GitOps:
- Git là nguồn chân lý duy nhất
- Mọi thay đổi phải được thực hiện qua Git và ArgoCD
- Không sử dụng `kubectl apply` trực tiếp (trừ bootstrap)

## Các thành phần chính

### Bootstrap Services
Các dịch vụ nền tảng cần thiết cho Kubernetes cluster:
- **HashiCorp Vault**: Quản lý secrets
- **Traefik Gateway**: Kubernetes Gateway API và Load Balancer
- **ArgoCD**: GitOps Continuous Deployment

### Platform Services
Các dịch vụ nền tảng được chia sẻ giữa các ứng dụng:
- **PostgreSQL**: Database
- **Redis**: Cache
- **MinIO**: Object Storage
- **Harbor**: Container Registry
- **SigNoz**: Observability Platform
- **Keycloak**: Authentication

### Applications
Các ứng dụng business sử dụng external database/redis từ platform services.

## Liên hệ

- **Maintainer**: OSP Group
- **Email**: <EMAIL>
- **Repository**: https://github.com/ospgroupvn/k8s-deployment

## Đóng góp

Khi đóng góp tài liệu mới:
1. Đặt file đúng thư mục theo cấu trúc
2. Sử dụng tiếng Việt (trừ thuật ngữ chuyên ngành)
3. Đảm bảo nội dung không trùng lặp với file hiện có
4. Cập nhật file README.md nếu cần