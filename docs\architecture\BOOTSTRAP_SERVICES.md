# Bootstrap Services

## Tổng quan

Bootstrap services là các dịch vụ nền tảng cần thiết cho Kubernetes cluster, được triển khai trong namespace `bootstrap`.

## Các dịch vụ chính

1. **HashiCorp Vault** - Quản lý secrets và bảo mật
2. **Traefik Gateway** - Kubernetes Gateway API và Load Balancer
3. **ArgoCD** - GitOps Continuous Deployment

## Cấu trúc thư mục

```
bootstrap/
├── README.md                 # File này
├── common/                   # Cấu hình chung
│   ├── namespace.yaml       # Định nghĩa namespace bootstrap
│   └── install.sh           # Script cài đặt tất cả dịch vụ
├── vault/                   # HashiCorp Vault
│   ├── Chart.yaml          # Helm chart metadata
│   ├── values.yaml         # Cấu hình Vault
│   └── charts/             # Helm chart dependencies
├── traefik/                # Traefik Gateway
│   ├── Chart.yaml          # Helm chart metadata
│   ├── values.yaml         # Cấu hình Traefik
│   └── charts/             # Helm chart dependencies
└── argocd/                 # ArgoCD
    ├── Chart.yaml          # Helm chart metadata
    ├── values.yaml         # Cấu hình ArgoCD
    └── charts/             # Helm chart dependencies
```

## Thứ tự cài đặt

1. **Vault** - Quản lý secrets
2. **Traefik** - Gateway và Load Balancer
3. **ArgoCD** - GitOps continuous deployment

## Chi tiết dịch vụ

### 1. HashiCorp Vault

**Trạng thái**: ✅ Đã cài đặt và chạy
**Namespace**: `bootstrap`
**Release Name**: `vault-bootstrap`

#### Thông tin truy cập:
- **Service**: `vault-bootstrap:8200`
- **Port Forward**: `kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200`
- **URL**: http://localhost:8200
- **Root Token**: `bootstrap-root-token`
- **Mode**: Development (in-memory storage)

#### Cấu hình quan trọng:
- Dev mode được bật (chỉ dành cho môi trường phát triển)
- UI được kích hoạt
- TLS bị vô hiệu hóa
- Storage: In-memory (dữ liệu sẽ mất khi restart)

#### Lệnh hữu ích:
```bash
# Kiểm tra trạng thái Vault
kubectl exec -n bootstrap vault-bootstrap-0 -- vault status

# Truy cập Vault CLI
kubectl exec -it -n bootstrap vault-bootstrap-0 -- vault auth -method=token token=bootstrap-root-token
```

### 2. Traefik Gateway

**Trạng thái**: ✅ Đã cài đặt và chạy ổn định
**Namespace**: `bootstrap`
**Release Name**: `traefik-bootstrap`

#### Thông tin truy cập LAN:
- **LoadBalancer Service**: `traefik-bootstrap` (IP: ************* - MetalLB)
- **HTTP Port**: 80
- **HTTPS Port**: 443
- **Gateway IP**: ************* (được cấp phát bởi MetalLB)

#### Truy cập qua LoadBalancer:
```bash
# Vault UI
curl -H "Host: vault.local" http://*************

# ArgoCD UI
curl -H "Host: argocd.local" http://*************
```

#### Cấu hình hosts file:
```
************* vault.local
************* argocd.local
************* traefik.local
```

#### Cấu hình quan trọng:
- ✅ Kubernetes Gateway API được kích hoạt
- ✅ Gateway Class: traefik (Accepted & Programmed)
- ✅ HTTPRoute: 5 routes attached (vault.local, argocd.local, harbor.local, minio.local, signoz.local)
- ✅ 2 replicas cho high availability
- ✅ LoadBalancer IP: ************* (MetalLB assigned)
- ✅ Cross-namespace HTTPRoute support enabled

#### Lệnh hữu ích:
```bash
# Kiểm tra Gateway và HTTPRoute
kubectl get gateway -n bootstrap
kubectl get httproute -n bootstrap

# Kiểm tra Traefik pods
kubectl get pods -n bootstrap -l app.kubernetes.io/name=traefik

# Test truy cập qua LoadBalancer
curl -H "Host: vault.local" http://*************
curl -H "Host: argocd.local" http://*************
```

### 3. ArgoCD

**Trạng thái**: ✅ Đã cài đặt và chạy
**Namespace**: `bootstrap`
**Release Name**: `argocd-bootstrap`

#### Thông tin truy cập:
- **Service**: `argocd-bootstrap-server:80`
- **Port Forward**: `kubectl port-forward svc/argocd-bootstrap-server -n bootstrap 8080:80`
- **URL**: http://localhost:8080
- **Username**: `admin`
- **Password**: `admin123`

#### Cấu hình quan trọng:
- Insecure mode được bật (HTTP thay vì HTTPS)
- Admin user được kích hoạt
- ApplicationSet controller được kích hoạt
- Redis internal được sử dụng
- RBAC được kích hoạt

#### Lệnh hữu ích:
```bash
# Kiểm tra ArgoCD pods
kubectl get pods -n bootstrap -l app.kubernetes.io/part-of=argocd

# Truy cập ArgoCD CLI
kubectl exec -it -n bootstrap deployment/argocd-bootstrap-server -- argocd login localhost:8080 --username admin --password admin123 --insecure

# Kiểm tra applications
kubectl get applications -A
```

## Gateway API Configuration

### Gateway Definition

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: traefik-gateway
  namespace: bootstrap
  labels:
    app: traefik
    component: gateway
spec:
  gatewayClassName: traefik
  listeners:
  - name: web
    port: 8000
    protocol: HTTP
    allowedRoutes:
      namespaces:
        from: All
  - name: websecure
    port: 8443
    protocol: HTTPS
    allowedRoutes:
      namespaces:
        from: All
```

### HTTPRoute Examples

#### ArgoCD HTTPRoute
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-server
  namespace: bootstrap
  labels:
    app: argocd
    component: server
spec:
  hostnames:
    - argocd.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: argocd-bootstrap-server
          port: 80
```

#### Vault HTTPRoute
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: vault-server
  namespace: bootstrap
  labels:
    app: vault
    component: server
spec:
  hostnames:
    - vault.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: vault-bootstrap-ui
          port: 8200
```

## Hướng dẫn truy cập từ LAN

### Cách 1: Sử dụng Traefik Gateway LoadBalancer (Khuyến nghị)

**Bước 1**: Cấu hình file hosts trên máy client
```bash
# Windows: C:\Windows\System32\drivers\etc\hosts
# Linux/Mac: /etc/hosts
************* vault.local
************* argocd.local
************* traefik.local
************* harbor.local
************* minio.local
************* signoz.local
```

**Bước 2**: Truy cập các dịch vụ
- **Vault UI**: http://vault.local
- **ArgoCD UI**: http://argocd.local
- **Traefik Dashboard**: https://traefik.local (HTTPS only)

### Cách 2: Port Forwarding (cho testing local)

```bash
# Port forward Vault
kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200

# Port forward ArgoCD  
kubectl port-forward svc/argocd-bootstrap-server -n bootstrap 8080:80
```

## Cài đặt

### Cài đặt tất cả dịch vụ
```bash
cd bootstrap
./common/install.sh
```

### Cài đặt từng dịch vụ
```bash
helm install vault ./vault -n bootstrap
helm install traefik ./traefik -n bootstrap
helm install argocd ./argocd -n bootstrap
```

## Trạng thái tổng thể

```bash
# Kiểm tra tất cả pods trong namespace bootstrap
kubectl get pods -n bootstrap

# Kiểm tra Gateway API resources
kubectl get gateway,httproute -n bootstrap

# Kiểm tra tất cả services
kubectl get svc -n bootstrap

# Kiểm tra Helm releases
helm list -n bootstrap
```

## Troubleshooting

### Vault Issues

**Vấn đề**: Pod không khởi động
```bash
# Kiểm tra logs
kubectl logs -n bootstrap vault-bootstrap-0

# Kiểm tra events
kubectl describe pod -n bootstrap vault-bootstrap-0
```

**Vấn đề**: Không thể truy cập UI
```bash
# Kiểm tra service
kubectl get svc -n bootstrap vault-bootstrap

# Port forward
kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200
```

### Traefik Issues

**Vấn đề**: LoadBalancer pending
- Cần cài đặt MetalLB hoặc cloud load balancer
- Tạm thời có thể sử dụng NodePort hoặc port-forward

**Vấn đề**: Gateway không hoạt động
```bash
# Kiểm tra Gateway Class
kubectl get gatewayclass

# Kiểm tra CRDs
kubectl get crd | grep gateway
```

### ArgoCD Issues

**Vấn đề**: Không thể đăng nhập
- Username: `admin`
- Password: `admin123`
- Đảm bảo sử dụng HTTP (insecure mode)

**Vấn đề**: Applications không sync
```bash
# Kiểm tra ArgoCD server logs
kubectl logs -n bootstrap deployment/argocd-bootstrap-server

# Kiểm tra application controller logs
kubectl logs -n bootstrap statefulset/argocd-bootstrap-application-controller
```

## Test kết nối đã thực hiện

**Ngày test**: 2025-09-03 22:00 UTC+7

```bash
# Test Vault status
kubectl exec -n bootstrap vault-bootstrap-0 -- vault status
# ✅ Result: Initialized=true, Sealed=false

# Test ArgoCD HTTP response
curl -H "Host: argocd.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 200

# Test Vault HTTP response
curl -H "Host: vault.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 307 (redirect, normal behavior)

# Test Gateway API
kubectl get gateway traefik-gateway -n bootstrap
# ✅ Result: Accepted=True, Programmed=True, AttachedRoutes=5

# Test HTTPRoute status
kubectl get httproutes -A
# ✅ Result: 6 HTTPRoutes total (2 in bootstrap, 3 in platform-services, 1 in default)
```

## Bảo mật

⚠️ **Cảnh báo**: Cấu hình hiện tại chỉ phù hợp cho môi trường phát triển/testing:

- Vault chạy ở dev mode với root token cố định
- ArgoCD chạy ở insecure mode
- Mật khẩu admin được hardcode
- Không có TLS encryption

Đối với môi trường production, cần:
- Cấu hình Vault với proper storage backend
- Kích hoạt TLS cho tất cả services
- Sử dụng proper authentication và authorization
- Rotate passwords và tokens thường xuyên

## Bước tiếp theo

1. **Cấu hình HTTPS listener** với TLS certificates cho Gateway
2. **Thiết lập proper authentication** cho các platform services
3. **Cấu hình Vault policies** và authentication methods
4. **Tạo HTTPRoute** cho các ứng dụng mới
5. **Setup monitoring và logging pipeline** với SigNoz