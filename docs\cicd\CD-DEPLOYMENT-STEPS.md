# CD Pipeline Deployment Steps

## ✅ **Changes Committed:**

### 1. **k8s-deployment repository** (✅ Already committed to main):
- Fixed `reuseable-dotnet-nuget-cd.yml` with proper tag validation
- Added force_build parameter
- Enhanced debug output

### 2. **osp-common-be-dotnet repository** (✅ Committed to feature/common-features):
- Fixed `cd-release.yml` to only trigger on `[published]`
- Added `force_build: true`
- Updated concurrency control

## 🔄 **Required Actions to Fix the Issue:**

### **Option 1: Merge to develop branch (Recommended)**
```bash
# In osp-common-be-dotnet repository
git checkout develop
git merge feature/common-features
git push origin develop
```

### **Option 2: Create release from feature/common-features branch**
- When creating the release, set **Target branch** to `feature/common-features`
- This will use the fixed workflow files

### **Option 3: Cherry-pick the fix to develop**
```bash
git checkout develop
git cherry-pick 2ecf402  # The commit with CD fixes
git push origin develop
```

## 🧪 **Testing the Complete Fix:**

After merging/deploying the changes:

1. **Create a new release**:
   - Tag: `v1.0.9` (new version)
   - Target: `develop` branch (or whichever branch has the fixes)
   - Click "Publish release"

2. **Expected behavior**:
   - ✅ **Only 1 workflow** triggers (not 3)
   - ✅ **"Build và Test NuGet Package"** starts and runs
   - ✅ **NuGet package** gets created and published
   - ✅ **Single Lark notification**

## 🔍 **Why Previous Tests Failed:**

1. **Multiple triggers**: Old config had `[published, created]`
2. **Job skipped**: Reusable workflow fixes weren't deployed to `@main`
3. **Wrong branch**: Fixes were on `feature/common-features` but releases from different branch

## 📋 **Verification Checklist:**

Before testing:
- [ ] CD workflow changes merged to target branch
- [ ] Reusable workflow changes committed to k8s-deployment main ✅
- [ ] Both repositories have the latest fixes

After testing:
- [ ] Single workflow run visible in Actions
- [ ] Build job executes (not skipped)
- [ ] NuGet package published successfully
- [ ] Debug logs show correct values

## 🚨 **Critical Point:**

**The branch you create the release from MUST have the updated `cd-release.yml` file with:**
```yaml
on:
  release:
    types: [published]  # Only published, not created
```

**And:**
```yaml
force_build: true  # Always build for releases
```

---

**Once the changes are merged to the target branch, the CD pipeline will work correctly! 🎉**