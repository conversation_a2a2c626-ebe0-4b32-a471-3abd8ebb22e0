# Fix: Duplicate CD Pipeline Triggers

## 🔍 **Issue Identified**

The CD Release workflow was triggering **twice** for each GitHub release:
1. ❌ First trigger: `release: created` event
2. ❌ Second trigger: `release: published` event

This caused duplicate NuGet package builds and potentially conflicting publishes.

## ✅ **Solution Applied**

### **Before (Problematic)**:
```yaml
on:
  release:
    types: [published, created]  # ❌ Triggers TWICE
  create:
    tags:
      - 'v*'  # ❌ Additional potential conflict
```

### **After (Fixed)**:
```yaml
on:
  release:
    types: [published]  # ✅ Triggers ONCE only when released
  # Removed create trigger to avoid conflicts
```

### **Enhanced Concurrency Control**:
```yaml
concurrency:
  group: cd-release-${{ github.event_name }}-${{ github.event.release.tag_name || github.event.inputs.tag_name || github.ref }}
  cancel-in-progress: false
```

## 🧪 **Testing the Fix**

### **Test Case 1: Draft → Publish Release**
1. **Create Draft Release:**
   - Tag: `v1.0.6`
   - Target: `develop`
   - Set as "Draft" (not published)
   - **Expected**: ✅ **NO workflow triggered** (correct!)

2. **Publish the Release:**
   - Click "Publish release"
   - **Expected**: ✅ **ONE workflow triggered** (correct!)
   - Should see only: "Release v1.0.6 published by [user]"

### **Test Case 2: Direct Publish Release**
1. **Create and Publish in One Step:**
   - Tag: `v1.0.7`
   - Target: `develop`
   - Click "Publish release" (not "Save draft")
   - **Expected**: ✅ **ONE workflow triggered** (correct!)

### **Test Case 3: Manual Trigger**
1. **Workflow Dispatch:**
   - Actions → "CD Release - OSP Common Backend .NET"
   - Run workflow with custom tag
   - **Expected**: ✅ **ONE workflow runs** (unchanged)

## 📋 **Verification Checklist**

After the fix is deployed:

- [ ] ✅ Creating a draft release triggers **0 workflows**
- [ ] ✅ Publishing a release triggers **exactly 1 workflow**
- [ ] ✅ Workflow run shows: "Release v1.0.x published by [user]"
- [ ] ✅ No duplicate "created" and "published" workflows
- [ ] ✅ Manual dispatch still works normally
- [ ] ✅ NuGet packages are published only once per release
- [ ] ✅ Lark notifications sent only once per release

## 🔄 **Workflow Behavior Changes**

### **Previous Behavior (Problematic)**:
```
Create Release → Trigger #1 (created)
                ↓
Publish Release → Trigger #2 (published)
                 ↓
Result: 2 workflows, potential conflicts
```

### **New Behavior (Fixed)**:
```
Create Draft → No trigger ✅
     ↓
Publish Release → Trigger #1 (published) ✅
                 ↓
Result: 1 workflow, clean execution
```

## ⚠️ **Important Notes**

1. **Draft Releases**: Will no longer trigger CD pipeline (this is correct behavior)
2. **Only Published Releases**: Will trigger the CD pipeline
3. **Tag Creation**: Direct tag creation (via git) will NOT trigger CD (use manual dispatch if needed)
4. **Existing Releases**: If you re-publish an existing release, it will trigger the CD again

## 🚀 **Best Practices**

1. **Always use "Publish release"** (not "Save draft") when ready to deploy
2. **Create draft first** if you need to prepare release notes
3. **Publish when ready** to trigger CD pipeline
4. **Monitor the single workflow** to ensure successful NuGet publish
5. **Use manual dispatch** for custom scenarios or re-runs

## 🛠️ **Rollback Plan** (if needed)

If this change causes issues, you can temporarily rollback by reverting to:
```yaml
on:
  release:
    types: [published, created]
```

But the duplicate trigger issue will return.

## 📞 **Next Steps**

1. **Deploy this fix** by committing the workflow changes
2. **Test with a new release** (use a patch version like v0.0.6)
3. **Verify single trigger** in Actions tab
4. **Confirm NuGet publish** works correctly
5. **Update team documentation** about new release process

---

**This fix ensures clean, single-execution CD pipelines for NuGet package releases! 🎉**