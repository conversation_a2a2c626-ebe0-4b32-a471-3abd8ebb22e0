# CD Pipeline Test Checklist

## ✅ Kiểm tra trước khi test

### 1. **Secrets đã được cấu hình** (trong OSP Common BE repository)
- [ ] `OSP_NUGET_PACKAGE_PASSWORD` - Token NuGet registry
- [ ] `LARK_WEBHOOK_URL` - Webhook Lark (optional)

### 2. **Repository structure đúng format**
```
osp-common-be-dotnet/
├── src/
│   ├── *.sln (solution file)
│   ├── **/*.csproj (project files)
│   └── **/*.cs (source files)
├── .github/
│   └── workflows/
│       └── cd-release.yml
└── README.md
```

### 3. **Project files (.csproj) có cấu hình package**
```xml
<PropertyGroup>
  <IsPackable>true</IsPackable>
  <PackageId>YourPackage.Name</PackageId>
  <Description>Package description</Description>
  <Authors>OSP Group</Authors>
  <!-- <PERSON><PERSON><PERSON> thu<PERSON> t<PERSON>h package khác -->
</PropertyGroup>
```

## 🧪 Test Scenarios

### Scenario 1: GitHub Release (Khuyến nghị)
1. **Tạo release từ GitHub UI:**
   - Vào repository → Releases → Create a new release
   - Tag: `v1.0.0` (hoặc format tương tự)
   - Target: `develop` branch
   - Title: `Release v1.0.0`
   - Description: Release notes
   - ⚠️ **QUAN TRỌNG**: Click "Publish release" (không phải "Save draft")
   - Chỉ có "published" release mới trigger CD pipeline

2. **Kết quả mong đợi:**
   - Workflow "CD Release - OSP Common Backend .NET" được trigger
   - Job "Kiểm tra nhánh phát hành" pass với log: "✅ Release event from target: develop"
   - Job "Build và Publish NuGet Package" chạy và hoàn thành
   - Package được publish lên `https://package.ospgroup.io.vn/repository/nuget-hosted/index.json`
   - Notification gửi qua Lark (nếu có)

### Scenario 2: Manual Tag Creation
1. **Tạo tag thủ công:**
   ```bash
   git checkout develop
   git tag v1.0.1
   git push origin v1.0.1
   ```

2. **Kết quả mong đợi:**
   - Workflow được trigger với event `create`
   - Tag validation pass
   - Build và publish thành công

### Scenario 3: Manual Workflow Trigger
1. **Chạy workflow thủ công:**
   - Vào Actions tab → "CD Release - OSP Common Backend .NET"
   - Click "Run workflow"
   - Inputs:
     - Tag name: `v1.0.2`
     - Force publish: `false`
     - Target branch: `develop`

2. **Kết quả mong đợi:**
   - Workflow chạy với parameters đã nhập
   - Build và publish thành công

### Scenario 4: Invalid Tag Format
1. **Test với tag không hợp lệ:**
   - Tạo release với tag: `release-1.0` hoặc `latest`

2. **Kết quả mong đợi:**
   - Job "Kiểm tra nhánh phát hành" fail với message: "❌ Invalid tag format"
   - Job "Bỏ qua phát hành" chạy với thông tin debug

### Scenario 5: Wrong Branch
1. **Test với branch không đúng:**
   - Tạo release từ `main` branch thay vì `develop`

2. **Kết quả mong đợi:**
   - Deploy được skip với message branch không đúng
   - Job "Bỏ qua phát hành" cung cấp hướng dẫn

## 🔍 Debug Commands

### Kiểm tra workflow logs:
1. Vào Actions tab trong GitHub
2. Click vào workflow run
3. Click vào job bị lỗi
4. Xem detailed logs

### Kiểm tra package đã publish:
```bash
# Tìm kiếm package
dotnet package search "PackageName" --source https://package.ospgroup.io.vn/repository/nuget-hosted/index.json

# Thêm package vào project test
dotnet add package PackageName --version 1.0.0 --source https://package.ospgroup.io.vn/repository/nuget-hosted/index.json
```

### Kiểm tra NuGet source:
```bash
# List sources
dotnet nuget list source

# Test authentication
dotnet nuget push test.nupkg --source "https://package.ospgroup.io.vn/repository/nuget-hosted/index.json" --api-key "your-token" --dry-run
```

## 🐛 Common Issues & Solutions

### Issue 1: "should-deploy=false"
**Symptoms:** Workflow chạy nhưng skip deploy
**Solutions:**
- Kiểm tra tag format (phải là v1.0.0 hoặc 1.0.0)
- Verify target branch là develop
- Xem logs của job "Kiểm tra nhánh phát hành"

### Issue 2: Build failures
**Symptoms:** Job "Build và Test NuGet Package" fail
**Solutions:**
- Verify .NET version (9.0.100) compatibility
- Kiểm tra dependencies trong .csproj
- Test build locally: `dotnet build src/ --configuration Release`

### Issue 3: Package push failures
**Symptoms:** Publish job fail với authentication error
**Solutions:**
- Verify secret `OSP_NUGET_PACKAGE_PASSWORD` đã được set
- Test token permissions với NuGet registry
- Kiểm tra package size limits

### Issue 4: Test failures
**Symptoms:** Tests fail trong CD pipeline
**Solutions:**
- Run tests locally: `dotnet test src/ --configuration Release`
- Tạm thời set `enable-tests: false` trong workflow
- Fix test issues và re-run

### Issue 5: No .csproj found
**Symptoms:** "Không tìm thấy file .csproj"
**Solutions:**
- Verify `src-directory` parameter đúng
- Ensure .csproj files tồn tại trong thư mục
- Check project structure

## 📋 Success Criteria

✅ **Pipeline thành công khi:**
- [ ] Workflow trigger đúng từ GitHub release
- [ ] Tag validation pass
- [ ] Build không có errors/warnings
- [ ] Tests pass (nếu enable)
- [ ] NuGet packages được tạo (.nupkg + .snupkg)
- [ ] Packages publish thành công lên registry
- [ ] Lark notification gửi đúng status
- [ ] Artifacts được upload để debug

✅ **Package quality đảm bảo:**
- [ ] Package metadata đầy đủ (id, version, description, author)
- [ ] Dependencies được resolve đúng
- [ ] Symbols package (.snupkg) để debugging
- [ ] Package size hợp lý (< 50MB)
- [ ] Install và sử dụng được trong project khác

## 🚀 Production Checklist

Trước khi đưa vào production:

- [ ] Test tất cả scenarios trên
- [ ] Verify package registry permissions
- [ ] Confirm Lark webhook working
- [ ] Document package usage cho team
- [ ] Setup monitoring cho CD pipeline
- [ ] Create runbook cho troubleshooting
- [ ] Train team về quy trình release

## 📞 Support

Nếu có issues:
1. Check logs trong Actions tab
2. Verify secrets configuration
3. Test build/publish locally
4. Review this checklist
5. Contact DevOps team qua Lark với:
   - Workflow run URL
   - Error messages
   - Expected vs actual behavior