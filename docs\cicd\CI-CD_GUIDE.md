# Guide tổng hợp về CI/CD

Đây là tài liệu tổng hợp các hướng dẫn, quy trình và cách khắc phục lỗi liên quan đến CI/CD trong dự án.

---

## Danh sách secret variable đang có

| Tên biến                            | <PERSON><PERSON> tả                                                                               | Gi<PERSON> trị mặc định                                                    |
| ----------------------------------- | ----------------------------------------------------------------------------------- | ------------------------------------------------------------------- |
| LARK_WEBHOOK_URL                    | đường dẫn tới webhook của Lark (để gửi thông báo)                                   |                                                                     |
| OSP_IMAGE_OWNER                     | là harbor repository chứa các docker image. (để push image)                         | `osp-public`                                                        |
| OSP_REGISTRY                        | là docker registry chứa các docker image.                                           | `dockerhub.ospgroup.vn`                                             |
| OSP_REGISTRY_USERNAME               | username để đăng nhập vào docker registry                                           |                                                                     |
| OSP_REGISTRY_PASSWORD               | password để đăng nhập vào docker registry                                           |                                                                     |
| OSP_NUGET_PACKAGE_REGISTRY          | là nuget registry chứa các nuget package.                                           | `https://package.ospgroup.io.vn/repository/nuget-hosted/index.json` |
| OSP_NUGET_PACKAGE_REGISTRY_USERNAME | là username để đăng nhập vào nuget registry                                         | `osp-package`                                                       |
| OSP_NUGET_PACKAGE_PASSWORD          | là password / token / apikey để đăng nhập vào nuget registry (có quyền ghi package) |                                                                     |

---

## CI/CD Pipeline Permission Fix Summary

### Problem Analysis

The GitHub Actions CI pipeline was failing with permission denied errors when trying to access NuGet cache directories and packages.

### Root Cause

The GitHub Actions runner was running as the `actions-runner` user, but the NuGet directories didn't have proper permissions set up.

### Comprehensive Solution Implemented

1.  **Docker Image Fixes (`custom-docker-images/dotnet-9/Dockerfile`)**:
    *   Enhanced Directory Creation for all NuGet-related paths.
    *   Added NuGet-specific environment variables.
    *   Created a script for runtime permission fixes.
2.  **CI Workflow Fixes (`.github/workflows/reuseable-dotnet-lib-ci-build.yml`)**:
    *   Set global environment variables for NuGet paths.
    *   Added a pre-build step to fix permissions before any `dotnet` command.

---

## CI/CD Permission Fix - Deployment and Testing Plan

### Phase 1: Docker Image Deployment
- Build and verify the new Docker image with permission fixes.

### Phase 2: Runner Deployment Update
- Update the Helm deployment to use the new image.
- Verify the runner pods are updated and running correctly.

### Phase 3: CI Pipeline Testing
- Trigger a test build and monitor for permission errors.
- Expected success is the absence of "Permission denied" errors.

### Phase 4: Validation and Monitoring
- Test fresh builds, cached builds, and concurrent builds.
- Monitor for performance impacts.

### Phase 5: Rollback Plan
- Revert to the previous Docker image tag via Helm if issues occur.
- Revert workflow changes via git if necessary.

---

## CD Pipeline Fixes Summary

### Issues Identified & Fixed

1.  **Duplicate CD Pipeline Triggers**: Fixed by changing the trigger from `[published, created]` to only `[published]`.
2.  **Build Job Not Starting**: Fixed tag validation logic in the reusable workflow.
3.  **No Build on Release Without Changes**: Added `force_build: true` parameter for all release builds.

---

## Fix: Duplicate CD Pipeline Triggers

### Issue Identified
The CD workflow was triggering twice for each release, causing duplicate builds.

### Solution Applied
Changed the workflow trigger from:
```yaml
on:
  release:
    types: [published, created]
```
to:
```yaml
on:
  release:
    types: [published]
```
This ensures the pipeline runs only once when the release is officially published.

---

## CD Pipeline Deployment Steps

### Required Actions to Fix the Issue:
- Merge the `feature/common-features` branch (which contains the fixes) into the `develop` branch.
- The reusable workflow `reuseable-dotnet-nuget-cd.yml` has already been fixed in the `main` branch of the `k8s-deployment` repository.

### Testing the Fix:
1.  Create a new release from the `develop` branch.
2.  **Expected behavior**: Only one workflow should trigger, successfully build, and publish the NuGet package.

---

## CD Pipeline Test Checklist

### Pre-test Checklist
- [ ] Ensure `OSP_NUGET_PACKAGE_PASSWORD` secret is configured.
- [ ] Verify the repository has the correct structure and `.csproj` files are packable.

### Test Scenarios
1.  **GitHub Release (Recommended)**: Create a new release and publish it. Verify a single workflow runs.
2.  **Manual Tag Creation**: Push a new tag. Verify the workflow is triggered.
3.  **Manual Workflow Trigger**: Run the workflow from the Actions tab.
4.  **Invalid Tag/Branch**: Test with incorrect formats to ensure the pipeline fails gracefully.

### Common Issues & Solutions
*   **"should-deploy=false"**: Check tag format and target branch.
*   **Build failures**: Check .NET version and dependencies.
*   **Publish failures**: Check NuGet secrets and token permissions.
