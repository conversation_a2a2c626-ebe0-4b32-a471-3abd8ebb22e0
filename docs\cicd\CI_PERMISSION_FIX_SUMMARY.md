# CI/CD Pipeline Permission Fix Summary

## Problem Analysis

The GitHub Actions CI pipeline was failing with permission denied errors when trying to access NuGet cache directories and packages. The specific errors were:

1. **NuGet HTTP Cache Permission Issues**: 
   ```
   Access to the path '/home/<USER>/.local/share/NuGet/http-cache/...' is denied.
   Permission denied
   ```

2. **NuGet Packages Directory Permission Issues**:
   ```
   error : Access to the path '/home/<USER>/.nuget/packages' is denied.
   error :   Permission denied
   ```

## Root Cause

The GitHub Actions runner was running as the `actions-runner` user, but the NuGet directories didn't have proper permissions set up. The .NET 9 custom runner Docker image wasn't creating all necessary directories with correct ownership and permissions.

## Comprehensive Solution Implemented

### 1. Docker Image Fixes (`custom-docker-images/dotnet-9/Dockerfile`)

**Enhanced Directory Creation:**
- Added creation of all NuGet-related directories:
  - `~/.nuget/packages`
  - `~/.local/share/NuGet/http-cache`
  - `~/.local/share/NuGet/v3-cache`
  - `~/.local/share/NuGet/plugins-cache`
  - `~/.dotnet/NuGetFallbackFolder`

**Environment Variables:**
- Added NuGet-specific environment variables:
  ```dockerfile
  ENV NUGET_PACKAGES=/home/<USER>/.nuget/packages
  ENV NUGET_HTTP_CACHE_PATH=/home/<USER>/.local/share/NuGet/http-cache
  ENV NUGET_PLUGINS_CACHE_PATH=/home/<USER>/.local/share/NuGet/plugins-cache
  ```

**Permission Script:**
- Created `/fix-permissions.sh` script for runtime permission fixes
- Set proper ownership and permissions (755) for all directories

**Custom Entrypoint:**
- Created `entrypoint-dotnet.sh` with .NET-specific initialization
- Includes comprehensive permission fixing and environment setup
- Verifies .NET installation and creates global.json if needed

### 2. CI Workflow Fixes (`.github/workflows/reuseable-dotnet-lib-ci-build.yml`)

**Global Environment Variables:**
```yaml
env:
  NUGET_PACKAGES: ~/.nuget/packages
  NUGET_HTTP_CACHE_PATH: ~/.local/share/NuGet/http-cache
  NUGET_PLUGINS_CACHE_PATH: ~/.local/share/NuGet/plugins-cache
  DOTNET_CLI_TELEMETRY_OPTOUT: 1
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  DOTNET_NOLOGO: 1
```

**Pre-Build Permission Fix Step:**
- Clears existing problematic cache files
- Creates all necessary directories
- Sets proper permissions (755)
- Runs system-wide permission fix if available
- Sets environment variables for the workflow
- Verifies setup and shows diagnostic information

**Enhanced Command Execution:**
- Added environment variables to `dotnet restore`, `dotnet build`, and `dotnet test` commands
- Ensures consistent NuGet directory usage throughout the pipeline

### 3. Key Features of the Solution

**Comprehensive Directory Management:**
- Creates all NuGet-related directories at build time and runtime
- Sets proper ownership (`actions-runner:actions-runner`)
- Sets appropriate permissions (755)

**Multi-Layer Permission Fixes:**
1. **Docker Build Time**: Creates directories and sets initial permissions
2. **Container Startup**: Custom entrypoint fixes permissions
3. **CI Workflow**: Additional permission fixes before dotnet commands

**Environment Variable Consistency:**
- Ensures all .NET/NuGet tools use the same directory paths
- Prevents permission conflicts from mixed directory usage

**Diagnostic and Logging:**
- Comprehensive logging throughout the permission fixing process
- Verification steps to confirm successful setup
- Graceful handling of permission errors with fallbacks

## Files Modified

1. `custom-docker-images/dotnet-9/Dockerfile` - Enhanced with comprehensive permission setup
2. `custom-docker-images/dotnet-9/entrypoint-dotnet.sh` - New custom entrypoint script
3. `.github/workflows/reuseable-dotnet-lib-ci-build.yml` - Added permission fixes and environment variables

## Expected Results

After implementing these changes:

1. **No Permission Denied Errors**: All NuGet operations should work without permission issues
2. **Consistent Environment**: All .NET tools use the same, properly configured directories
3. **Robust CI Pipeline**: Multiple layers of permission fixes ensure reliability
4. **Better Diagnostics**: Clear logging helps identify any remaining issues

## Testing and Validation

The solution includes:
- Verification steps in both Docker image and CI workflow
- Diagnostic logging to confirm proper setup
- Graceful error handling with fallbacks
- Environment variable validation

## Deployment

To deploy these fixes:

1. **Build New Docker Image**: The .NET 9 custom runner image needs to be rebuilt with the new Dockerfile
2. **Update Runner Deployment**: Deploy the new image to the Kubernetes cluster
3. **Test CI Pipeline**: Run a test build to verify the fixes work correctly

The changes are backward compatible and include fallbacks for different environments.
