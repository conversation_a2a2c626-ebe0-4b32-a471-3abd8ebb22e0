# CI/CD Permission Fix - Deployment and Testing Plan

## Overview

This document outlines the step-by-step process to deploy and test the comprehensive permission fixes for the CI/CD pipeline.

## Phase 1: Docker Image Deployment

### 1.1 Automatic Build Trigger
The changes to `custom-docker-images/dotnet-9/` will automatically trigger the Docker image build workflow:
- **Workflow**: `.github/workflows/osp-custom-runner-dotnet9-build.yml`
- **Trigger**: Changes to `custom-docker-images/dotnet-9/**`
- **Output**: New `osp-custom-runner-dotnet-9` image with version tag

### 1.2 Manual Build (if needed)
If automatic build doesn't trigger, manually run:
```bash
# Navigate to the workflow in GitHub Actions
# Go to: https://github.com/ospgroupvn/k8s-deployment/actions/workflows/osp-custom-runner-dotnet9-build.yml
# Click "Run workflow" and select appropriate options
```

### 1.3 Verify Docker Image Build
Check that the new image includes our fixes:
- Verify `/fix-permissions.sh` exists
- Verify `/entrypoint-dotnet.sh` exists
- Verify environment variables are set correctly
- Check that all NuGet directories are created

## Phase 2: Runner Deployment Update

### 2.1 Update Runner Configuration
Update the runner deployment to use the new image:
```yaml
# In github-runners/values-osp-common-be-dotnet.yaml
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9
  tag: "1.0.1"  # Use the new version tag
  pullPolicy: Always
```

### 2.2 Deploy Updated Runners
```bash
# Update the Helm deployment
helm upgrade osp-common-be-dotnet-runner ./github-runners \
  -f github-runners/values-osp-common-be-dotnet.yaml \
  --namespace github-runners
```

### 2.3 Verify Runner Deployment
```bash
# Check pod status
kubectl get pods -n github-runners -l app.kubernetes.io/instance=osp-common-be-dotnet-runner

# Check logs for permission fixes
kubectl logs -n github-runners -l app.kubernetes.io/instance=osp-common-be-dotnet-runner --tail=100
```

## Phase 3: CI Pipeline Testing

### 3.1 Test Build Trigger
Trigger a test build on the `osp-common-be-dotnet` repository:

**Option A: Push a small change**
```bash
# Make a minor change to trigger CI
echo "# Permission fix test - $(date)" >> dotnet/osp-common-be-dotnet/README.md
git add dotnet/osp-common-be-dotnet/README.md
git commit -m "test: trigger CI to validate permission fixes"
git push origin main
```

**Option B: Manual workflow dispatch**
- Go to: https://github.com/ospgroupvn/osp-common-be-dotnet/actions/workflows/ci-build.yml
- Click "Run workflow"
- Set `force_build: true`

### 3.2 Monitor Test Results
Watch the CI pipeline execution:
1. **Check "Fix NuGet permissions" step**: Should show successful directory creation and permission setting
2. **Check "Restore dependencies" step**: Should complete without permission denied errors
3. **Check "Build all projects" step**: Should build successfully
4. **Check "Run tests" step**: Should run tests without issues

### 3.3 Expected Success Indicators
✅ **Permission Fix Step Output:**
```
🔧 Fixing NuGet permissions for CI environment...
Clearing existing NuGet caches...
✅ NuGet directories created and permissions set
Current user: actions-runner
Home directory: /home/<USER>
```

✅ **Restore Dependencies Output:**
```
Determining projects to restore...
Restored [project files] (in X.Xs).
```

✅ **No Permission Denied Errors:**
- No "Access to the path ... is denied" messages
- No "Permission denied" errors in any step

## Phase 4: Validation and Monitoring

### 4.1 Comprehensive Test Matrix
Test the following scenarios:
- [x] Fresh build (no cache)
- [x] Incremental build (with cache)
- [x] Test execution
- [x] Multiple concurrent builds
- [x] Different branch builds (main, develop, feature branches)

### 4.2 Performance Validation
Monitor for any performance impacts:
- Build time comparison (before vs after)
- Resource usage (CPU, memory)
- Cache effectiveness

### 4.3 Error Monitoring
Set up monitoring for:
- Permission-related errors
- NuGet restore failures
- Build failures
- Test execution issues

## Phase 5: Rollback Plan (if needed)

### 5.1 Quick Rollback
If issues occur, quickly rollback:
```bash
# Revert to previous image version
helm upgrade osp-common-be-dotnet-runner ./github-runners \
  -f github-runners/values-osp-common-be-dotnet.yaml \
  --set image.tag="1.0.0" \
  --namespace github-runners
```

### 5.2 Workflow Rollback
If workflow changes cause issues:
```bash
# Revert workflow changes
git revert [commit-hash-of-workflow-changes]
git push origin main
```

## Success Criteria

The deployment is considered successful when:

1. ✅ **Docker Image Built**: New image with permission fixes is available
2. ✅ **Runners Updated**: All runner pods are using the new image
3. ✅ **CI Pipeline Works**: Builds complete without permission errors
4. ✅ **Tests Pass**: All unit tests execute successfully
5. ✅ **No Regressions**: No new issues introduced
6. ✅ **Performance Maintained**: Build times remain acceptable

## Troubleshooting Guide

### Common Issues and Solutions

**Issue**: Docker image build fails
- **Solution**: Check Dockerfile syntax and base image availability

**Issue**: Runner pods fail to start
- **Solution**: Check pod logs and resource limits

**Issue**: Permission errors still occur
- **Solution**: Verify entrypoint script execution and directory creation

**Issue**: Build performance degraded
- **Solution**: Check cache configuration and directory permissions

## Contact and Support

For issues during deployment:
- Check GitHub Actions logs
- Review Kubernetes pod logs
- Consult the CI_PERMISSION_FIX_SUMMARY.md for technical details

## Next Steps After Successful Deployment

1. Monitor CI pipeline for 24-48 hours
2. Collect performance metrics
3. Document any additional optimizations needed
4. Consider applying similar fixes to other .NET projects
