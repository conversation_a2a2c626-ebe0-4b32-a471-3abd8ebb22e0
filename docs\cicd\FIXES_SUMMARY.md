# Tóm tắt các fix cho PR #22 - fe-osp-shared

## 1. Fix cho ci-build.yml (dòng 30)

**Vấn đề**: Expression `|| true` khiến skip_draft_pr luôn là true và bỏ qua input từ workflow_dispatch.

**Fix**: 
```yaml
# Trước
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr || true }}

# Sau  
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr != '' && github.event.inputs.skip_draft_pr || true }}
```

**Gi<PERSON>i thích**: Sử dụng ternary operator để kiểm tra input có tồn tại không, nếu có thì dùng giá trị đó, nếu không thì mặc định là true.

## 2. Fix cho reusable-frontend-ci.yml (dòng 106)

**Vấn đề**: Pattern chỉ kiểm tra `src/frontend/`, `package.json` và `pnpm-lock.yaml`, bỏ sót các file config quan trọng.

**Fix**:
```bash
# Trước
'^(${{ inputs.frontend_path }}/|package\.json|pnpm-lock\.yaml)'

# Sau
'^(${{ inputs.frontend_path }}/|package\.json|pnpm-lock\.yaml|tsconfig(\..*)?.json|turbo\.json|\.npmrc|\.eslintrc(\..*)?\.|next\.config\.js|next-env\.d\.ts|public/|styles/)'
```

**Các file config được thêm**:
- `tsconfig.json` và `tsconfig.*.json` (TypeScript config)
- `turbo.json` (Turborepo config)
- `.npmrc` (NPM config)
- `.eslintrc` và `.eslintrc.*` (ESLint config)
- `next.config.js` (Next.js config)
- `next-env.d.ts` (Next.js types)
- `public/` (Static assets)
- `styles/` (Styles directory)

## 3. Fix cho cd-release.yml (dòng 75-82)

**Vấn đề**: Script set should-deploy=true mà không verify TARGET_BRANCH tồn tại trên remote.

**Fix được thêm**:
```bash
# Validate the branch exists on remote
if ! git ls-remote origin "refs/heads/$TARGET_BRANCH" | grep -q "refs/heads/$TARGET_BRANCH"; then
  echo "Target branch '$TARGET_BRANCH' does not exist on remote"
  echo "should-deploy=false" >> $GITHUB_OUTPUT
  exit 0
fi

# Perform minimal fetch to ensure downstream checkouts succeed
git fetch --depth=1 origin "$TARGET_BRANCH"
```

**Giải thích**: 
- Kiểm tra branch tồn tại trên remote trước khi deploy
- Thực hiện minimal fetch để đảm bảo downstream checkout thành công
- Nếu branch không tồn tại, set should-deploy=false và exit 0

## Tác động

1. **ci-build.yml**: Cho phép user override skip_draft_pr qua workflow_dispatch
2. **reusable-frontend-ci.yml**: CI sẽ chạy khi các file config quan trọng thay đổi
3. **cd-release.yml**: Tránh lỗi checkout khi target branch không tồn tại

## Lưu ý

- Fix cho reusable-frontend-ci.yml cần được apply vào repo k8s-deployment
- Fix cho ci-build.yml và cd-release.yml cần được apply vào repo fe-osp-shared
