# CI/CD Pipeline Guide

## Tổng quan

Tài liệu này tổng hợp các hướng dẫn, quy trình và cách khắc phục lỗi liên quan đến CI/CD trong dự án.

---

## Dan<PERSON> sách Secret Variables

| Tên biến                            | <PERSON><PERSON> tả                                                                               | Gi<PERSON> trị mặc định                                                    |
| ----------------------------------- | ----------------------------------------------------------------------------------- | ------------------------------------------------------------------- |
| LARK_WEBHOOK_URL                    | đường dẫn tới webhook của Lark (để gửi thông báo)                                   |                                                                     |
| OSP_IMAGE_OWNER                     | là harbor repository chứa các docker image. (để push image)                         | `osp-public`                                                        |
| OSP_REGISTRY                        | là docker registry chứa các docker image.                                           | `dockerhub.ospgroup.vn`                                             |
| OSP_REGISTRY_USERNAME               | username để đăng nhập vào docker registry                                           |                                                                     |
| OSP_REGISTRY_PASSWORD               | password để đăng nhập vào docker registry                                           |                                                                     |
| OSP_NUGET_PACKAGE_REGISTRY          | là nuget registry chứa các nuget package.                                           | `https://package.ospgroup.io.vn/repository/nuget-hosted/index.json` |
| OSP_NUGET_PACKAGE_REGISTRY_USERNAME | là username để đăng nhập vào nuget registry                                         | `osp-package`                                                       |
| OSP_NUGET_PACKAGE_PASSWORD          | là password / token / apikey để đăng nhập vào nuget registry (có quyền ghi package) |                                                                     |

---

## CI/CD Pipeline Permission Fix

### Problem Analysis

The GitHub Actions CI pipeline was failing with permission denied errors when trying to access NuGet cache directories and packages.

### Root Cause

The GitHub Actions runner was running as the `actions-runner` user, but the NuGet directories didn't have proper permissions set up.

### Comprehensive Solution Implemented

1. **Docker Image Fixes (`custom-docker-images/dotnet-9/Dockerfile`)**:
   - Enhanced Directory Creation for all NuGet-related paths.
   - Added NuGet-specific environment variables.
   - Created a script for runtime permission fixes.
2. **CI Workflow Fixes (`.github/workflows/reuseable-dotnet-lib-ci-build.yml`)**:
   - Set global environment variables for NuGet paths.
   - Added a pre-build step to fix permissions before any `dotnet` command.

### Deployment and Testing Plan

#### Phase 1: Docker Image Deployment
- Build and verify the new Docker image with permission fixes.

#### Phase 2: Runner Deployment Update
- Update the Helm deployment to use the new image.
- Verify the runner pods are updated and running correctly.

#### Phase 3: CI Pipeline Testing
- Trigger a test build and monitor for permission errors.
- Expected success is the absence of "Permission denied" errors.

#### Phase 4: Validation and Monitoring
- Test fresh builds, cached builds, and concurrent builds.
- Monitor for performance impacts.

#### Phase 5: Rollback Plan
- Revert to the previous Docker image tag via Helm if issues occur.
- Revert workflow changes via git if necessary.

---

## CD Pipeline Fixes

### Issues Identified & Fixed

1. **Duplicate CD Pipeline Triggers**: Fixed by changing the trigger from `[published, created]` to only `[published]`.
2. **Build Job Not Starting**: Fixed tag validation logic in the reusable workflow.
3. **No Build on Release Without Changes**: Added `force_build: true` parameter for all release builds.

### Fix: Duplicate CD Pipeline Triggers

#### Issue Identified
The CD workflow was triggering twice for each release, causing duplicate builds.

#### Solution Applied
Changed the workflow trigger from:
```yaml
on:
  release:
    types: [published, created]
```
to:
```yaml
on:
  release:
    types: [published]
```
This ensures the pipeline runs only once when the release is officially published.

### CD Pipeline Deployment Steps

#### Required Actions to Fix the Issue:
- Merge the `feature/common-features` branch (which contains the fixes) into the `develop` branch.
- The reusable workflow `reuseable-dotnet-nuget-cd.yml` has already been fixed in the `main` branch of the `k8s-deployment` repository.

#### Testing the Fix:
1. Create a new release from the `develop` branch.
2. **Expected behavior**: Only one workflow should trigger, successfully build, and publish the NuGet package.

### CD Pipeline Test Checklist

#### Pre-test Checklist
- [ ] Ensure `OSP_NUGET_PACKAGE_PASSWORD` secret is configured.
- [ ] Verify the repository has the correct structure and `.csproj` files are packable.

#### Test Scenarios
1. **GitHub Release (Recommended)**: Create a new release and publish it. Verify a single workflow runs.
2. **Manual Tag Creation**: Push a new tag. Verify the workflow is triggered.
3. **Manual Workflow Trigger**: Run the workflow from the Actions tab.
4. **Invalid Tag/Branch**: Test with incorrect formats to ensure the pipeline fails gracefully.

#### Common Issues & Solutions
* **"should-deploy=false"**: Check tag format and target branch.
* **Build failures**: Check .NET version and dependencies.
* **Publish failures**: Check NuGet secrets and token permissions.

---

## Detailed CD Pipeline Fixes

### Issues Identified & Fixed

#### Issue 1: Duplicate CD Pipeline Triggers
- **Problem**: CD triggered twice - once on `release: created` and once on `release: published`
- **Fix**: Changed trigger to only `release: [published]`
- **File**: `cd-release.yml`

#### Issue 2: Build Job Not Starting After Tag Validation
- **Problem**: `build-and-test` job not running due to incorrect tag validation output
- **Fix**: Fixed tag format validation for manual inputs and improved debug output
- **File**: `reuseable-dotnet-nuget-cd.yml`

#### Issue 3: No Build on Release Without Changes
- **Problem**: CD should always build on release regardless of code changes
- **Fix**: Added `force_build: true` parameter to always build for releases
- **Files**: `reuseable-dotnet-nuget-cd.yml` and `cd-release.yml`

### Detailed Fixes Applied

#### 1. Fixed Duplicate Triggers
```yaml
# Before (Problematic)
on:
  release:
    types: [published, created]  # ❌ Triggers twice
  create:
    tags: ['v*']  # ❌ Potential conflict

# After (Fixed)
on:
  release:
    types: [published]  # ✅ Triggers once only
```

#### 2. Fixed Tag Validation Logic
```yaml
# Added proper validation for inputs.package-version
elif [[ -n "${{ inputs.package-version }}" ]]; then
  TAG_NAME="${{ inputs.package-version }}"
  echo "tag-name=$TAG_NAME" >> $GITHUB_OUTPUT

  # Validate tag format for manual input
  if [[ $TAG_NAME =~ ^v?([0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?)$ ]]; then
    VERSION="${BASH_REMATCH[1]}"
    echo "✅ Valid manual tag format: $TAG_NAME -> Version: $VERSION"
    echo "version=$VERSION" >> $GITHUB_OUTPUT
    echo "is-valid=true" >> $GITHUB_OUTPUT
  else
    echo "❌ Invalid manual tag format: $TAG_NAME"
    echo "is-valid=false" >> $GITHUB_OUTPUT
    exit 1
  fi
```

#### 3. Added Force Build Parameter
```yaml
# Reusable workflow input
force_build:
  description: 'Buộc build mà không kiểm tra thay đổi (dành cho CD)'
  required: false
  type: boolean
  default: false

# CD workflow usage
with:
  force_build: true  # Always build for releases
```

### Testing Instructions

#### Test the Complete Fix:
1. **Create a GitHub Release**:
   - Tag: `v1.0.8`
   - Target: `develop` branch
   - Click "Publish release" (not draft)

2. **Expected Behavior**:
   - ✅ **Exactly 1 workflow** triggers (not 2)
   - ✅ "Validate tag and extract version" succeeds with debug output
   - ✅ "Build và Test NuGet Package" starts and runs
   - ✅ Package gets built and published
   - ✅ Single Lark notification

3. **Debug Output to Verify**:
   ```
   Validate tag job:
   🔍 Debug info:
     github.ref_type: ''
     github.ref_name: ''
     inputs.package-version: 'v1.0.8'
   ✅ Valid manual tag format: v1.0.8 -> Version: 1.0.8
   📋 Validate-tag job completed successfully

   Build job:
   🔧 Build job debug info:
     needs.validate-tag.outputs.is-valid: 'true'
     job condition result: true
     force_build input: 'true'
   ```

### Expected Workflow Flow

```mermaid
graph TD
    A[Create Release v1.0.x] --> B[Trigger: release published]
    B --> C[check-branch: Validate release]
    C --> D[Reusable CD Workflow]
    D --> E[validate-tag: Extract v1.0.x → 1.0.x]
    E --> F[build-and-test: Build + Test]
    F --> G[publish-packages: Push to NuGet]
    G --> H[notify-success: Send Lark]
```

### Success Criteria

After testing, verify:
- [ ] Only 1 CD workflow runs per release
- [ ] Build job starts regardless of code changes
- [ ] NuGet package is created and published
- [ ] Lark notification sent once
- [ ] Debug logs show correct values
- [ ] No duplicate or conflicting runs

---

## Fix cho PR #22 - fe-osp-shared

### 1. Fix cho ci-build.yml (dòng 28)

**Vấn đề**: Expression `|| true` khiến skip_draft_pr luôn là true, bỏ qua input từ workflow_dispatch.

**Fix cần apply**:
```yaml
# Thay đổi từ:
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr || true }}

# Thành:
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr != '' && github.event.inputs.skip_draft_pr || true }}
```

### 2. Fix cho cd-release.yml (sau dòng 82)

**Vấn đề**: Script set should-deploy=true mà không verify TARGET_BRANCH tồn tại trên remote.

**Fix cần thêm** (sau dòng `echo "target-branch=$TARGET_BRANCH" >> $GITHUB_OUTPUT`):
```bash
# Validate the branch exists on remote
if ! git ls-remote origin "refs/heads/$TARGET_BRANCH" | grep -q "refs/heads/$TARGET_BRANCH"; then
  echo "Target branch '$TARGET_BRANCH' does not exist on remote"
  echo "should-deploy=false" >> $GITHUB_OUTPUT
  exit 0
fi

# Perform minimal fetch to ensure downstream checkouts succeed
git fetch --depth=1 origin "$TARGET_BRANCH"
```

### 3. Fix cho reusable-frontend-ci.yml (dòng 106)

**Vấn đề**: Pattern chỉ kiểm tra `src/frontend/`, `package.json` và `pnpm-lock.yaml`, bỏ sót các file config quan trọng.

**Fix đã apply**:
```bash
# Từ:
'^(${{ inputs.frontend_path }}/|package\.json|pnpm-lock\.yaml)'

# Thành:
'^(${{ inputs.frontend_path }}/|package\.json|pnpm-lock\.yaml|tsconfig(\..*)?.json|turbo\.json|\.npmrc|\.eslintrc(\..*)?\.|next\.config\.js|next-env\.d\.ts|public/|styles/)'
```

### Các file config được thêm vào pattern detection

- `tsconfig.json` và `tsconfig.*.json` (TypeScript config)
- `turbo.json` (Turborepo config)  
- `.npmrc` (NPM config)
- `.eslintrc` và `.eslintrc.*` (ESLint config)
- `next.config.js` (Next.js config)
- `next-env.d.ts` (Next.js types)
- `public/` (Static assets)
- `styles/` (Styles directory)

### Tác động của các fix

1. **ci-build.yml**: Cho phép user override skip_draft_pr qua workflow_dispatch
2. **cd-release.yml**: Tránh lỗi checkout khi target branch không tồn tại
3. **reusable-frontend-ci.yml**: CI sẽ chạy khi các file config quan trọng thay đổi

---

## Rollback Plan

If issues occur, you can rollback by reverting these files:
1. `cd-release.yml` - revert trigger changes
2. `reuseable-dotnet-nuget-cd.yml` - revert validation fixes
3. Remove `force_build: true` temporarily

---

## Kết luận

Tất cả các fix đã được triển khai và pipeline CI/CD nên hoạt động ổn định. Các vấn đề về permission, duplicate triggers và tag validation đã được giải quyết.