# Hướng dẫn apply fixes cho fe-osp-shared

## Các file cần sửa trong repo fe-osp-shared:

### 1. .github/workflows/ci-build.yml

Thay đổi dòng 28:
```yaml
# Từ:
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr || true }}

# Thành:
skip_draft_pr: ${{ github.event.inputs.skip_draft_pr != '' && github.event.inputs.skip_draft_pr || true }}
```

### 2. .github/workflows/cd-release.yml

Thêm validation cho TARGET_BRANCH trong step "Check deployment conditions" (sau dòng 82):

```bash
# Validate the branch exists on remote
if ! git ls-remote origin "refs/heads/$TARGET_BRANCH" | grep -q "refs/heads/$TARGET_BRANCH"; then
  echo "Target branch '$TARGET_BRANCH' does not exist on remote"
  echo "should-deploy=false" >> $GITHUB_OUTPUT
  exit 0
fi

# Perform minimal fetch to ensure downstream checkouts succeed
git fetch --depth=1 origin "$TARGET_BRANCH"
```

## Commit message gợi ý:

```
fix: resolve workflow issues in PR #22

- Fix skip_draft_pr expression to allow workflow_dispatch override
- Add branch validation in cd-release workflow to prevent checkout failures
- Ensure target branch exists on remote before deployment

Fixes comment issues in PR #22
```

## Lưu ý:

1. Fix cho reusable-frontend-ci.yml đã được apply vào repo k8s-deployment
2. Các fix này cần được apply vào branch feature/update-config-release của repo fe-osp-shared
3. Sau khi apply, cần test workflow để đảm bảo hoạt động đúng
