# Docker Build Issues & Prevention Guide

## 🔍 Common Issues Analysis

Based on the CI/CD log from `https://github.com/ospgroupvn/k8s-deployment/actions/runs/18025042059/job/51290424675`, here are the main Docker build issues and their solutions:

## 🚨 **Issue 1: Base Image Dependency Problems**

### Problem
```dockerfile
FROM dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0
```
- Base image may not be accessible
- Registry authentication issues
- Image version conflicts

### Solutions

#### Option A: Use Public Base Image
```dockerfile
# Replace with public Ubuntu base
FROM ubuntu:22.04

# Install GitHub Actions runner manually
ARG RUNNER_VERSION=2.328.0
RUN curl -o actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz -L \
    https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz
```

#### Option B: Multi-stage Build with Fallback
```dockerfile
# Try private registry first, fallback to public
FROM dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0 as private-base
FROM ubuntu:22.04 as public-base

# Use build arg to choose base
ARG USE_PRIVATE_BASE=false
FROM ${USE_PRIVATE_BASE:+private-base} ${USE_PRIVATE_BASE:-public-base} as final-base
```

## 🚨 **Issue 2: COPY Commands from Non-existent Images**

### Problem
```dockerfile
COPY --from=dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0.4 /entrypoint.sh /actions-runner/entrypoint.sh
```
- Copying from images that may not exist
- Version mismatches (1.0 vs 1.0.4)

### Solutions

#### Option A: Include Scripts in Repository
```dockerfile
# Copy local scripts instead
COPY scripts/entrypoint.sh /actions-runner/entrypoint.sh
COPY scripts/register-runner.sh /register-runner.sh
```

#### Option B: Create Scripts Inline
```dockerfile
# Create entrypoint script inline
RUN echo '#!/bin/bash' > /entrypoint.sh && \
    echo 'set -e' >> /entrypoint.sh && \
    echo '# Your script content here' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh
```

## 🚨 **Issue 3: .NET Installation Problems**

### Problem
- Package repository issues
- Version conflicts
- Missing dependencies

### Solutions

#### Robust .NET Installation
```dockerfile
# Install .NET 9 with error handling
RUN apt-get update && \
    apt-get install -y wget ca-certificates && \
    wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb && \
    dpkg -i packages-microsoft-prod.deb && \
    rm packages-microsoft-prod.deb && \
    apt-get update && \
    apt-get install -y dotnet-sdk-9.0 && \
    rm -rf /var/lib/apt/lists/* && \
    # Verify installation
    dotnet --version
```

## 🚨 **Issue 4: Permission and User Issues**

### Problem
- User creation conflicts
- Permission denied errors
- File ownership issues

### Solutions

#### Safe User Management
```dockerfile
# Create user safely
RUN groupadd -g 1000 runner || true && \
    useradd -m -s /bin/bash -u 1000 -g runner runner || true && \
    mkdir -p /home/<USER>
    chown -R runner:runner /home/<USER>
```

## 🛠️ **Complete Fixed Dockerfile**

```dockerfile
# OSP Custom GitHub Runner with .NET 9 SDK - Fixed Version
FROM ubuntu:22.04

# Metadata
LABEL maintainer="OSP DevOps Team"
LABEL description="OSP Custom GitHub Runner with .NET 9 SDK"
LABEL version="1.0.0"

# Arguments
ARG DOTNET_VERSION=9.0
ARG RUNNER_VERSION=2.328.0

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV DOTNET_ROOT=/usr/share/dotnet
ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV DOTNET_NOLOGO=1
ENV PATH="${PATH}:/usr/share/dotnet"

# Install basic dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    ca-certificates \
    apt-transport-https \
    software-properties-common \
    gnupg \
    lsb-release \
    jq \
    git \
    unzip \
    tar \
    sudo \
    libc6-dev \
    libgdiplus \
    libssl-dev \
    libicu-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI (optional)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli docker-buildx-plugin docker-compose-plugin && \
    rm -rf /var/lib/apt/lists/*

# Install .NET 9 SDK with error handling
RUN wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb && \
    dpkg -i packages-microsoft-prod.deb && \
    rm packages-microsoft-prod.deb && \
    apt-get update && \
    apt-get install -y dotnet-sdk-9.0 && \
    rm -rf /var/lib/apt/lists/* && \
    # Verify installation
    dotnet --version && \
    dotnet --list-sdks && \
    dotnet --list-runtimes

# Create runner user safely
RUN groupadd -g 1000 runner && \
    useradd -m -s /bin/bash -u 1000 -g runner runner && \
    usermod -aG sudo runner && \
    echo "runner ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create GitHub Actions runner directory
RUN mkdir -p /actions-runner && chown runner:runner /actions-runner

# Download and install GitHub Actions runner
WORKDIR /actions-runner
RUN curl -o actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz -L \
    https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz && \
    tar xzf ./actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz && \
    rm actions-runner-linux-x64-${RUNNER_VERSION}.tar.gz && \
    chown -R runner:runner /actions-runner

# Install runner dependencies
RUN ./bin/installdependencies.sh

# Create working directories
RUN mkdir -p /workspace && \
    mkdir -p /home/<USER>/.nuget/NuGet && \
    mkdir -p /home/<USER>/.nuget/packages && \
    mkdir -p /home/<USER>/.local/share/NuGet/http-cache && \
    mkdir -p /home/<USER>/.dotnet && \
    chown -R runner:runner /workspace && \
    chown -R runner:runner /home/<USER>

# Create NuGet.Config
RUN echo '<?xml version="1.0" encoding="utf-8"?>' > /home/<USER>/.nuget/NuGet/NuGet.Config && \
    echo '<configuration>' >> /home/<USER>/.nuget/NuGet/NuGet.Config && \
    echo '  <packageSources>' >> /home/<USER>/.nuget/NuGet/NuGet.Config && \
    echo '    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />' >> /home/<USER>/.nuget/NuGet/NuGet.Config && \
    echo '  </packageSources>' >> /home/<USER>/.nuget/NuGet/NuGet.Config && \
    echo '</configuration>' >> /home/<USER>/.nuget/NuGet/NuGet.Config && \
    chown runner:runner /home/<USER>/.nuget/NuGet/NuGet.Config

# Pre-warm .NET
WORKDIR /tmp
RUN dotnet new console -n warmup && \
    cd warmup && \
    dotnet restore && \
    dotnet build && \
    cd .. && \
    rm -rf warmup

# Create entrypoint script
RUN echo '#!/bin/bash' > /entrypoint.sh && \
    echo 'set -e' >> /entrypoint.sh && \
    echo 'echo "=== .NET Information ==="' >> /entrypoint.sh && \
    echo 'dotnet --version' >> /entrypoint.sh && \
    echo 'dotnet --list-sdks' >> /entrypoint.sh && \
    echo 'if [[ -n "$REPO_URL" && -n "$ACCESS_TOKEN" ]]; then' >> /entrypoint.sh && \
    echo '  echo "Configuring GitHub Actions runner..."' >> /entrypoint.sh && \
    echo '  ./config.sh --url "$REPO_URL" --token "$ACCESS_TOKEN" --name "${RUNNER_NAME:-dotnet9-runner}" --labels "${RUNNER_LABELS:-self-hosted,linux,x64,docker,dotnet,dotnet9}" --unattended --replace' >> /entrypoint.sh && \
    echo '  echo "Starting GitHub Actions runner..."' >> /entrypoint.sh && \
    echo '  ./run.sh' >> /entrypoint.sh && \
    echo 'else' >> /entrypoint.sh && \
    echo '  echo "Environment variables REPO_URL and ACCESS_TOKEN are required"' >> /entrypoint.sh && \
    echo '  exit 1' >> /entrypoint.sh && \
    echo 'fi' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh && \
    chown runner:runner /entrypoint.sh

# Switch to runner user
USER runner

# Set working directory
WORKDIR /actions-runner

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD dotnet --version || exit 1

# Entry point
ENTRYPOINT ["/entrypoint.sh"]
```

## 🔧 **Build Script Improvements**

### Enhanced Build Script
```bash
#!/bin/bash
set -e

# Build with error handling
build_image() {
    echo "Building Docker image..."
    
    # Check if base image is accessible
    if ! docker pull ubuntu:22.04; then
        echo "Error: Cannot pull base image"
        exit 1
    fi
    
    # Build with retry logic
    for i in {1..3}; do
        if docker build -t osp-custom-runner-dotnet-9:latest .; then
            echo "Build successful on attempt $i"
            break
        else
            echo "Build failed on attempt $i"
            if [ $i -eq 3 ]; then
                echo "Build failed after 3 attempts"
                exit 1
            fi
            sleep 10
        fi
    done
}

# Test the built image
test_image() {
    echo "Testing built image..."
    
    # Test .NET installation
    if ! docker run --rm osp-custom-runner-dotnet-9:latest dotnet --version; then
        echo "Error: .NET not working in image"
        exit 1
    fi
    
    echo "Image test passed"
}

# Main execution
main() {
    build_image
    test_image
    echo "Build completed successfully"
}

main "$@"
```

## 🚀 **Prevention Strategies**

### 1. **Use Multi-stage Builds**
- Separate build and runtime stages
- Reduce final image size
- Better error isolation

### 2. **Implement Health Checks**
- Verify installations during build
- Add runtime health checks
- Fail fast on errors

### 3. **Use Build Arguments**
- Make versions configurable
- Enable different build modes
- Support multiple environments

### 4. **Add Retry Logic**
- Handle network issues
- Retry failed downloads
- Implement exponential backoff

### 5. **Comprehensive Testing**
- Test each layer separately
- Verify all functionality
- Include integration tests

## 📋 **Checklist for Successful Builds**

- [ ] Base image is accessible
- [ ] All COPY sources exist
- [ ] Package repositories are available
- [ ] User permissions are correct
- [ ] Environment variables are set
- [ ] Health checks pass
- [ ] Scripts are executable
- [ ] Dependencies are installed
- [ ] Verification tests pass
- [ ] Registry credentials are configured

## 🔧 **Quick Fix Commands**

### For the Current Build Issue:

1. **Update the Dockerfile** (already done in our repository):
```bash
# The improved Dockerfile addresses:
# - Better error handling with set -e
# - Improved logging and verification
# - Robust .NET installation
# - Enhanced user management
```

2. **Use the Enhanced Build Script**:
```bash
cd custom-docker-images/dotnet-9
chmod +x build.sh

# Build without pushing (safe for testing)
./build.sh --no-push

# Build with retry and comprehensive testing
./build.sh --no-push --test --verbose

# Build and push (when registry credentials are configured)
./build.sh --push
```

3. **Manual Build with Error Handling**:
```bash
# Test base image first
docker pull ubuntu:22.04

# Build with explicit error handling
docker build \
  --build-arg DOTNET_VERSION=9.0 \
  --build-arg RUNNER_VERSION=2.328.0 \
  --tag osp-custom-runner-dotnet-9:test \
  --progress=plain \
  custom-docker-images/dotnet-9/

# Test the built image
docker run --rm osp-custom-runner-dotnet-9:test dotnet --version
```

## 🚨 **Emergency Fixes for CI/CD**

If the build is failing in CI/CD, apply these immediate fixes:

### Option 1: Use Public Base Image
Replace the FROM line in Dockerfile:
```dockerfile
# Change from:
FROM dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0

# To:
FROM ubuntu:22.04
```

### Option 2: Skip Registry Push
Modify the workflow to build without pushing:
```yaml
- name: Build Docker Image
  run: |
    cd custom-docker-images/dotnet-9
    ./build.sh --no-push --test
```

### Option 3: Use Manual Workflow
Trigger the manual workflow we created:
```bash
gh workflow run manual-dotnet9-build.yml
```

## 📊 **Expected Results**

After applying these fixes, you should see:

✅ **Successful Build Output:**
```
🚀 Starting OSP Custom Runner .NET 9 build process...
📋 Step 1: Checking prerequisites...
[SUCCESS] Prerequisites check passed
[SUCCESS] Base image ubuntu:22.04 is accessible
🔨 Step 2: Building Docker image...
[SUCCESS] Docker image built successfully
🧪 Step 3: Testing built image...
[SUCCESS] ✅ .NET 9 project creation and build test passed
[SUCCESS] 🎉 All image tests completed successfully!
🎉 Build process completed successfully!
```

✅ **Working .NET 9 Image:**
- Image size: ~2GB
- .NET 9 SDK installed and verified
- GitHub Actions runner ready
- Docker CLI available
- Comprehensive testing passed

## 🎯 **Next Steps After Successful Build**

1. **Configure Registry Credentials** (if needed):
   ```bash
   # Add these secrets to GitHub repository:
   OSP_REGISTRY_USERNAME
   OSP_REGISTRY_PASSWORD
   OSP_REGISTRY
   OSP_IMAGE_OWNER
   ```

2. **Deploy to Kubernetes**:
   ```bash
   # Use the built image in your K8s deployment
   kubectl set image deployment/github-runner \
     runner=osp-custom-runner-dotnet-9:latest
   ```

3. **Test in Production**:
   ```bash
   # Run a test .NET 9 build in your CI/CD pipeline
   # Verify all .NET 9 features work as expected
   ```

This comprehensive fix addresses all common Docker build issues and provides multiple fallback options to ensure successful builds.
