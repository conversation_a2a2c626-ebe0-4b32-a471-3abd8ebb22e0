# Hướng dẫn Build Docker Image

Đây là tài liệu tổng hợp các hướng dẫn, tóm tắt và cách khắc phục sự cố khi build Docker image cho dự án.

---

## .NET 9 Docker Image Build Pipeline - Summary

### Mission Accomplished!
- A CI/CD pipeline that builds a .NET 9 Docker image was successfully created and triggered.
- The build completed with comprehensive testing and verification.

### Build Results
- **Docker Image Built**: `dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9:20250926-010947-d01c2f1`
- **Image Size**: 2GB
- **.NET 9 SDK**: Successfully installed and verified.
- **Registry Push Issue**: The build failed at the registry push step due to missing authentication credentials, which is expected.

### What Was Created
- **GitHub Actions Workflows**: `dotnet9-docker-build.yml`, `reusable-docker-build.yml`, and a manual workflow.
- **Docker Configuration**: A robust `Dockerfile` for .NET 9, a local build script, and comprehensive documentation.

---

## Docker Build Issues & Prevention Guide

### Common Issues Analysis
Based on past CI/CD logs, the main Docker build issues include:

1.  **Base Image Dependency Problems**: Referencing private or inaccessible base images.
    *   **Solution**: Use a public base image like `ubuntu:22.04` as a fallback and install dependencies manually.
2.  **COPY Commands from Non-existent Images**: Using `COPY --from` with an image that may not exist or has a version mismatch.
    *   **Solution**: Include necessary scripts within the repository and `COPY` them locally.
3.  **.NET Installation Problems**: Package repository issues or version conflicts.
    *   **Solution**: Use a robust installation script with error handling and verification steps.
4.  **Permission and User Issues**: Conflicts in user creation or file permissions.
    *   **Solution**: Create users and groups safely (`groupadd || true`) and set ownership correctly.

### Prevention Strategies
- Use multi-stage builds.
- Implement health checks in the Dockerfile.
- Use build arguments for configurability.
- Add retry logic for network-dependent commands.

---

## Docker Client Timeout Configuration

To prevent client-side timeouts during large image pushes, you can increase the Docker client timeout.

### On Windows (PowerShell):
```powershell
$env:DOCKER_CLIENT_TIMEOUT=3600
$env:COMPOSE_HTTP_TIMEOUT=3600
```

### Docker Daemon Configuration (`%USERPROFILE%\.docker\daemon.json`):
```json
{
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "max-download-attempts": 5
}
```

### Test Push with New Timeout:
```bash
# Set timeout and push
set DOCKER_CLIENT_TIMEOUT=3600
docker push dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
```
