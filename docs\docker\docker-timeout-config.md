# Docker Client Timeout Configuration

## 1. Tăng Docker Client Timeout (Windows)

### Cách 1: Environment Variables
```bash
# Trong PowerShell hoặc CMD
set DOCKER_CLIENT_TIMEOUT=3600
set COMPOSE_HTTP_TIMEOUT=3600

# Hoặc trong PowerShell
$env:DOCKER_CLIENT_TIMEOUT=3600
$env:COMPOSE_HTTP_TIMEOUT=3600
```

### Cách 2: Docker Desktop Settings
1. Mở Docker Desktop
2. Vào Settings > Docker Engine
3. Thêm cấu hình:
```json
{
  "registry-mirrors": [],
  "insecure-registries": [],
  "debug": false,
  "experimental": false,
  "features": {
    "buildkit": true
  },
  "builder": {
    "gc": {
      "enabled": true,
      "defaultKeepStorage": "20GB"
    }
  },
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "max-download-attempts": 5
}
```

### Cách 3: Docker Daemon Configuration
Tạo hoặc sửa file `%USERPROFILE%\.docker\daemon.json`:
```json
{
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "max-download-attempts": 5,
  "shutdown-timeout": 15
}
```

## 2. Test Push với Timeout mới
```bash
# Set timeout và push
set DOCKER_CLIENT_TIMEOUT=3600
set COMPOSE_HTTP_TIMEOUT=3600
docker push dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0
```

## 3. Kiểm tra Network
```bash
# Test connectivity
ping dockerhub.ospgroup.vn
nslookup dockerhub.ospgroup.vn

# Test HTTP connection
curl -I http://dockerhub.ospgroup.vn
```