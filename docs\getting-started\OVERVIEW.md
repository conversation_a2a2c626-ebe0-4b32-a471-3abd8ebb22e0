# Tổng quan về dự án Kubernetes Deployment

## Giới thiệu

Dự án Kubernetes Deployment của OSP Group là một hệ thống hoàn chỉnh để triển khai và quản lý ứng dụng trên Kubernetes theo phương pháp GitOps. Dự án này cung cấp một nền tảng chuẩn hóa để triển khai các dịch vụ nền tảng và ứng dụng business một cách nhất quán và an toàn.

## Mục tiêu

1. **Standardization**: Cung cấp một quy trình chuẩn hóa để triển khai Kubernetes cluster
2. **Automation**: Tự động hóa việc triển khai và quản lý thông qua GitOps
3. **Scalability**: Hỗ trợ scaling theo nhu cầu business
4. **Security**: Đ<PERSON>m bảo security best practices trong toàn bộ hệ thống
5. **Observability**: <PERSON><PERSON> cấp monitoring và logging cho toàn bộ hệ thống

## Kiến trúc tổng quan

```
┌─────────────────────────────────────────────────────────────┐
│                        Git Repository                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │   Bootstrap  │  │   Platform  │  │     Applications       │  │
│  │   Services  │  │   Services  │  │                        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                         GitOps Sync
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Kubernetes Cluster                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │   Namespace │  │   Namespace │  │     Multiple           │  │
│  │   bootstrap  │  │platform-services│  │   Application          │  │
│  │             │  │             │  │     Namespaces         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Các thành phần chính

### Bootstrap Services
Các dịch vụ nền tảng cần thiết cho Kubernetes cluster:
- **HashiCorp Vault**: Quản lý secrets và bảo mật
- **Traefik Gateway**: Kubernetes Gateway API và Load Balancer
- **ArgoCD**: GitOps Continuous Deployment

### Platform Services
Các dịch vụ nền tảng được chia sẻ giữa các ứng dụng:
- **PostgreSQL**: Database
- **Redis**: Cache
- **MinIO**: Object Storage
- **Harbor**: Container Registry
- **SigNoz**: Observability Platform
- **Keycloak**: Authentication

### Applications
Các ứng dụng business sử dụng external database/redis từ platform services.

## Nguyên tắc hoạt động

### 1. GitOps là nguồn chân lý duy nhất
- Git là nguồn chân lý duy nhất (single source of truth) cho toàn bộ Kubernetes cluster.
- Mọi thay đổi phải được thực hiện qua Git và ArgoCD.
- Không sử dụng `kubectl apply` trực tiếp (trừ bootstrap).

### 2. Tách biệt Platform Services và Applications
- Platform Services được triển khai trong namespace `platform-services` với vòng đời độc lập.
- Applications không tự deploy DB/Redis đi kèm, sử dụng external database/redis từ platform services.

### 3. Multi-tenant Database/Redis
- Mỗi ứng dụng có database/user riêng trên cùng một cluster PostgreSQL/Redis.
- Không chia sẻ user/schema giữa các ứng dụng.
- Sử dụng Vault để quản lý credentials với dynamic creds per-app (rotation).

### 4. ArgoCD App of Apps
- Sử dụng ArgoCD App of Apps pattern để quản lý deployment.
- Thứ tự sync: platform services trước, applications sau (sử dụng sync-waves).

## Công nghệ sử dụng

### Container Orchestration
- **Kubernetes**: Container orchestration platform
- **Helm**: Package manager cho Kubernetes

### GitOps
- **ArgoCD**: GitOps continuous delivery tool
- **GitHub**: Source code management

### Gateway API
- **Traefik**: Modern reverse proxy and load balancer
- **Kubernetes Gateway API**: Official Kubernetes API for routing

### Security
- **Vault**: Secrets management
- **Keycloak**: Identity and access management

### Observability
- **SigNoz**: Observability platform (metrics, logs, traces)
- **Prometheus**: Metrics collection (via SigNoz)

### Container Registry
- **Harbor**: Container registry with security scanning

## Quy trình triển khai

### 1. Bootstrap Phase
- Triển khai các dịch vụ nền tảng trong namespace `bootstrap`
- Cấu hình Vault, Traefik Gateway, và ArgoCD
- Thiết lập Gateway API và external access

### 2. Platform Services Phase
- Triển khai các dịch vụ nền tảng trong namespace `platform-services`
- Cấu hình database, cache, storage, và monitoring
- Thiết lập external access qua Gateway API

### 3. Applications Phase
- Triển khai các ứng dụng business trong các namespace riêng biệt
- Sử dụng external database/redis từ platform services
- Cấu hình HTTPRoute để expose services qua Gateway API

## Các bước tiếp theo

1. **Đọc tài liệu**: Xem [Quick Start Guide](QUICK_START.md) để bắt đầu
2. **Hiểu kiến trúc**: Đọc [System Architecture](../architecture/SYSTEM_ARCHITECTURE.md) để hiểu chi tiết
3. **Tuân thủ quy tắc**: Xem [Coding Guidelines](../standards/CODING_GUIDELINES.md) để hiểu quy tắc
4. **Triển khai**: Sử dụng [Deployment Guides](../guides/DEPLOYMENT_GUIDES.md) để triển khai

## Liên hệ

- **Maintainer**: OSP Group
- **Email**: <EMAIL>
- **Repository**: https://github.com/ospgroupvn/k8s-deployment

## Đóng góp

Khi đóng góp vào dự án:
1. Tuân thủ các quy tắc trong [Coding Guidelines](../standards/CODING_GUIDELINES.md)
2. Sử dụng templates từ [Coding Templates](../standards/CODING_TEMPLATES.md)
3. Đảm bảo tất cả changes được thực hiện qua Git và ArgoCD
4. Cập nhật tài liệu khi cần thiết