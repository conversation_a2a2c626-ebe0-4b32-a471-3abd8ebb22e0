# Hướng dẫn bắt đầu nhanh

## <PERSON><PERSON><PERSON> cầu tiên quyết

### 1. <PERSON><PERSON><PERSON> trường phát triển
- Git client
- kubectl client
- Helm 3.x
- Docker (nếu cần build images)

### 2. Kubernetes cluster
- Kubernetes cluster v1.24+ với Gateway API CRDs
- MetalLB hoặc cloud load balancer (cho external access)
- Persistent storage provider

### 3. <PERSON>uyền truy cập
- Quyền admin trên Kubernetes cluster
- Quyền push vào GitHub repository

## Bắt đầu nhanh

### 1. Clone repository

```bash
git clone https://github.com/ospgroupvn/k8s-deployment.git
cd k8s-deployment
```

### 2. Cấu hình KUBECONFIG

```bash
export KUBECONFIG=$(pwd)/.kube/config
```

### 3. Kiểm tra cluster

```bash
# Kiểm tra nodes
kubectl get nodes

# Kiểm tra Gateway API CRDs
kubectl get crd | grep gateway

# Kiểm tra storage class
kubectl get storageclass
```

### 4. Triển khai Bootstrap Services

```bash
cd bootstrap

# Cài đặt tất cả bootstrap services
./common/install.sh

# Hoặc cài đặt từng service
helm install vault ./vault -n bootstrap
helm install traefik ./traefik -n bootstrap
helm install argocd ./argocd -n bootstrap
```

### 5. Kiểm tra Bootstrap Services

```bash
# Kiểm tra pods
kubectl get pods -n bootstrap

# Kiểm tra Gateway
kubectl get gateway -n bootstrap

# Kiểm tra HTTPRoutes
kubectl get httproute -n bootstrap
```

### 6. Triển khai Platform Services

```bash
# Deploy platform services App of Apps
kubectl apply -f platform-services/platform-services-apps.yaml

# Kiểm tra ArgoCD applications
kubectl get applications -n bootstrap | grep platform
```

### 7. Kiểm tra Platform Services

```bash
# Kiểm tra pods
kubectl get pods -n platform-services

# Kiểm tra services
kubectl get services -n platform-services

# Kiểm tra HTTPRoutes
kubectl get httproute -n platform-services
```

### 8. Truy cập các dịch vụ

#### Cấu hình hosts file

```bash
# Lấy external IP của Traefik Gateway
EXTERNAL_IP=$(kubectl get svc traefik-bootstrap -n bootstrap -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

# Thêm vào /etc/hosts
echo "$EXTERNAL_IP vault.local" | sudo tee -a /etc/hosts
echo "$EXTERNAL_IP argocd.local" | sudo tee -a /etc/hosts
echo "$EXTERNAL_IP harbor.local" | sudo tee -a /etc/hosts
echo "$EXTERNAL_IP minio.local" | sudo tee -a /etc/hosts
```

#### Truy cập các dịch vụ

- **ArgoCD**: http://argocd.local (admin/admin123)
- **Vault**: http://vault.local (token: bootstrap-root-token)
- **Harbor**: http://harbor.local (admin/Harbor12345)
- **MinIO**: http://minio.local (admin/minio123456)

### 9. Triển khai ứng dụng mẫu

```bash
# Tạo ArgoCD application cho ứng dụng mẫu
kubectl apply -f projects/sample-app/sample-app-app.yaml

# Kiểm tra deployment
kubectl get pods -n sample-app
kubectl get httproute -n sample-app
```

## Các bước tiếp theo

### 1. Đọc tài liệu chi tiết

- [System Architecture](../architecture/SYSTEM_ARCHITECTURE.md) - Hiểu kiến trúc hệ thống
- [Bootstrap Services](../architecture/BOOTSTRAP_SERVICES.md) - Chi tiết về bootstrap services
- [Platform Services](../architecture/PLATFORM_SERVICES.md) - Chi tiết về platform services
- [Coding Guidelines](../standards/CODING_GUIDELINES.md) - Quy tắc coding
- [Deployment Guides](../guides/DEPLOYMENT_GUIDES.md) - Hướng dẫn triển khai chi tiết

### 2. Tùy chỉnh cấu hình

- Cập nhật values files cho các services
- Cấu hình secrets trong Vault
- Tùy chỉnh Gateway API routes
- Cấu hình monitoring và logging

### 3. Triển khai ứng dụng mới

- Sử dụng [Coding Templates](../standards/CODING_TEMPLATES.md) để tạo ứng dụng mới
- Đọc [GitHub Runners Guide](../guides/GITHUB_RUNNERS.md) để thiết lập CI/CD
- Xem [CI/CD Pipeline Guide](../cicd/PIPELINE_GUIDE.md) để hiểu quy trình CI/CD

## Troubleshooting

### 1. Bootstrap Services không khởi động

```bash
# Kiểm tra logs
kubectl logs -n bootstrap -l app.kubernetes.io/name=vault
kubectl logs -n bootstrap -l app.kubernetes.io/name=traefik
kubectl logs -n bootstrap -l app.kubernetes.io/name=argocd

# Kiểm tra events
kubectl get events -n bootstrap --sort-by='.lastTimestamp'
```

### 2. Gateway API không hoạt động

```bash
# Kiểm tra Gateway Class
kubectl get gatewayclass

# Kiểm tra Gateway status
kubectl describe gateway traefik-gateway -n bootstrap

# Kiểm tra HTTPRoute status
kubectl describe httproute -n bootstrap
```

### 3. Platform Services không sync

```bash
# Kiểm tra ArgoCD application status
kubectl get application -n bootstrap

# Kiểm tra ArgoCD logs
kubectl logs -n bootstrap deployment/argocd-bootstrap-application-controller

# Sync thủ công
kubectl patch application <app-name> -n bootstrap -p '{"spec":{"syncPolicy":{"automated":{}}}}' --type=merge
```

### 4. Không thể truy cập dịch vụ

```bash
# Kiểm tra services
kubectl get svc -n bootstrap
kubectl get svc -n platform-services

# Kiểm tra endpoints
kubectl get endpoints -n bootstrap
kubectl get endpoints -n platform-services

# Port forward để test
kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200
```

## Best Practices

### 1. Security
- Không sử dụng mật khẩu mặc định trong production
- Kích hoạt TLS cho tất cả services
- Sử dụng Vault để quản lý secrets
- Regular rotation của credentials

### 2. Monitoring
- Thiết lập monitoring cho tất cả services
- Cấu hình alerts cho các metrics quan trọng
- Sử dụng centralized logging

### 3. Backup
- Backup Vault data
- Backup ArgoCD configurations
- Backup database data

### 4. GitOps
- Tất cả changes phải được thực hiện qua Git
- Sử dụng proper commit messages
- Review changes trước khi merge

## Liên hệ

Nếu có vấn đề hoặc câu hỏi:

- **Maintainer**: OSP Group
- **Email**: <EMAIL>
- **Repository**: https://github.com/ospgroupvn/k8s-deployment
- **Issues**: https://github.com/ospgroupvn/k8s-deployment/issues

## Tài liệu tham khảo

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [ArgoCD Documentation](https://argoproj.github.io/argo-cd/)
- [Traefik Documentation](https://doc.traefik.io/traefik/)
- [Vault Documentation](https://www.vaultproject.io/docs/)
- [Helm Documentation](https://helm.sh/docs/)