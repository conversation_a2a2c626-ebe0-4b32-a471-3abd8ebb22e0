# Reusable Maven Package CD Workflow

## Tổng quan

Workflow CD có thể tái sử dụng để tự động build, test và publish Maven packages khi có tag được tạo trên develop branch của các repository Java.

## Tính năng chính

✅ **Tự động trigger khi tạo tag** trên develop branch
✅ **Validate tag format** (v1.0.0, 1.0.0, v1.0.0-beta)
✅ **Build và test** tự động trước khi package
✅ **Maven deployment** - deploy JAR files lên Maven repository
✅ **Duplicate detection** - tránh publish package trùng lặp
✅ **Source và Javadoc JAR** - tạo source và javadoc artifacts
✅ **Artifact storage** - lưu packages để troubleshooting
✅ **Lark notification** - thông báo kết quả qua Lark
✅ **Manual trigger** - có thể chạy thủ công với custom tag

## Cách sử dụng

### 1. Tạo file `.github/workflows/cd-release.yml` trong repository

```yaml
name: 'CD Release - My Java Project'

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name cho package (ví dụ: v1.0.0)'
        required: true
        type: string
      force_publish:
        description: 'Bắt buộc publish ngay cả khi package đã tồn tại'
        required: false
        type: boolean
        default: false

jobs:
  release:
    name: 'Build và Publish Maven Package'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-maven-cd.yml@main
    with:
      src-directory: '.'
      java-version: '21'
      build-configuration: 'Release'
      enable-tests: true
      skip-duplicate: true
    secrets:
      OSP_PACKAGE_USERNAME: ${{ secrets.OSP_PACKAGE_USERNAME }}
      OSP_PACKAGE_PASSWORD: ${{ secrets.OSP_PACKAGE_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### 2. Cấu hình Repository Secrets

Trong repository settings → Secrets and variables → Actions, thêm:

- `OSP_PACKAGE_USERNAME`: Username cho Maven registry
- `OSP_PACKAGE_PASSWORD`: Password cho Maven registry  
- `LARK_WEBHOOK_URL`: Webhook URL cho Lark notifications (optional)

### 3. Cấu hình pom.xml

Đảm bảo pom.xml có cấu hình distribution management:

```xml
<distributionManagement>
    <repository>
        <id>osp-maven-releases</id>
        <name>OSP Maven Releases</name>
        <url>https://package.ospgroup.io.vn/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
        <id>osp-maven-snapshots</id>
        <name>OSP Maven Snapshots</name>
        <url>https://package.ospgroup.io.vn/repository/maven-snapshots/</url>
    </snapshotRepository>
</distributionManagement>
```

Và các plugin cần thiết:

```xml
<build>
    <plugins>
        <!-- Maven Source Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.3.1</version>
            <executions>
                <execution>
                    <id>attach-sources</id>
                    <goals>
                        <goal>jar-no-fork</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
        
        <!-- Maven Javadoc Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.10.1</version>
            <executions>
                <execution>
                    <id>attach-javadocs</id>
                    <goals>
                        <goal>jar</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

## Input Parameters

| Parameter               | Required | Default                                                     | Description                   |
| ----------------------- | -------- | ----------------------------------------------------------- | ----------------------------- |
| `src-directory`         | No       | `.`                                                         | Thư mục chứa source code      |
| `java-version`          | No       | `21`                                                        | Phiên bản Java                |
| `build-configuration`   | No       | `Release`                                                   | Build configuration           |
| `package-version`       | Yes      | -                                                           | Version cho package           |
| `maven-registry-url`    | No       | `https://package.ospgroup.io.vn/repository/maven-releases/` | URL Maven registry            |
| `enable-tests`          | No       | `true`                                                      | Có chạy tests không           |
| `skip-duplicate`        | No       | `true`                                                      | Bỏ qua nếu package đã tồn tại |
| `force_build`           | No       | `false`                                                     | Bắt buộc build                |
| `additional-build-args` | No       | `''`                                                        | Tham số bổ sung cho Maven     |

## Secrets

| Secret                 | Required | Description                        |
| ---------------------- | -------- | ---------------------------------- |
| `OSP_PACKAGE_USERNAME` | Yes      | Username cho Maven registry        |
| `OSP_PACKAGE_PASSWORD` | Yes      | Password cho Maven registry        |
| `LARK_WEBHOOK_URL`     | No       | Webhook URL cho Lark notifications |

## Workflow Steps

1. **Validate Tag**: Kiểm tra format của tag (semantic versioning)
2. **Build và Test**: Compile code, chạy tests
3. **Package**: Tạo JAR files (main, sources, javadoc)
4. **Upload Artifacts**: Lưu packages làm GitHub artifacts
5. **Publish**: Deploy lên Maven repository
6. **Notify**: Gửi thông báo kết quả qua Lark

## Troubleshooting

### Package đã tồn tại
- Sử dụng `force_publish: true` để override
- Hoặc tăng version number

### Build thất bại
- Kiểm tra Java version compatibility
- Đảm bảo tests pass locally
- Xem logs trong GitHub Actions

### Authentication lỗi
- Kiểm tra username/password trong secrets
- Đảm bảo user có quyền publish lên repository
