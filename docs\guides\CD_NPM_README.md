# Reusable NPM Package CD Workflow

## Tổng quan

Workflow CD có thể tái sử dụng để tự động build, test và publish NPM packages khi có tag được tạo trên develop branch của các repository Node.js/React.

## Tính năng chính

✅ **Tự động trigger khi tạo tag** trên develop branch
✅ **Validate tag format** (v1.0.0, 1.0.0, v1.0.0-beta)
✅ **Build và test** tự động trước khi package
✅ **NPM publish** - publish package lên NPM registry
✅ **Duplicate detection** - tránh publish package trùng lặp
✅ **TypeScript support** - type checking và build
✅ **Linting** - code quality checks
✅ **Artifact storage** - lưu build artifacts để troubleshooting
✅ **Lark notification** - thông báo kết quả qua Lark
✅ **Manual trigger** - có thể chạy thủ công với custom tag

## Cách sử dụng

### 1. Tạo file `.github/workflows/cd-release.yml` trong repository

```yaml
name: 'CD Release - My NPM Package'

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name cho package (ví dụ: v1.0.0)'
        required: true
        type: string
      force_publish:
        description: 'Bắt buộc publish ngay cả khi package đã tồn tại'
        required: false
        type: boolean
        default: false

jobs:
  release:
    name: 'Build và Publish NPM Package'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-npm-cd.yml@main
    with:
      src-directory: 'src/frontend'
      node-version: '20'
      pnpm-version: '10'
      build-configuration: 'production'
      enable-tests: true
      skip-duplicate: true
    secrets:
      OSP_PACKAGE_USERNAME: ${{ secrets.OSP_PACKAGE_USERNAME }}
      OSP_PACKAGE_PASSWORD: ${{ secrets.OSP_PACKAGE_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### 2. Cấu hình Repository Secrets

Trong repository settings → Secrets and variables → Actions, thêm:

- `OSP_PACKAGE_USERNAME`: Username cho NPM registry
- `OSP_PACKAGE_PASSWORD`: Password cho NPM registry  
- `LARK_WEBHOOK_URL`: Webhook URL cho Lark notifications (optional)

### 3. Cấu hình package.json

Đảm bảo package.json có cấu hình publishConfig:

```json
{
  "name": "@osp/my-package",
  "version": "1.0.0",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist",
    "README.md"
  ],
  "publishConfig": {
    "registry": "https://package.ospgroup.io.vn/repository/npm-hosted/",
    "access": "restricted"
  },
  "scripts": {
    "build": "rollup -c",
    "test": "jest",
    "lint": "eslint src --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  }
}
```

### 4. Cấu hình Build Tools

Đảm bảo có các file cấu hình:
- `tsconfig.json` - TypeScript configuration
- `rollup.config.js` hoặc build tool khác
- `.eslintrc.js` - ESLint configuration
- `jest.config.js` - Jest configuration (nếu có tests)

## Input Parameters

| Parameter               | Required | Default                                                 | Description                   |
| ----------------------- | -------- | ------------------------------------------------------- | ----------------------------- |
| `src-directory`         | No       | `src/frontend`                                          | Thư mục chứa source code      |
| `node-version`          | No       | `20`                                                    | Phiên bản Node.js             |
| `pnpm-version`          | No       | `10`                                                    | Phiên bản PNPM                |
| `build-configuration`   | No       | `production`                                            | Build configuration           |
| `package-version`       | Yes      | -                                                       | Version cho package           |
| `npm-registry-url`      | No       | `https://package.ospgroup.io.vn/repository/npm-hosted/` | URL NPM registry              |
| `enable-tests`          | No       | `true`                                                  | Có chạy tests không           |
| `skip-duplicate`        | No       | `true`                                                  | Bỏ qua nếu package đã tồn tại |
| `force_build`           | No       | `false`                                                 | Bắt buộc build                |
| `additional-build-args` | No       | `''`                                                    | Tham số bổ sung cho build     |

## Secrets

| Secret                 | Required | Description                        |
| ---------------------- | -------- | ---------------------------------- |
| `OSP_PACKAGE_USERNAME` | Yes      | Username cho NPM registry          |
| `OSP_PACKAGE_PASSWORD` | Yes      | Password cho NPM registry          |
| `LARK_WEBHOOK_URL`     | No       | Webhook URL cho Lark notifications |

## Workflow Steps

1. **Validate Tag**: Kiểm tra format của tag (semantic versioning)
2. **Setup Environment**: Cài đặt Node.js và PNPM
3. **Install Dependencies**: Cài đặt node_modules
4. **Type Check**: Kiểm tra TypeScript types
5. **Lint**: Chạy ESLint
6. **Test**: Chạy unit tests (nếu enabled)
7. **Build**: Build package với Rollup/Webpack
8. **Upload Artifacts**: Lưu build artifacts
9. **Publish**: Publish lên NPM registry
10. **Notify**: Gửi thông báo kết quả qua Lark

## Troubleshooting

### Package đã tồn tại
- Sử dụng `force_publish: true` để override
- Hoặc tăng version number trong package.json

### Build thất bại
- Kiểm tra TypeScript errors
- Đảm bảo tests pass locally
- Kiểm tra dependencies trong package.json

### Authentication lỗi
- Kiểm tra username/password trong secrets
- Đảm bảo user có quyền publish lên registry
- Kiểm tra .npmrc configuration

### Dependencies lỗi
- Đảm bảo pnpm-lock.yaml được commit
- Kiểm tra Node.js version compatibility
- Clear cache và reinstall dependencies

## Best Practices

1. **Versioning**: Sử dụng semantic versioning (major.minor.patch)
2. **Testing**: Luôn enable tests trong production builds
3. **Type Safety**: Sử dụng TypeScript và enable strict mode
4. **Documentation**: Cập nhật README.md khi có breaking changes
5. **Dependencies**: Giữ dependencies updated và secure
