# Reusable .NET NuGet Package CD Workflow

## Tổng quan

Workflow CD có thể tái sử dụng để tự động build, test và publish NuGet packages khi có tag được tạo trên develop branch của các repository .NET.

## Tính năng chính

✅ **Tự động trigger khi tạo tag** trên develop branch
✅ **Validate tag format** (v1.0.0, 1.0.0, v1.0.0-beta)
✅ **Build và test** tự động trước khi package
✅ **Multi-project support** - đóng gói nhiều project trong cùng solution
✅ **Duplicate detection** - tránh publish package trùng lặp
✅ **Symbols package** - tạo .snupkg cho debugging
✅ **Artifact storage** - lưu packages để troubleshooting
✅ **Lark notification** - thông báo kết quả qua Lark
✅ **Manual trigger** - c<PERSON> thể chạy thủ công với custom tag

## Cách sử dụng

### 1. Tạo CD workflow trong repository

Tạo file `.github/workflows/cd-release.yml`:

```yaml
name: 'CD Release - Your Project Name'

on:
  release:
    types: [published]  # Only when release is published (not draft)
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name (ví dụ: v1.0.0)'
        required: true
        type: string
      force_publish:
        description: 'Force publish even if exists'
        required: false
        type: boolean
        default: false

jobs:
  release:
    name: 'Build và Publish NuGet Package'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-dotnet-nuget-cd.yml@main
    with:
      src-directory: 'src'
      dotnet-version: '9.0.100'
      build-configuration: 'Release'
      enable-tests: true
      skip-duplicate: true
    secrets:
      OSP_NUGET_PACKAGE_PASSWORD: ${{ secrets.OSP_NUGET_PACKAGE_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### 2. Cấu hình Secrets

Thêm các secrets sau vào repository:

| Secret Name                  | Description                         | Required   |
| ---------------------------- | ----------------------------------- | ---------- |
| `OSP_NUGET_PACKAGE_PASSWORD` | Token để publish vào NuGet registry | ✅ Yes      |
| `LARK_WEBHOOK_URL`           | URL webhook Lark cho notifications  | ❌ Optional |

### 3. Quy trình Release

#### Tự động (Khuyến nghị):
1. Merge code vào `develop` branch
2. Tạo tag với format hợp lệ:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```
3. Workflow sẽ tự động chạy và publish package

#### Thủ công:
1. Vào Actions tab trong GitHub
2. Chọn "CD Release" workflow
3. Click "Run workflow"
4. Nhập tag name và các options cần thiết

## Tham số đầu vào (Inputs)

| Parameter                 | Type    | Default       | Description                   |
| ------------------------- | ------- | ------------- | ----------------------------- |
| `src-directory`           | string  | `src`         | Thư mục chứa source code      |
| `dotnet-version`          | string  | `8.0.x`       | Phiên bản .NET SDK            |
| `build-configuration`     | string  | `Release`     | Cấu hình build                |
| `package-version`         | string  | *(from tag)*  | Phiên bản package             |
| `nuget-registry-url`      | string  | OSP Registry  | URL của NuGet registry        |
| `nuget-registry-username` | string  | `osp-package` | Username cho registry         |
| `enable-tests`            | boolean | `true`        | Có chạy test trước package    |
| `skip-duplicate`          | boolean | `true`        | Bỏ qua nếu package đã tồn tại |
| `additional-build-args`   | string  | `''`          | Tham số build bổ sung         |

## Tag Format

Workflow hỗ trợ các định dạng tag sau:

✅ `v1.0.0` - Stable release
✅ `1.0.0` - Stable release (no prefix)
✅ `v1.0.0-beta` - Pre-release
✅ `v1.0.0-alpha.1` - Pre-release with build number
✅ `v2.1.0-rc.2` - Release candidate

❌ `release-1.0` - Invalid format
❌ `v1.0` - Missing patch version
❌ `latest` - Not semantic version

## Quy trình Jobs

1. **validate-tag**: Kiểm tra tag format và extract version
2. **build-and-test**: Build solution, chạy tests, tạo packages
3. **publish-packages**: Publish packages lên NuGet registry
4. **notify-success/notify-failure**: Gửi notification qua Lark

## Troubleshooting

### Package không được tạo
- Kiểm tra project có `<IsPackable>true</IsPackable>` trong .csproj
- Đảm bảo project không phải test project
- Xem logs của job "Build và Test NuGet Package"

### Publish thất bại
- Kiểm tra secret `OSP_NUGET_PACKAGE_PASSWORD` đã được cấu hình
- Verify NuGet registry URL và credentials
- Kiểm tra package version có conflict không

### Tag không trigger workflow
- Đảm bảo tag được tạo từ develop branch
- Kiểm tra tag format có hợp lệ không
- Xem trong Actions tab có workflow run không

### Tests thất bại
- Set `enable-tests: false` để skip tests tạm thời
- Fix test issues và re-run workflow
- Kiểm tra test projects có dependencies đúng không

## Ví dụ hoàn chỉnh

```bash
# 1. Chuẩn bị code trên develop
git checkout develop
git pull origin develop

# 2. Tạo tag
git tag v1.2.0
git push origin v1.2.0

# 3. Workflow sẽ:
# - Validate tag format: v1.2.0 ✅
# - Build solution với version 1.2.0
# - Chạy tất cả unit tests
# - Tạo NuGet packages (.nupkg + .snupkg)
# - Publish lên https://package.ospgroup.io.vn/repository/nuget-hosted/index.json
# - Gửi notification qua Lark
# - Upload artifacts để troubleshooting

# 4. Package có thể sử dụng:
# dotnet add package YourPackage --version 1.2.0
```

## Best Practices

🔹 **Luôn test trên develop** trước khi tạo tag
🔹 **Sử dụng semantic versioning** (1.0.0, 1.1.0, 2.0.0)
🔹 **Tạo tag từ develop branch** để đảm bảo stability
🔹 **Review packages artifacts** trước khi sử dụng
🔹 **Monitor Lark notifications** để biết kết quả ngay
🔹 **Sử dụng pre-release versions** (beta, alpha) cho testing

## Support

Nếu có vấn đề với workflow:
1. Kiểm tra logs trong Actions tab
2. Xem artifacts đã upload
3. Verify secrets configuration
4. Liên hệ DevOps team qua Lark