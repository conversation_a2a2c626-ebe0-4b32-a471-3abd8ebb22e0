# Reusable Java Library CI Build Workflow

## Tổng quan

Workflow CI có thể tái sử dụng để tự động build, test và kiểm tra các Java/Maven libraries khi có pull request hoặc push vào các branch chính.

## Tính năng chính

✅ **Tự động phát hiện thay đổi** - chỉ build khi có thay đổi code Java/Maven
✅ **Multi-project support** - hỗ trợ Maven multi-module projects
✅ **Flexible Java versions** - hỗ trợ Java 8, 11, 17, 21+
✅ **Maven caching** - cache dependencies để tăng tốc build
✅ **Unit testing** - tự động chạy tests với báo cáo
✅ **Package creation** - tạo JAR files
✅ **Lark notifications** - thông báo kết quả qua Lark
✅ **Build summary** - báo cáo chi tiết thời gian build

## Cách sử dụng

### C<PERSON>u hình cơ bản

```yaml
name: 'CI Build - Java Project'

on:
  pull_request:
    branches: [ main, develop ]
    types: [ opened, synchronize, reopened ]
  push:
    branches: [ main, develop ]

jobs:
  build:
    name: 'Build Java Library'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
    with:
      src-directory: '.'
      java-version: '21'
      java-distribution: 'temurin'
      enable-tests: true
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### Cấu hình nâng cao

```yaml
jobs:
  build:
    name: 'Build Java Library'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
    with:
      src-directory: 'backend'
      java-version: '17'
      java-distribution: 'temurin'
      build-configuration: 'Release'
      enable-tests: true
      additional-build-args: '--quiet --update-snapshots'
      force_build: false
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

## Tham số đầu vào (Inputs)

| Tham số | Bắt buộc | Mặc định | Mô tả |
|---------|----------|----------|-------|
| `src-directory` | Không | `.` | Thư mục chứa source code |
| `java-version` | Không | `21` | Phiên bản Java (8, 11, 17, 21, etc.) |
| `java-distribution` | Không | `temurin` | Phân phối Java (temurin, adopt, etc.) |
| `build-configuration` | Không | `Release` | Cấu hình build |
| `enable-tests` | Không | `true` | Có chạy unit tests không |
| `additional-build-args` | Không | `''` | Tham số bổ sung cho Maven |
| `force_build` | Không | `false` | Buộc build mà không kiểm tra thay đổi |
| `branch` | Không | `''` | Nhánh cần build (mặc định: nhánh hiện tại) |
| `lark-webhook-url` | Không | `''` | URL webhook Lark (có thể dùng thay cho secret) |

## Secrets

| Secret | Bắt buộc | Mô tả |
|--------|----------|-------|
| `LARK_WEBHOOK_URL` | Không | URL webhook Lark để gửi thông báo |

## Outputs

Workflow này không có outputs trực tiếp, nhưng sẽ:
- Tạo JAR files trong thư mục `target/`
- Gửi thông báo Lark (nếu được cấu hình)
- Hiển thị build summary với thời gian thực hiện

## Cấu trúc project được hỗ trợ

### Single Module Project
```
project/
├── pom.xml
├── src/
│   ├── main/java/
│   └── test/java/
└── target/
```

### Multi Module Project
```
project/
├── pom.xml (parent)
├── module1/
│   ├── pom.xml
│   └── src/
├── module2/
│   ├── pom.xml
│   └── src/
└── target/
```

## Ví dụ thực tế

### Ví dụ 1: Java 21 với Spring Boot
```yaml
name: 'CI Build - Spring Boot Service'

on:
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    name: 'Build Spring Boot Service'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
    with:
      src-directory: '.'
      java-version: '21'
      enable-tests: true
      additional-build-args: '-Dspring.profiles.active=test'
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### Ví dụ 2: Java 17 với Maven multi-module
```yaml
name: 'CI Build - Multi Module Project'

on:
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    name: 'Build Multi Module Project'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
    with:
      src-directory: '.'
      java-version: '17'
      java-distribution: 'temurin'
      enable-tests: true
      additional-build-args: '--also-make --threads 2'
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### Ví dụ 3: Tích hợp với change detection
```yaml
name: 'Smart CI Build'

on:
  pull_request:
    branches: [ main, develop ]

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      java-changed: ${{ steps.changes.outputs.java }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            java:
              - 'src/**'
              - '**/*.java'
              - '**/pom.xml'

  build:
    name: 'Build Java Library'
    needs: detect-changes
    if: needs.detect-changes.outputs.java-changed == 'true'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
    with:
      src-directory: '.'
      java-version: '21'
      enable-tests: true
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

## Troubleshooting

### Build thất bại
1. Kiểm tra Java version compatibility
2. Xem Maven dependencies có conflict không
3. Kiểm tra test failures trong logs

### Cache issues
- Workflow tự động cache Maven dependencies
- Nếu có vấn đề, có thể clear cache trong GitHub Actions settings

### Lark notifications không hoạt động
- Kiểm tra `LARK_WEBHOOK_URL` secret đã được cấu hình
- Verify webhook URL còn hoạt động

## Migration từ local workflows

Để migrate từ local reusable workflows:

1. **Xóa local workflow files**:
   ```bash
   rm -rf .github/workflows/reusable-*.yml
   rm -rf .github/actions/
   ```

2. **Cập nhật CI workflow**:
   ```yaml
   # Thay đổi từ:
   uses: ./.github/workflows/reusable-flexible-runner.yml
   
   # Thành:
   uses: ospgroupvn/k8s-deployment/.github/workflows/reuseable-java-lib-ci-build.yml@main
   ```

3. **Cập nhật parameters** theo bảng inputs ở trên

## Lưu ý quan trọng

- ⚠️ **Luôn sử dụng `@main`** để đảm bảo sử dụng version mới nhất
- ⚠️ **Test thoroughly** sau khi migrate từ local workflows
- ⚠️ **Backup** workflows cũ trước khi xóa
- ⚠️ **Repository secrets** cần được cấu hình đúng

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs chi tiết trong GitHub Actions
2. Tham khảo các ví dụ trong tài liệu này
3. Liên hệ team DevOps để được hỗ trợ
