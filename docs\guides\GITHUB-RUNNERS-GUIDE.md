# GitHub Runners Platform - Deployment Guide

Hướng dẫn triển khai và quản lý GitHub self-hosted runners trên Kubernetes sử dụng OSP custom runner image.

## Tổng quan kiến trúc

### Thành phần chính

```
github-runners-platform/
├── github-runners/                    # Helm chart chính
│   ├── Chart.yaml                     # Chart metadata
│   ├── values.yaml                    # Default values
│   ├── values-template.yaml           # Template cho repo mới
│   ├── values-k8s-deployment.yaml     # Values cho k8s-deployment
│   ├── templates/                     # Kubernetes templates
│   └── README.md                      # Chart documentation
├── github-runners-apps/               # ArgoCD applications
│   ├── TEMPLATE-runner.yaml           # Template cho app mới
│   └── k8s-deployment-runner.yaml     # App cho k8s-deployment
├── github-runners-manifests/          # Kubernetes manifests
│   ├── namespace.yaml                 # Namespace definition
│   └── dockerhub-secret.yaml          # Docker registry secret
├── scripts/
│   └── add-github-runner.sh           # Script thêm runner mới
├── github-runners-platform-apps.yaml  # App of Apps
└── github-runners-secrets-app.yaml    # Secrets application
```

### Docker Command Equivalent

Platform này tương đương với Docker command:

```bash
docker run -d \
  --name osp-runner \
  -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
  -e ACCESS_TOKEN="****************************************" \
  -e RUNNER_NAME="osp-custom-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0
```

## Triển khai ban đầu

### 1. Deploy secrets và platform

```bash
# Deploy secrets trước
kubectl apply -f github-runners-secrets-app.yaml

# Deploy platform (App of Apps)
kubectl apply -f github-runners-platform-apps.yaml
```

### 2. Kiểm tra trạng thái

```bash
# Kiểm tra ArgoCD applications
kubectl get applications -n bootstrap | grep runner

# Kiểm tra pods
kubectl get pods -n github-runners

# Kiểm tra logs
kubectl logs -f <pod-name> -n github-runners
```

## Thêm runner cho repository mới

### Cách 1: Sử dụng script tự động

```bash
# Thêm runner cho repository mới
./scripts/add-github-runner.sh ospgroupvn my-app 2 2 4Gi 10Gi

# Commit và push
git add .
git commit -m "feat: Add GitHub runner for ospgroupvn/my-app"
git push origin main
```

### Cách 2: Thủ công

1. **Tạo values file**:
   ```bash
   cp github-runners/values-template.yaml github-runners/values-my-app.yaml
   # Chỉnh sửa file theo repository
   ```

2. **Tạo ArgoCD application**:
   ```bash
   cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/my-app-runner.yaml
   # Chỉnh sửa file theo repository
   ```

3. **Commit và push**:
   ```bash
   git add github-runners/values-my-app.yaml github-runners-apps/my-app-runner.yaml
   git commit -m "feat: Add GitHub runner for my-app"
   git push origin main
   ```

## Tính năng nâng cao

### Multi-instance Support

- Mỗi repository có thể có nhiều replicas
- Mỗi pod có environment riêng biệt
- Working directory unique cho mỗi instance
- Anti-affinity để spread across nodes

### Environment Isolation

- Mỗi runner có PVC riêng
- Working directory unique: `/tmp/runner/work-${HOSTNAME}`
- Không có xung đột giữa các instances
- Docker builds isolated

### High Availability

- Pod Disruption Budget
- Anti-affinity rules
- Health checks và auto-restart
- Persistent storage cho build cache

### Security

- Privileged mode chỉ khi cần thiết
- ServiceAccount với permissions tối thiểu
- Network policies (optional)
- Secret management qua Kubernetes

## Monitoring và Troubleshooting

### Kiểm tra trạng thái

```bash
# Xem tất cả runners
kubectl get pods -n github-runners -l app.kubernetes.io/name=github-runners

# Xem logs của runner cụ thể
kubectl logs -f <pod-name> -n github-runners

# Exec vào pod để debug
kubectl exec -it <pod-name> -n github-runners -- /bin/bash
```

### Test Docker functionality

```bash
# Kiểm tra Docker socket
kubectl exec -it <pod-name> -n github-runners -- /check-docker.sh

# Test Docker commands
kubectl exec -it <pod-name> -n github-runners -- docker info
kubectl exec -it <pod-name> -n github-runners -- docker run --rm hello-world
```

### Common Issues

1. **Runner registration fails**:
   - Kiểm tra ACCESS_TOKEN
   - Kiểm tra REPO_URL
   - Kiểm tra network connectivity

2. **Docker commands fail**:
   - Kiểm tra Docker socket mount
   - Kiểm tra permissions
   - Kiểm tra privileged mode

3. **Pod crashes**:
   - Kiểm tra resource limits
   - Kiểm tra storage availability
   - Xem logs để debug

## Best Practices

### Resource Planning

| Repository Size | CPU Request | CPU Limit | Memory Request | Memory Limit | Storage |
|----------------|-------------|-----------|----------------|--------------|---------|
| Small          | 500m        | 2         | 1Gi            | 4Gi          | 5Gi     |
| Medium         | 1           | 4         | 2Gi            | 8Gi          | 10Gi    |
| Large          | 2           | 8         | 4Gi            | 16Gi         | 20Gi    |

### Security

- Regular rotation của GitHub tokens
- Sử dụng dedicated nodes cho runners
- Cấu hình Network Policies
- Monitor resource usage

### Scaling

- Bắt đầu với 1 replica
- Tăng dần theo nhu cầu
- Sử dụng HPA cho auto-scaling
- Cân nhắc spot instances cho cost optimization

## Maintenance

### Update runner image

1. Build và push image mới
2. Update `image.tag` trong values files
3. Commit và push
4. ArgoCD sẽ tự động rolling update

### Cleanup unused runners

```bash
# Xóa runner không sử dụng
kubectl delete application <runner-name> -n bootstrap

# Cleanup PVCs nếu cần
kubectl delete pvc -n github-runners -l app.kubernetes.io/instance=<runner-name>
```
