# GitHub Runners Guide

## Tổng quan

Hướng dẫn triển khai và quản lý GitHub self-hosted runners trên Kubernetes sử dụng OSP custom runner image.

## Kiến trúc

### Thành phần chính

```
github-runners-platform/
├── github-runners/                    # Helm chart chính
│   ├── Chart.yaml                     # Chart metadata
│   ├── values.yaml                    # Default values
│   ├── values-template.yaml           # Template cho repo mới
│   ├── values-k8s-deployment.yaml     # Values cho k8s-deployment
│   ├── templates/                     # Kubernetes templates
│   └── README.md                      # Chart documentation
├── github-runners-apps/               # ArgoCD applications
│   ├── TEMPLATE-runner.yaml           # Template cho app mới
│   └── k8s-deployment-runner.yaml     # App cho k8s-deployment
├── github-runners-manifests/          # Kubernetes manifests
│   ├── namespace.yaml                 # Namespace definition
│   └── dockerhub-secret.yaml          # Docker registry secret
├── scripts/
│   └── add-github-runner.sh           # Script thêm runner mới
├── github-runners-platform-apps.yaml  # App of Apps
└── github-runners-secrets-app.yaml    # Secrets application
```

### Docker Command Equivalent

Platform này tương đương với Docker command:

```bash
docker run -d \
  --name osp-runner \
  -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
  -e ACCESS_TOKEN="****************************************" \
  -e RUNNER_NAME="osp-custom-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0
```

## Triển khai ban đầu

### 1. Commit và push code lên repository

```bash
git add .
git commit -m "feat: Add GitHub runners infrastructure with ArgoCD GitOps"
git push origin main
```

### 2. Cập nhật platform-apps để bao gồm GitHub runners

Thêm github-runners-app vào platform applications để ArgoCD tự động deploy:

```bash
# Thêm vào platform/platform-apps.yaml hoặc tạo riêng platform app
kubectl apply -f platform/github-runners-app.yaml
```

### 3. Deploy secrets và platform

```bash
# Deploy secrets trước
kubectl apply -f github-runners-secrets-app.yaml

# Deploy platform (App of Apps)
kubectl apply -f github-runners-platform-apps.yaml
```

### 4. Kiểm tra deployment

```bash
# Kiểm tra ArgoCD applications
export KUBECONFIG=$(pwd)/.kube/config
kubectl get applications -n bootstrap

# Kiểm tra namespace github-runners
kubectl get namespace github-runners

# Kiểm tra pods trong namespace github-runners
kubectl get pods -n github-runners

# Kiểm tra logs của runner
kubectl logs -n github-runners -l app.kubernetes.io/name=github-runners
```

### 5. Xác minh runner hoạt động

1. Truy cập GitHub repository: https://github.com/ospgroupvn/k8s-deployment
2. Vào Settings > Actions > Runners
3. Kiểm tra thấy 2 self-hosted runners với tên "k8s-deployment-runner-xxxx"

## Thêm runner cho repository mới

### Cách 1: Sử dụng script tự động

```bash
# Thêm runner cho repository mới
./scripts/add-github-runner.sh ospgroupvn my-app 2 2 4Gi 10Gi

# Commit và push
git add .
git commit -m "feat: Add GitHub runner for ospgroupvn/my-app"
git push origin main
```

### Cách 2: Thủ công

#### 1. Tạo values file:
```bash
cp github-runners/values-template.yaml github-runners/values-example-app.yaml
```

#### 2. Chỉnh sửa values-example-app.yaml:
```yaml
repository:
  url: "https://github.com/ospgroupvn/example-app"

runner:
  name: "example-app-runner"
  labels: "self-hosted,linux,x64,docker,nodejs"  # Thêm labels phù hợp
```

#### 3. Tạo ArgoCD Application:
```bash
cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/example-app-runner.yaml
```

#### 4. Chỉnh sửa example-app-runner.yaml:
```yaml
metadata:
  name: example-app-runner
spec:
  source:
    helm:
      valueFiles:
        - values-example-app.yaml
```

#### 5. Commit và push:
```bash
git add github-runners/values-example-app.yaml
git add github-runners-apps/example-app-runner.yaml  
git commit -m "feat: Add GitHub runner for example-app"
git push origin main
```

ArgoCD sẽ tự động sync và tạo runner mới trong vài phút.

## Tính năng nâng cao

### Multi-instance Support

- Mỗi repository có thể có nhiều replicas
- Mỗi pod có environment riêng biệt
- Working directory unique cho mỗi instance
- Anti-affinity để spread across nodes

### Environment Isolation

- Mỗi runner có PVC riêng
- Working directory unique: `/tmp/runner/work-${HOSTNAME}`
- Không có xung đột giữa các instances
- Docker builds isolated

### High Availability

- Pod Disruption Budget
- Anti-affinity rules
- Health checks và auto-restart
- Persistent storage cho build cache

### Security

- Privileged mode chỉ khi cần thiết
- ServiceAccount với permissions tối thiểu
- Network policies (optional)
- Secret management qua Kubernetes

## Resource Planning

| Repository Size | CPU Request | CPU Limit | Memory Request | Memory Limit | Storage |
| --------------- | ----------- | --------- | -------------- | ------------ | ------- |
| Small           | 500m        | 2         | 1Gi            | 4Gi          | 5Gi     |
| Medium          | 1           | 4         | 2Gi            | 8Gi          | 10Gi    |
| Large           | 2           | 8         | 4Gi            | 16Gi         | 20Gi    |

## Monitoring và Troubleshooting

### Kiểm tra trạng thái

```bash
# Xem tất cả runners
kubectl get pods -n github-runners -l app.kubernetes.io/name=github-runners

# Xem logs của runner cụ thể
kubectl logs -f <pod-name> -n github-runners

# Exec vào pod để debug
kubectl exec -it <pod-name> -n github-runners -- /bin/bash
```

### Test Docker functionality

```bash
# Kiểm tra Docker socket
kubectl exec -it <pod-name> -n github-runners -- /check-docker.sh

# Test Docker commands
kubectl exec -it <pod-name> -n github-runners -- docker info
kubectl exec -it <pod-name> -n github-runners -- docker run --rm hello-world
```

### Common Issues

1. **Runner registration fails**:
   - Kiểm tra ACCESS_TOKEN
   - Kiểm tra REPO_URL
   - Kiểm tra network connectivity

2. **Docker commands fail**:
   - Kiểm tra Docker socket mount
   - Kiểm tra permissions
   - Kiểm tra privileged mode

3. **Pod crashes**:
   - Kiểm tra resource limits
   - Kiểm tra storage availability
   - Xem logs để debug

### Troubleshooting Commands

#### Kiểm tra logs ArgoCD
```bash
kubectl logs -n bootstrap -l app.kubernetes.io/name=argocd-application-controller
```

#### Kiểm tra runner registration
```bash
kubectl logs -n github-runners -l app.kubernetes.io/name=github-runners -f
```

#### Restart runners nếu cần
```bash
kubectl rollout restart deployment -n github-runners
```

## Best Practices

### Security

- Regular rotation của GitHub tokens
- Sử dụng dedicated nodes cho runners
- Cấu hình Network Policies
- Monitor resource usage

### Scaling

- Bắt đầu với 1 replica
- Tăng dần theo nhu cầu
- Sử dụng HPA cho auto-scaling
- Cân nhắc spot instances cho cost optimization

## Maintenance

### Update runner image

1. Build và push image mới
2. Update `image.tag` trong values files
3. Commit và push
4. ArgoCD sẽ tự động rolling update

### Cleanup unused runners

```bash
# Xóa runner không sử dụng
kubectl delete application <runner-name> -n bootstrap

# Cleanup PVCs nếu cần
kubectl delete pvc -n github-runners -l app.kubernetes.io/instance=<runner-name>
```

## Templates và Values

### Values Template Structure

```yaml
# github-runners/values-template.yaml
replicaCount: 1

image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  pullPolicy: IfNotPresent
  tag: "1.0"

repository:
  url: "https://github.com/ospgroupvn/REPO_NAME"

runner:
  name: "REPO_NAME-runner"
  group: "default"
  labels: "self-hosted,linux,x64,docker"
  
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

persistence:
  enabled: true
  size: 10Gi
  storageClass: "local-path"

serviceAccount:
  create: true
  annotations: {}
  name: ""

nodeSelector: {}
tolerations: []
affinity: {}
```

### ArgoCD Application Template

```yaml
# github-runners-apps/TEMPLATE-runner.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: REPO_NAME-runner
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: github-runners
    targetRevision: main
    helm:
      valueFiles:
        - values-REPO_NAME.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: github-runners
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
```

## Scripts tự động hóa

### Script thêm runner mới

```bash
#!/bin/bash
# scripts/add-github-runner.sh

set -e

OWNER=$1
REPO=$2
REPLICAS=${3:-1}
CPU_REQUEST=${4:-500m}
CPU_LIMIT=${5:-2}
MEMORY_REQUEST=${6:-1Gi}
MEMORY_LIMIT=${7:-4Gi}
STORAGE=${8:-10Gi}

if [ -z "$OWNER" ] || [ -z "$REPO" ]; then
  echo "Usage: $0 <owner> <repo> [replicas] [cpu-request] [cpu-limit] [memory-request] [memory-limit] [storage]"
  exit 1
fi

RUNNER_NAME="${REPO}-runner"
VALUES_FILE="github-runners/values-${REPO}.yaml"
APP_FILE="github-runners-apps/${REPO}-runner.yaml"

echo "Creating GitHub runner for ${OWNER}/${REPO}"

# Create values file
cp github-runners/values-template.yaml $VALUES_FILE

# Update values file
sed -i.bak "s|REPO_NAME|${REPO}|g" $VALUES_FILE
sed -i.bak "s|https://github.com/ospgroupvn/REPO_NAME|https://github.com/${OWNER}/${REPO}|g" $VALUES_FILE
sed -i.bak "s|replicaCount: 1|replicaCount: ${REPLICAS}|g" $VALUES_FILE
sed -i.bak "s|cpu: 500m|cpu: ${CPU_REQUEST}|g" $VALUES_FILE
sed -i.bak "s|cpu: 2|cpu: ${CPU_LIMIT}|g" $VALUES_FILE
sed -i.bak "s|memory: 1Gi|memory: ${MEMORY_REQUEST}|g" $VALUES_FILE
sed -i.bak "s|memory: 4Gi|memory: ${MEMORY_LIMIT}|g" $VALUES_FILE
sed -i.bak "s|size: 10Gi|size: ${STORAGE}|g" $VALUES_FILE
rm $VALUES_FILE.bak

# Create ArgoCD application
cp github-runners-apps/TEMPLATE-runner.yaml $APP_FILE

# Update ArgoCD application file
sed -i.bak "s|REPO_NAME|${REPO}|g" $APP_FILE
rm $APP_FILE.bak

echo "Created files:"
echo "- $VALUES_FILE"
echo "- $APP_FILE"
echo ""
echo "Next steps:"
echo "1. Review and modify the files if needed"
echo "2. Commit and push the changes"
echo "3. ArgoCD will automatically deploy the runner"
```

## Kết luận

GitHub Runners platform cung cấp một giải pháp linh hoạt và có khả năng mở rộng để chạy CI/CD pipelines trên Kubernetes. Với ArgoCD GitOps integration, việc quản lý và triển khai runners trở nên đơn giản và nhất quán.

Các tính năng chính:
- ✅ Scalable Repository Support
- ✅ Multi-instance Support
- ✅ Docker-in-Docker
- ✅ Persistent Storage
- ✅ High Availability
- ✅ GitOps Ready