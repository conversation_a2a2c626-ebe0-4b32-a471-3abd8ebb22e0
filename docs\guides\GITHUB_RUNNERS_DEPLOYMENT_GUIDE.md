# Hướng dẫn triển khai GitHub Runners

## Cách triển khai GitHub Runners cho k8s-deployment

### 1. Commit và push code lên repository

```bash
git add .
git commit -m "feat: Add GitHub runners infrastructure with ArgoCD GitOps"
git push origin main
```

### 2. Cập nhật platform-apps để bao gồm GitHub runners

Thêm github-runners-app vào platform applications để ArgoCD tự động deploy:

```bash
# Thêm vào platform/platform-apps.yaml hoặc tạo riêng platform app
kubectl apply -f platform/github-runners-app.yaml
```

### 3. Kiểm tra deployment

```bash
# Kiểm tra ArgoCD applications
export KUBECONFIG=$(pwd)/.kube/config
kubectl get applications -n bootstrap

# Kiểm tra namespace github-runners
kubectl get namespace github-runners

# Kiểm tra pods trong namespace github-runners
kubectl get pods -n github-runners

# <PERSON><PERSON><PERSON> tra logs của runner
kubectl logs -n github-runners -l app.kubernetes.io/name=github-runners
```

### 4. Xác minh runner hoạt động

1. Truy cập GitHub repository: https://github.com/ospgroupvn/k8s-deployment
2. Vào Settings > Actions > Runners
3. Kiểm tra thấy 2 self-hosted runners với tên "k8s-deployment-runner-xxxx"

## Cách thêm runner cho repository mới

### Ví dụ: Thêm runner cho repository "example-app"

1. **Tạo values file:**
```bash
cp github-runners/values-template.yaml github-runners/values-example-app.yaml
```

2. **Chỉnh sửa values-example-app.yaml:**
```yaml
repository:
  url: "https://github.com/ospgroupvn/example-app"

runner:
  name: "example-app-runner"
  labels: "self-hosted,linux,x64,docker,nodejs"  # Thêm labels phù hợp
```

3. **Tạo ArgoCD Application:**
```bash
cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/example-app-runner.yaml
```

4. **Chỉnh sửa example-app-runner.yaml:**
```yaml
metadata:
  name: example-app-runner
spec:
  source:
    helm:
      valueFiles:
        - values-example-app.yaml
```

5. **Commit và push:**
```bash
git add github-runners/values-example-app.yaml
git add github-runners-apps/example-app-runner.yaml  
git commit -m "feat: Add GitHub runner for example-app"
git push origin main
```

ArgoCD sẽ tự động sync và tạo runner mới trong vài phút.

## Troubleshooting

### Kiểm tra logs ArgoCD
```bash
kubectl logs -n bootstrap -l app.kubernetes.io/name=argocd-application-controller
```

### Kiểm tra runner registration
```bash
kubectl logs -n github-runners -l app.kubernetes.io/name=github-runners -f
```

### Restart runners nếu cần
```bash
kubectl rollout restart deployment -n github-runners
```