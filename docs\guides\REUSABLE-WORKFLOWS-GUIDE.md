# OSP Reusable GitHub Workflows Guide

## 📋 Tổng quan

Repository này cung cấp các reusable workflows để các repos khác trong OSP Group có thể tái sử dụng mà không cần duplicate code. Điều này giúp:

- ✅ **Tái sử dụng code**: Không cần viết lại workflows tương tự
- ✅ **Quản lý tập trung**: Cập nhật workflows ở một nơi, áp dụng cho tất cả
- ✅ **Consistency**: Đ<PERSON>m bảo tất cả projects đều follow cùng standard
- ✅ **Auto versioning**: Tự động tăng version cho Docker images
- ✅ **Lark notifications**: Thông báo tự động vào Lark

## 🛠️ Available Reusable Workflows

### 1. Docker Build và Push (`reusable-docker-build.yml`)

**<PERSON><PERSON> tả**: Build Docker image với auto-increment version, push lên registry và gửi notification

**Features:**
- ✅ Auto-increment version từ git tags
- ✅ Build Docker image với multiple tags (version, latest, branch)
- ✅ Push lên Harbor registry
- ✅ Optional testing và security scanning
- ✅ Lark notifications cho success/failure
- ✅ Auto tạo git tags
- ✅ Docker cleanup

### 2. Lark Notification (`reusable-lark-notification.yml`)

**Mô tả**: Gửi thông báo vào Lark với format chuẩn

**Features:**
- ✅ Rich message với emojis và colors
- ✅ Thông tin chi tiết về commit, branch, author
- ✅ Links trở lại GitHub workflow
- ✅ Support custom fields

### 3. Lark Action (`lark-notification/action.yml`)

**Mô tả**: Composite action để gửi Lark notification

## 🚀 Cách sử dụng cho repos khác

### Bước 1: Setup Repository Secrets

Trước tiên, repository của bạn cần có các secrets sau:

```bash
# Required secrets
OSP_REGISTRY=dockerhub.ospgroup.vn
OSP_IMAGE_OWNER=osp-public  # hoặc namespace phù hợp

# Optional (nếu cần push image)
OSP_REGISTRY_USERNAME=your-registry-username
OSP_REGISTRY_PASSWORD=your-registry-password

# Optional (nếu cần Lark notifications)
LARK_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
```

### Bước 2: Tạo Workflow File

Tạo file `.github/workflows/docker-build.yml` trong repository của bạn:

#### Ví dụ 1: Basic Docker Build

```yaml
name: Build và Push Docker Image

on:
  push:
    paths:
      - 'Dockerfile'
      - 'src/**'
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean

jobs:
  build-docker-image:
    name: 🐳 Build Docker Image
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: '.'  # Đường dẫn tới thư mục chứa Dockerfile
      image-name: 'my-app'  # Tên Docker image
      force-rebuild: ${{ inputs.force_rebuild || false }}
      enable-test: false
      enable-security-scan: false
      push-to-registry: ${{ github.event_name != 'pull_request' }}
      create-git-tag: true
      notification-title: "My App Docker Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

#### Ví dụ 2: Advanced với Build Args và Testing

```yaml
name: Build Next.js App

on:
  push:
    paths:
      - 'Dockerfile'
      - 'src/**'
      - 'package.json'
    branches:
      - main
  workflow_dispatch:
    inputs:
      node_version:
        description: 'Node.js version'
        required: false
        default: '18'
        type: string

jobs:
  build-nextjs-app:
    name: 🚀 Build Next.js App
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: '.'
      image-name: 'nextjs-app'
      build-args: '{"NODE_VERSION": "${{ inputs.node_version || '18' }}", "BUILD_ENV": "production"}'
      enable-test: true
      test-port: '3000'
      test-command: 'curl -f http://localhost:3000/api/health'
      enable-security-scan: true
      push-to-registry: true
      create-git-tag: true
      notification-title: "Next.js App Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

#### Ví dụ 3: Monorepo với Multiple Images

```yaml
name: Build Monorepo Services

on:
  push:
    paths:
      - 'services/**'
    branches:
      - main

jobs:
  build-frontend:
    name: 🎨 Build Frontend
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: 'services/frontend'
      image-name: 'frontend-service'
      notification-title: "Frontend Service Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}

  build-backend:
    name: ⚙️ Build Backend
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: 'services/backend'
      image-name: 'backend-service'
      build-args: '{"JAVA_VERSION": "17"}'
      enable-test: true
      test-port: '8080'
      notification-title: "Backend Service Build"
    secrets:
      OSP_REGISTRY: ${{ secrets.OSP_REGISTRY }}
      OSP_IMAGE_OWNER: ${{ secrets.OSP_IMAGE_OWNER }}
      OSP_REGISTRY_USERNAME: ${{ secrets.OSP_REGISTRY_USERNAME }}
      OSP_REGISTRY_PASSWORD: ${{ secrets.OSP_REGISTRY_PASSWORD }}
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

### Bước 3: Lark Notification Standalone

Nếu chỉ muốn gửi notification mà không build Docker:

```yaml
name: Send Deployment Notification

on:
  deployment_status:

jobs:
  notify-deployment:
    name: 📢 Notify Deployment
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-lark-notification.yml@main
    with:
      title: "Production Deployment"
      message: "Application đã được deploy thành công lên production!"
      status: "success"
      custom-fields: '{"Environment": "Production", "Version": "v1.2.3"}'
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
```

## ⚙️ Chi tiết Parameters

### Reusable Docker Build Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `dockerfile-path` | string | ✅ | - | Đường dẫn tới thư mục chứa Dockerfile |
| `image-name` | string | ✅ | - | Tên Docker image |
| `build-args` | string | ❌ | `{}` | Build arguments dạng JSON |
| `runner-version` | string | ❌ | `2.328.0` | GitHub Actions Runner version |
| `force-rebuild` | boolean | ❌ | `false` | Force rebuild without cache |
| `enable-test` | boolean | ❌ | `false` | Enable testing container |
| `test-command` | string | ❌ | `''` | Command để test container |
| `test-port` | string | ❌ | `''` | Port để health check |
| `enable-security-scan` | boolean | ❌ | `false` | Enable Trivy security scan |
| `push-to-registry` | boolean | ❌ | `true` | Push image lên registry |
| `create-git-tag` | boolean | ❌ | `true` | Tạo git tag cho version |
| `notification-title` | string | ❌ | `Docker Build` | Title cho Lark notification |

### Required Secrets

| Secret | Description |
|--------|-------------|
| `OSP_REGISTRY` | URL của Docker registry (ví dụ: dockerhub.ospgroup.vn) |
| `OSP_IMAGE_OWNER` | Namespace/owner của image (ví dụ: osp-public) |
| `OSP_REGISTRY_USERNAME` | Username để login registry (optional) |
| `OSP_REGISTRY_PASSWORD` | Password để login registry (optional) |
| `LARK_WEBHOOK_URL` | Lark webhook URL để gửi notification (optional) |

## 🎯 Auto Versioning

Workflow sẽ tự động:

1. **Đọc git tag** hiện tại (ví dụ: `v1.2.3`)
2. **Tăng patch version** (thành `v1.2.4`)
3. **Tạo multiple image tags**:
   - `image:1.2.4` (version tag)
   - `image:latest` (latest tag)
   - `image:main-abc1234-20240321-143052` (branch tag)
4. **Tạo git tag mới** (nếu `create-git-tag: true`)

## 📢 Lark Notifications

Notification sẽ bao gồm:

- ✅ **Status** với emoji và màu sắc
- 📦 **Repository** và **branch** info
- 📝 **Commit** details với link
- 👤 **Author** information
- ⏱️ **Build duration** (nếu có)
- 🔗 **Link** trở lại GitHub workflow

## 🔧 Troubleshooting

### Lỗi thường gặp:

#### 1. "Repository not found" hoặc "Workflow not found"

**Nguyên nhân**: Repository `ospgroupvn/k8s-deployment` không public hoặc không có quyền access

**Giải pháp**:
- Đảm bảo repository `ospgroupvn/k8s-deployment` là public
- Hoặc thêm repository vào organization với proper permissions

#### 2. "Secret not found"

**Nguyên nhân**: Thiếu secrets cần thiết

**Giải pháp**:
- Kiểm tra repository secrets: Settings → Secrets and variables → Actions
- Thêm các secrets cần thiết như `OSP_REGISTRY`, `OSP_IMAGE_OWNER`

#### 3. "Docker login failed"

**Nguyên nhân**: Sai thông tin đăng nhập registry

**Giải pháp**:
- Kiểm tra `OSP_REGISTRY_USERNAME` và `OSP_REGISTRY_PASSWORD`
- Đảm bảo credentials có quyền push image

#### 4. "Git tag already exists"

**Nguyên nhân**: Version tag đã tồn tại

**Giải pháp**:
- Workflow sẽ tự động skip nếu tag đã tồn tại
- Hoặc manually tăng version trong git tag

### Debug Steps:

1. **Kiểm tra workflow logs** trong GitHub Actions
2. **Verify secrets** trong repository settings
3. **Test Docker build locally** với cùng parameters
4. **Check registry permissions**

## 📝 Best Practices

### 1. **Branch Strategy**
```yaml
on:
  push:
    branches:
      - main      # Build và push lên registry
      - develop   # Build và push lên registry
  pull_request:
    branches:
      - main      # Chỉ build, không push
```

### 2. **Path Filters**
```yaml
on:
  push:
    paths:
      - 'Dockerfile'
      - 'src/**'
      - 'package.json'
      - '.dockerignore'
```

### 3. **Environment-Specific Builds**
```yaml
jobs:
  build-staging:
    if: github.ref == 'refs/heads/develop'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      build-args: '{"NODE_ENV": "staging"}'

  build-production:
    if: github.ref == 'refs/heads/main'
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      build-args: '{"NODE_ENV": "production"}'
      enable-security-scan: true
```

### 4. **Multi-Stage Dependencies**
```yaml
jobs:
  build-base:
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: './docker/base'
      image-name: 'base-image'

  build-app:
    needs: build-base
    uses: ospgroupvn/k8s-deployment/.github/workflows/reusable-docker-build.yml@main
    with:
      dockerfile-path: './docker/app'
      image-name: 'app-image'
      build-args: '{"BASE_IMAGE": "${{ needs.build-base.outputs.image-tag }}"}'
```

## 📚 Examples Repository

Xem thêm examples trong repository này:
- [OSP Custom Runner Build](.github/workflows/osp-custom-runner-build.yml)
- [SonarQube Build](.github/workflows/sonarqube-docker-build.yml)

## 🤝 Contributing

Để cải thiện reusable workflows:

1. **Fork** repository này
2. **Tạo feature branch**: `git checkout -b feature/improve-workflow`
3. **Commit changes**: `git commit -am 'feat: improve workflow performance'`
4. **Push branch**: `git push origin feature/improve-workflow`
5. **Tạo Pull Request**

## 📞 Support

Nếu có vấn đề, vui lòng:

1. **Kiểm tra** [Troubleshooting](#-troubleshooting) section
2. **Tạo issue** trong repository này với label `workflow-support`
3. **Liên hệ** DevOps team qua Lark

---

## 📋 Template Checklist

Khi setup một repository mới để sử dụng reusable workflows:

- [ ] Thêm required secrets vào repository
- [ ] Tạo `.github/workflows/docker-build.yml` file
- [ ] Test workflow với một commit đơn giản
- [ ] Kiểm tra Lark notification hoạt động
- [ ] Verify image được push lên registry
- [ ] Check git tags được tạo tự động

**🎉 Done! Repository của bạn đã sẵn sàng sử dụng OSP reusable workflows!**