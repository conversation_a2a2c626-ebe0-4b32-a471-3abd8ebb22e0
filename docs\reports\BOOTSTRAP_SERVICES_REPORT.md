# Bootstrap Services Report

B<PERSON>o cáo tổng hợp về các dịch vụ nền tảng đã được cài đặt trong namespace `bootstrap`.

## Tổng quan

Các dịch vụ sau đã được cài đặt và xác minh hoạt động:

1. **HashiCorp Vault** ✅ - Quản lý secrets và bảo mật
2. **Traefik Gateway** ✅ - Kubernetes Gateway API và Load Balancer
3. **ArgoCD** ✅ - GitOps Continuous Deployment

## Trạng thái xác minh (Ngày: 2025-09-03)

**Tất cả pods đang chạy ổn định:**
- ✅ vault-bootstrap-0: 1/1 Running (0 restarts)
- ✅ traefik-bootstrap-xxx: 2/2 Running (0 restarts)
- ✅ argocd-bootstrap-xxx: 5/5 Running (0 restarts)

**Kết nối đã được test:**
- ✅ Vault: Initialized=true, Sealed=false
- ✅ ArgoCD: HTTP 200 response
- ✅ Traefik: Gateway API hoạt động, 2 HTTPRoute attached

## Chi tiết dịch vụ

### 1. HashiCorp Vault

**Trạng thái**: ✅ Đã cài đặt và chạy
**Namespace**: `bootstrap`
**Release Name**: `vault-bootstrap`

#### Thông tin truy cập:
- **Service**: `vault-bootstrap:8200`
- **Port Forward**: `kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200`
- **URL**: http://localhost:8200
- **Root Token**: `bootstrap-root-token`
- **Mode**: Development (in-memory storage)

#### Cấu hình quan trọng:
- Dev mode được bật (chỉ dành cho môi trường phát triển)
- UI được kích hoạt
- TLS bị vô hiệu hóa
- Storage: In-memory (dữ liệu sẽ mất khi restart)

#### Lệnh hữu ích:
```bash
# Kiểm tra trạng thái Vault
kubectl exec -n bootstrap vault-bootstrap-0 -- vault status

# Truy cập Vault CLI
kubectl exec -it -n bootstrap vault-bootstrap-0 -- vault auth -method=token token=bootstrap-root-token
```

### 2. Traefik Gateway

**Trạng thái**: ✅ Đã cài đặt và chạy ổn định
**Namespace**: `bootstrap`
**Release Name**: `traefik-bootstrap`

#### Thông tin truy cập LAN:
- **LoadBalancer Service**: `traefik-bootstrap` (IP: ************* - MetalLB)
- **HTTP Port**: 80
- **HTTPS Port**: 443
- **Gateway IP**: ************* (được cấp phát bởi MetalLB)

#### Truy cập qua LoadBalancer:
```bash
# Vault UI
curl -H "Host: vault.local" http://*************

# ArgoCD UI
curl -H "Host: argocd.local" http://*************
```

#### Cấu hình hosts file:
```
************* vault.local
************* argocd.local
************* traefik.local
```

#### Cấu hình quan trọng:
- ✅ Kubernetes Gateway API được kích hoạt
- ✅ Gateway Class: traefik (Accepted & Programmed)
- ✅ HTTPRoute: 5 routes attached (vault.local, argocd.local, harbor.local, minio.local, signoz.local)
- ✅ 2 replicas cho high availability
- ✅ LoadBalancer IP: ************* (MetalLB assigned)
- ✅ Cross-namespace HTTPRoute support enabled

#### Lệnh hữu ích:
```bash
# Kiểm tra Gateway và HTTPRoute
kubectl get gateway -n bootstrap
kubectl get httproute -n bootstrap

# Kiểm tra Traefik pods
kubectl get pods -n bootstrap -l app.kubernetes.io/name=traefik

# Test truy cập qua LoadBalancer
curl -H "Host: vault.local" http://*************
curl -H "Host: argocd.local" http://*************
```

### 3. ArgoCD

**Trạng thái**: ✅ Đã cài đặt và chạy
**Namespace**: `bootstrap`
**Release Name**: `argocd-bootstrap`

#### Thông tin truy cập:
- **Service**: `argocd-bootstrap-server:80`
- **Port Forward**: `kubectl port-forward svc/argocd-bootstrap-server -n bootstrap 8080:80`
- **URL**: http://localhost:8080
- **Username**: `admin`
- **Password**: `admin123`

#### Cấu hình quan trọng:
- Insecure mode được bật (HTTP thay vì HTTPS)
- Admin user được kích hoạt
- ApplicationSet controller được kích hoạt
- Redis internal được sử dụng
- RBAC được kích hoạt

#### Lệnh hữu ích:
```bash
# Kiểm tra ArgoCD pods
kubectl get pods -n bootstrap -l app.kubernetes.io/part-of=argocd

# Truy cập ArgoCD CLI
kubectl exec -it -n bootstrap deployment/argocd-bootstrap-server -- argocd login localhost:8080 --username admin --password admin123 --insecure

# Kiểm tra applications
kubectl get applications -A
```

## Hướng dẫn truy cập từ LAN


### Cách 2: Port Forwarding (cho testing local)

```bash
# Port forward Vault
kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200

# Port forward ArgoCD  
kubectl port-forward svc/argocd-bootstrap-server -n bootstrap 8080:80

# Port forward Harbor
kubectl port-forward svc/harbor -n platform-services 8081:80

# Port forward MinIO console
kubectl port-forward svc/minio-console -n platform-services 8082:9090

# Port forward SigNoz
kubectl port-forward svc/signoz -n platform-services 8083:8080
```

## Trạng thái tổng thể

```bash
# Kiểm tra tất cả pods trong namespace bootstrap
kubectl get pods -n bootstrap

# Kiểm tra Gateway API resources
kubectl get gateway,httproute -n bootstrap

# Kiểm tra tất cả services
kubectl get svc -n bootstrap

# Kiểm tra Helm releases
helm list -n bootstrap
```

## Troubleshooting

### Vault Issues

**Vấn đề**: Pod không khởi động
```bash
# Kiểm tra logs
kubectl logs -n bootstrap vault-bootstrap-0

# Kiểm tra events
kubectl describe pod -n bootstrap vault-bootstrap-0
```

**Vấn đề**: Không thể truy cập UI
```bash
# Kiểm tra service
kubectl get svc -n bootstrap vault-bootstrap

# Port forward
kubectl port-forward svc/vault-bootstrap -n bootstrap 8200:8200
```

### Traefik Issues

**Vấn đề**: LoadBalancer pending
- Cần cài đặt MetalLB hoặc cloud load balancer
- Tạm thời có thể sử dụng NodePort hoặc port-forward

**Vấn đề**: Gateway không hoạt động
```bash
# Kiểm tra Gateway Class
kubectl get gatewayclass

# Kiểm tra CRDs
kubectl get crd | grep gateway
```

### ArgoCD Issues

**Vấn đề**: Không thể đăng nhập
- Username: `admin`
- Password: `admin123`
- Đảm bảo sử dụng HTTP (insecure mode)

**Vấn đề**: Applications không sync
```bash
# Kiểm tra ArgoCD server logs
kubectl logs -n bootstrap deployment/argocd-bootstrap-server

# Kiểm tra application controller logs
kubectl logs -n bootstrap statefulset/argocd-bootstrap-application-controller
```

## Test kết nối đã thực hiện

**Ngày test**: 2025-09-03 22:00 UTC+7

```bash
# Test Vault status
kubectl exec -n bootstrap vault-bootstrap-0 -- vault status
# ✅ Result: Initialized=true, Sealed=false

# Test ArgoCD HTTP response
curl -H "Host: argocd.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 200

# Test Vault HTTP response
curl -H "Host: vault.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 307 (redirect, normal behavior)

# Test Harbor HTTP response
curl -H "Host: harbor.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 200

# Test MinIO Console HTTP response
curl -H "Host: minio.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 200

# Test SigNoz HTTP response
curl -H "Host: signoz.local" http://************* -s -o /dev/null -w "%{http_code}"
# ✅ Result: 200

# Test Gateway API
kubectl get gateway traefik-gateway -n bootstrap
# ✅ Result: Accepted=True, Programmed=True, AttachedRoutes=5

# Test HTTPRoute status
kubectl get httproutes -A
# ✅ Result: 6 HTTPRoutes total (2 in bootstrap, 3 in platform-services, 1 in default)
```

## Platform Services (Namespace: platform-services)

Các dịch vụ nền tảng đã được triển khai và có thể truy cập qua Traefik Gateway:

### 4. Harbor Container Registry

**Trạng thái**: ✅ Đã cài đặt (một số pods khởi động lại)
**Namespace**: `platform-services`
**Release Name**: `harbor`

#### Thông tin truy cập:
- **URL**: http://harbor.local (qua Traefik Gateway IP: *************)
- **Username**: `admin`
- **Password**: `Harbor12345`
- **Service**: `harbor:80`

#### Cấu hình quan trọng:
- TLS disabled (HTTP only)
- External URL: http://harbor.local
- PostgreSQL backend từ platform PostgreSQL
- Redis backend từ platform Redis

#### Lệnh hữu ích:
```bash
# Kiểm tra Harbor pods
kubectl get pods -n platform-services -l app=harbor

# Test truy cập
curl -H "Host: harbor.local" http://*************/
```

### 5. MinIO Object Storage

**Trạng thái**: ✅ Đã cài đặt và chạy ổn định
**Namespace**: `platform-services`
**Release Name**: `minio`

#### Thông tin truy cập:
- **Console URL**: http://minio.local (qua Traefik Gateway IP: *************)
- **Username**: `admin`
- **Password**: `minio123456`
- **API Service**: `minio:9000`
- **Console Service**: `minio-console:9090`

#### Cấu hình quan trọng:
- Single-node deployment
- Persistent storage: 8Gi
- Console UI enabled
- S3-compatible API

#### Lệnh hữu ích:
```bash
# Kiểm tra MinIO pods
kubectl get pods -n platform-services -l app.kubernetes.io/name=minio

# Test truy cập console
curl -H "Host: minio.local" http://*************/
```

### 6. SigNoz Observability Platform

**Trạng thái**: ✅ Đã cài đặt và chạy ổn định
**Namespace**: `platform-services`
**Release Name**: `signoz`

#### Thông tin truy cập:
- **UI URL**: http://signoz.local (qua Traefik Gateway IP: *************)
- **Username**: Không yêu cầu (first-time setup)
- **Password**: Không yêu cầu (first-time setup)
- **Service**: `signoz:8080`

#### Cấu hình quan trọng:
- ClickHouse backend cho metrics storage
- Zookeeper cho coordination
- OpenTelemetry collector integrated
- Query service port: 8080

#### Lệnh hữu ích:
```bash
# Kiểm tra SigNoz pods
kubectl get pods -n platform-services -l app.kubernetes.io/name=signoz

# Test truy cập UI
curl -H "Host: signoz.local" http://*************/
```

## Cấu hình truy cập từ LAN

### Cách 1: Sử dụng Traefik Gateway LoadBalancer (Khuyến nghị)

**Bước 1**: Cấu hình file hosts trên máy client
```bash
# Windows: C:\Windows\System32\drivers\etc\hosts
# Linux/Mac: /etc/hosts
************* vault.local
************* argocd.local
************* traefik.local
************* harbor.local
************* minio.local
************* signoz.local
```

**Bước 2**: Truy cập các dịch vụ
- **Vault UI**: http://vault.local
- **ArgoCD UI**: http://argocd.local
- **Traefik Dashboard**: https://traefik.local (HTTPS only)
- **Harbor Registry UI**: http://harbor.local
- **MinIO Object Storage Console**: http://minio.local
- **SigNoz Observability Platform**: http://signoz.local

### Tóm tắt thông tin đăng nhập:

| Service | URL | Username | Password | Notes |
|---------|-----|----------|----------|-------|
| Vault | http://vault.local | - | `bootstrap-root-token` | Root token |
| ArgoCD | http://argocd.local | `admin` | `admin123` | GitOps CD |
| Harbor | http://harbor.local | `admin` | `Harbor12345` | Container Registry |
| MinIO | http://minio.local | `admin` | `minio123456` | Object Storage |
| SigNoz | http://signoz.local | - | - | No auth required |

## Bước tiếp theo

1. **Cấu hình HTTPS listener** với TLS certificates cho Gateway
2. **Thiết lập proper authentication** cho các platform services
3. **Cấu hình Vault policies** và authentication methods
4. **Tạo HTTPRoute** cho các ứng dụng mới
5. **Setup monitoring và logging pipeline** với SigNoz

## Bảo mật

⚠️ **Cảnh báo**: Cấu hình hiện tại chỉ phù hợp cho môi trường phát triển/testing:

- Vault chạy ở dev mode với root token cố định
- ArgoCD chạy ở insecure mode
- Mật khẩu admin được hardcode
- Không có TLS encryption

Đối với môi trường production, cần:
- Cấu hình Vault với proper storage backend
- Kích hoạt TLS cho tất cả services
- Sử dụng proper authentication và authorization
- Rotate passwords và tokens thường xuyên

## Liên hệ

- **Maintainer**: OSP Group
- **Email**: <EMAIL>
- **Repository**: https://github.com/ospgroupvn/k8s-deployment
