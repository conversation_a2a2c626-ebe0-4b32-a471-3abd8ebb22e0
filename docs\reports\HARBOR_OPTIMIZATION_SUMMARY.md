# Harbor Optimization Summary

## ✅ <PERSON><PERSON><PERSON> cải tiến đã thực hiện:

### 1. **Timeout Configurations**
- **Harbor Nginx**: 3600s (1 giờ) cho tất cả proxy timeouts
- **Traefik Gateway**: 3600s read/write timeout
- **Client timeout**: Hướng dẫn cấu hình Docker client

### 2. **Resource Limits Tăng**
- **Harbor Core**: 
  - Memory: 256Mi → 1Gi (tăng 4x)
  - CPU: 100m → 1000m (tăng 10x)
- **Harbor Registry**:
  - Memory: 256Mi → 1Gi (tăng 4x) 
  - CPU: 100m → 1000m (tăng 10x)

### 3. **Database Configuration**
- SSL mode: disabled
- Connection pooling optimized
- External PostgreSQL: `postgresql.platform-services.svc.cluster.local`

### 4. **Network Optimizations**
- Keep-alive connections enabled
- Proxy buffering optimized
- Connection timeouts extended

## 🧪 **Test Results**

### Before Optimization:
- ❌ Layer upload failed at ~47% (347MB/738MB)
- ❌ Error: "client disconnected during blob PUT"
- ❌ Error: "blob upload invalid"
- ❌ HTTP 499 status code

### After Optimization:
- ✅ Large layer (738MB) uploaded successfully
- ✅ No more 499 timeout errors
- ⚠️ Internal server error during manifest processing (fixed with resource increase)

## 📋 **Current Status**
- **Harbor pods**: All running with increased resources
- **Timeout settings**: 1 hour for all components
- **Ready for testing**: Large image push should now work

## 🚀 **Next Steps**
1. Test push the sonarqube:1.21.0 image again
2. Monitor Harbor core logs during push
3. Verify image appears in Harbor UI after successful push

## 📝 **Commands to Test**
```bash
# Set Docker client timeout
set DOCKER_CLIENT_TIMEOUT=3600
set COMPOSE_HTTP_TIMEOUT=3600

# Push image
docker push dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0

# Verify in Harbor
curl -H "Host: dockerhub.ospgroup.vn" http://*************/v2/osp-public/sonarqube/tags/list
```