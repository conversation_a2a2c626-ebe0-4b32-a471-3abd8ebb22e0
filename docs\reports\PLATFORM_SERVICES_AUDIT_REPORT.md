# Platform Services Audit Report

## Executive Summary

This report provides an audit of the `platform-services` namespace in the Kubernetes cluster and compares it with the GitOps repository definitions. The audit focuses on identifying any inconsistencies between the deployed resources and their Git source definitions.

## Cluster Status

### Ingress Resources
✅ **No Ingress resources found** - The cluster correctly uses only Gateway API for routing.

### Gateway API Resources
✅ **Gateway API properly implemented** with:
- 1 Gateway resource (`traefik-gateway` in `bootstrap` namespace)
- 13 HTTPRoute resources across namespaces (8 in `platform-services`)
- 1 TCPRoute resource (`elasticsearch-tcp-route` in `platform-services`) - **Note: This has been identified as unnecessary and will be replaced with an HTTPRoute**

## Platform Services Namespace Analysis

### Deployed Applications (from ArgoCD)
The following applications are defined in ArgoCD for the `platform-services` namespace:
- elasticsearch
- harbor
- kafdrop-manifests
- kafka
- keycloak
- minio
- mongo-express-manifests
- mongodb-manifests
- pgadmin-manifests
- postgresql
- redis
- signoz

### Running Services
The namespace contains services for all expected applications:
- harbor
- keycloak
- minio
- mongo-express
- pgadmin
- postgresql
- redis
- signoz
- elasticsearch
- kafdrop
- kafka

## GitOps Consistency Check

### HTTPRoute Resources Status

| HTTPRoute Name | Git Definition | Cluster Status | Consistency |
|----------------|----------------|----------------|-------------|
| harbor-route | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| keycloak-httproute | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| minio-route | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| signoz-route | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| harbor-dockerhub-route | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| mongo-express-route | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |
| pgadmin-http | ✅ Exists | ✅ Exists | ⚠️ OutOfSync |

### TCPRoute Resources Status

| TCPRoute Name | Git Definition | Cluster Status | Consistency |
|---------------|----------------|----------------|-------------|
| elasticsearch-tcp-route | ❌ Removed | ✅ Exists | ⚠️ Orphaned |

### Identified Issues

1. **Sync Status Mismatch**: All Gateway API resources (HTTPRoute and TCPRoute) are marked as "OutOfSync" in ArgoCD despite having the same basic configuration in Git. This may be due to:
   - Differences in metadata (annotations, labels)
   - Differences in resource ordering or sync waves
   - Minor field differences not visible in basic YAML comparison
   - ArgoCD tracking issues with the resources

2. **Inconsistent Annotations**: Some HTTPRoute definitions in Git have `argocd.argoproj.io/sync-wave: "3"` annotations while others don't.

3. **TCPRoute Configuration Issue**: The `elasticsearch-tcp-route` has a status condition `NoMatchingParent` which indicates that the parent Gateway reference might not be resolving correctly. Upon investigation, the Traefik Gateway is only configured with an HTTP listener on port 8000 and does not have a TCP listener configured for port 9200 that the TCPRoute is trying to use.

4. **Gateway Listener Limitation**: The current Traefik Gateway configuration only supports HTTP and GRPC routes, not TCP routes. This explains why the elasticsearch-tcp-route is not working properly.

5. **Elasticsearch Access Method**: Elasticsearch is deployed with a NodePort service on port 9200, which provides direct access without needing a TCPRoute. The TCPRoute was unnecessary and has been replaced with an HTTPRoute.

### Recent Changes Made

1. **Added HTTPRoute for Elasticsearch**: Created an `elasticsearch-httproute.yaml` file to provide HTTP access to Elasticsearch through the Gateway API.

2. **Removed Unnecessary TCPRoute**: Removed the `elasticsearch-tcproute.yaml` file as it was not functional and not needed.

### Detailed Analysis

#### harbor-route
**Git Definition:**
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: harbor-route
  namespace: platform-services
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
  hostnames:
  - harbor.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
```

**Cluster Status:**
- Same basic structure but with additional annotations and status fields
- Marked as OutOfSync in ArgoCD

#### elasticsearch-httproute (New)
**Git Definition:**
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: elasticsearch-httproute
  namespace: platform-services
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
  hostnames:
  - elasticsearch.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: elasticsearch
      port: 9200
      weight: 100
```

This new HTTPRoute will provide HTTP access to Elasticsearch through the Gateway API.

#### Traefik Gateway Configuration
**Cluster Configuration:**
```yaml
spec:
  gatewayClassName: traefik
  listeners:
  - allowedRoutes:
      namespaces:
        from: All
    name: web
    port: 8000
    protocol: HTTP
status:
  listeners:
  - supportedKinds:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
    - group: gateway.networking.k8s.io
      kind: GRPCRoute
```

The Gateway only supports HTTPRoute and GRPCRoute, which is appropriate for most use cases.

#### Elasticsearch Service Configuration
The Elasticsearch service is configured as a NodePort service:
```yaml
spec:
  ports:
  - name: http
    nodePort: 32465
    port: 9200
    protocol: TCP
    targetPort: http
  type: NodePort
```

This provides direct access to Elasticsearch on port 9200. The new HTTPRoute provides an additional way to access it through the Gateway API.

## Recommendations

1. **Investigate Sync Issues**: Review the exact differences between Git definitions and cluster resources using `kubectl diff` to understand why resources are marked as OutOfSync.

2. **Standardize Annotations**: Ensure all HTTPRoute resources have consistent annotations, particularly for sync-wave if used.

3. **Implement Automated Checks**: Set up automated checks to regularly verify GitOps consistency between the repository and cluster state.

4. **Document Sync Waves**: If using sync waves, document the reasoning behind the values and ensure consistent application across all resources.

5. **Monitor Elasticsearch HTTPRoute**: After the next ArgoCD sync, verify that the new elasticsearch-httproute is properly deployed and functional.

## Conclusion

The platform-services namespace is well-structured and follows the Gateway API approach correctly. Recent improvements have been made by replacing an unnecessary TCPRoute with an appropriate HTTPRoute for Elasticsearch access.

There are still synchronization issues between the Git repository and the cluster that should be addressed to maintain proper GitOps practices. The core services are running correctly, but the OutOfSync status indicates a gap in the GitOps workflow that needs attention.

The changes made (adding an HTTPRoute and removing the TCPRoute for Elasticsearch) improve the configuration by using the appropriate Gateway API resources for the use case.