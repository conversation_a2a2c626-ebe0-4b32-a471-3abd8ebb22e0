# Kế hoạch Migration từ IngressRoute sang Gateway API

## Tình trạng hiện tại

### ✅ Gateway API đã có sẵn:
- **Gateway:** `traefik-gateway` trong namespace `bootstrap`
- **External IP:** *************
- **Listeners:** HTTP (port 8000), TCP (port 5432)
- **HTTPRoutes hoạt động:** 10 routes
- **Gateway Class:** traefik

### ⚠️ IngressRoute cần migration:
| Namespace | IngressRoute | HTTPRoute tương ứng | Trạng thái |
|-----------|--------------|-------------------|------------|
| bootstrap | argocd-server | ✅ argocd-server | Có sẵn |
| bootstrap | argocd-server-secure | ❌ | Cần tạo |
| bootstrap | traefik-dashboard | ❌ | Cần tạo |
| bootstrap | vault-ingressroute | ❌ | Cần tạo |
| platform-services | dockerhub-ingressroute | ✅ harbor-dockerhub-route | Có sẵn |
| platform-services | harbor-ingressroute | ✅ harbor-route | Có sẵn |
| platform-services | keycloak-ingressroute | ✅ keycloak-httproute | Có sẵn |
| platform-services | minio-ingressroute | ✅ minio-route | Có sẵn |
| platform-services | mongo-express-ingressroute | ✅ mongo-express-route | Có sẵn |
| platform-services | pgadmin-ingressroute | ✅ pgadmin-http | Có sẵn |
| platform-services | signoz-ingressroute | ✅ signoz-route | Có sẵn |

## Phân tích

### Điểm mạnh:
1. **Gateway API đã hoạt động ổn định** - 10 HTTPRoutes đang chạy tốt
2. **Hầu hết services đã có HTTPRoute** - chỉ cần cleanup IngressRoute
3. **External IP đã được cấp phát** - không cần thay đổi networking

### Cần làm:
1. **Tạo 4 HTTPRoute còn thiếu** cho bootstrap services
2. **Cleanup 12 IngressRoute cũ**
3. **Cập nhật bootstrap configs** để chỉ sử dụng Gateway API

## Kế hoạch thực hiện

### Giai đoạn 1: Tạo HTTPRoute cho bootstrap services

#### 1.1 ArgoCD Server Secure (HTTPS)
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-server-secure
  namespace: bootstrap
  labels:
    app: argocd
    component: server-secure
spec:
  hostnames:
    - argocd.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
          headers:
            - name: x-forwarded-proto
              value: https
      backendRefs:
        - name: argocd-bootstrap-server
          port: 443
```

#### 1.2 Traefik Dashboard
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: traefik-dashboard
  namespace: bootstrap
  labels:
    app: traefik
    component: dashboard
spec:
  hostnames:
    - traefik.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /dashboard
      backendRefs:
        - name: traefik-bootstrap
          port: 8080
    - matches:
        - path:
            type: PathPrefix
            value: /api
      backendRefs:
        - name: traefik-bootstrap
          port: 8080
```

#### 1.3 Vault HTTPRoute
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: vault-server
  namespace: bootstrap
  labels:
    app: vault
    component: server
spec:
  hostnames:
    - vault.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: vault-bootstrap-ui
          port: 8200
```

### Giai đoạn 2: Validation và Testing

#### 2.1 Kiểm tra HTTPRoute hoạt động
```bash
# Kiểm tra tất cả HTTPRoute
kubectl get httproute --all-namespaces

# Kiểm tra status chi tiết
kubectl describe httproute -n bootstrap
kubectl describe httproute -n platform-services

# Test connectivity
curl -H "Host: argocd.local" http://*************/
curl -H "Host: vault.local" http://*************/
curl -H "Host: traefik.local" http://*************/dashboard/
```

### Giai đoạn 3: Cleanup IngressRoute

#### 3.1 Backup IngressRoute (Optional)
```bash
kubectl get ingressroute --all-namespaces -o yaml > backup-ingressroutes.yaml
```

#### 3.2 Xóa từng IngressRoute
```bash
# Bootstrap namespace
kubectl delete ingressroute argocd-server -n bootstrap
kubectl delete ingressroute argocd-server-secure -n bootstrap
kubectl delete ingressroute traefik-dashboard -n bootstrap
kubectl delete ingressroute vault-ingressroute -n bootstrap

# Platform-services namespace
kubectl delete ingressroute dockerhub-ingressroute -n platform-services
kubectl delete ingressroute harbor-ingressroute -n platform-services
kubectl delete ingressroute keycloak-ingressroute -n platform-services
kubectl delete ingressroute minio-ingressroute -n platform-services
kubectl delete ingressroute mongo-express-ingressroute -n platform-services
kubectl delete ingressroute pgadmin-ingressroute -n platform-services
kubectl delete ingressroute signoz-ingressroute -n platform-services
```

### Giai đoạn 4: Cập nhật Bootstrap Configs

#### 4.1 Thêm HTTPRoute vào bootstrap/common/
- Tạo `bootstrap/common/argocd-httproute.yaml` (đã có)
- Tạo `bootstrap/common/vault-httproute.yaml` (đã có)
- Tạo `bootstrap/common/traefik-dashboard-httproute.yaml`

#### 4.2 Cập nhật ArgoCD Helm values
Đảm bảo ArgoCD values.yaml không tạo IngressRoute:
```yaml
server:
  ingress:
    enabled: false
  ingressGrpc:
    enabled: false
```

#### 4.3 Cập nhật Traefik Helm values
Đảm bảo Traefik chỉ enable Gateway API:
```yaml
gateway:
  enabled: true
ingressRoute:
  dashboard:
    enabled: false  # Tắt IngressRoute tự động
```

## Timeline thực hiện

### Tuần 1:
- [x] Kiểm tra tình trạng hiện tại ✅
- [ ] Tạo 3 HTTPRoute còn thiếu
- [ ] Test và validate

### Tuần 2:
- [ ] Cleanup tất cả IngressRoute
- [ ] Cập nhật bootstrap configs
- [ ] Commit changes to GitOps

## Lợi ích sau migration

### ✅ Tuân thủ Kubernetes standards:
- Sử dụng Gateway API chính thức thay vì CRD proprietary
- Tương thích với multi-vendor gateway implementations

### ✅ Consistency:
- Tất cả traffic routing qua cùng một cơ chế
- Dễ quản lý và troubleshoot

### ✅ GitOps friendly:
- Tất cả HTTPRoute được quản lý qua Git
- Không còn manual IngressRoute deployments

### ✅ Future-proof:
- Gateway API là chuẩn tương lai của Kubernetes
- Sẵn sàng cho các gateway khác (Istio, Envoy Gateway, etc.)

## Rủi ro và mitigation

### ⚠️ Rủi ro:
1. **Downtime ngắn** khi switch routes
2. **DNS/hostname conflicts** nếu cấu hình sai

### 🛡️ Mitigation:
1. **Tạo HTTPRoute trước, xóa IngressRoute sau**
2. **Test từng service một cách riêng biệt**
3. **Backup configs trước khi thay đổi**
4. **Rollback plan sẵn sàng**

---
*Migration plan được tạo bởi Claude Code vào ngày 2025-09-18*