# Harbor Debug Commands

## Test với image nhỏ hơn trước:
```bash
# Tag lại với tag khác
docker tag dockerhub.ospgroup.vn/osp-public/sonarqube:1.21.0 dockerhub.ospgroup.vn/osp-public/sonarqube:test

# Push tag mới
docker push dockerhub.ospgroup.vn/osp-public/sonarqube:test
```

## Hoặc thử push image nhỏ hơn:
```bash
# Pull và push image alpine nhỏ
docker pull alpine:latest
docker tag alpine:latest dockerhub.ospgroup.vn/osp-public/alpine:test
docker push dockerhub.ospgroup.vn/osp-public/alpine:test
```

## Kiểm tra kết quả:
```bash
# Kiểm tra trong Harbor UI hoặc
curl -H "Host: dockerhub.ospgroup.vn" http://*************/v2/osp-public/sonarqube/tags/list
curl -H "Host: dockerhub.ospgroup.vn" http://*************/v2/osp-public/alpine/tags/list
```

## Monitor logs trong khi push:
```bash
kubectl logs -f harbor-core-64b7b7c6f7-6dr6z -n platform-services
```