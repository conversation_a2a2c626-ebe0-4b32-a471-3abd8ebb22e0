# Label Studio Deployment Guide

## Tổng quan

Tài liệu này hướng dẫn triển khai Label Studio trên Kubernetes cluster sử dụng ArgoCD và tuân thủ nguyên tắc GitOps.

## Kiến trúc

- **Namespace**: `platform-services`
- **Database**: External PostgreSQL (sử dụng lại cluster hiện có)
- **Cache**: External Redis (sử dụng lại cluster hiện có)
- **Routing**: K8s Gateway API với Traefik
- **URL**: `https://common.ospgroup.vn/label-studio/`

## Các thành phần đã tạo

### 1. Helm Chart Configuration
- `platform-services/charts/label-studio/Chart.yaml`
- `platform-services/charts/label-studio/values.yaml`

### 2. Database Setup
- `platform-services/scripts/init-labelstudio-db.sql`
- `platform-services/platform/labelstudio-secrets.yaml`
- `platform-services/platform/labelstudio-db-init.yaml`

### 3. ArgoCD Application
- `platform-services/platform/labelstudio-app.yaml`

### 4. Gateway API Configuration
- `platform-services/platform/labelstudio-stripprefix-middleware.yaml`
- `platform-services/platform/labelstudio-common-httproute.yaml`

## Thông tin Database

- **Database Name**: `labelstudio`
- **Username**: `labelstudio`
- **Password**: `LS_2025_SecurePass_9x7!`
- **Host**: `postgresql.platform-services.svc.cluster.local:5432`

## Thông tin Redis

- **Host**: `redis-master.platform-services.svc.cluster.local:6379`
- **Database**: `2` (để tránh conflict với các service khác)

## Triển khai

### Bước 1: Commit và Push code

```bash
# Commit tất cả các file đã tạo
git add platform-services/
git commit -m "feat: add Label Studio deployment configuration

- Add Helm chart configuration for Label Studio
- Add database initialization scripts and secrets
- Add ArgoCD application manifest
- Add Gateway API routing configuration
- Configure external PostgreSQL and Redis integration"

# Push lên main branch
git push origin main
```

### Bước 2: Kiểm tra ArgoCD Sync

```bash
# Set KUBECONFIG
export KUBECONFIG=$(pwd)/.kube/config

# Kiểm tra ArgoCD applications
kubectl get applications -n bootstrap

# Kiểm tra sync status của platform-services
kubectl describe application platform-services -n bootstrap
```

### Bước 3: Kiểm tra Database Initialization

```bash
# Kiểm tra job khởi tạo database
kubectl get jobs -n platform-services | grep labelstudio

# Xem logs của job
kubectl logs job/labelstudio-db-init -n platform-services

# Kiểm tra secret đã được tạo
kubectl get secret labelstudio-db-secret -n platform-services
```

### Bước 4: Kiểm tra Label Studio Deployment

```bash
# Kiểm tra pods
kubectl get pods -n platform-services | grep labelstudio

# Kiểm tra services
kubectl get svc -n platform-services | grep labelstudio

# Xem logs của Label Studio
kubectl logs -l app.kubernetes.io/name=label-studio -n platform-services
```

### Bước 5: Kiểm tra Gateway API

```bash
# Kiểm tra HTTPRoute
kubectl get httproute -n platform-services | grep labelstudio

# Kiểm tra middleware
kubectl get middleware -n platform-services | grep labelstudio

# Describe HTTPRoute để xem chi tiết
kubectl describe httproute labelstudio-common-route -n platform-services
```

## Kiểm tra truy cập

### 1. Test internal connectivity

```bash
# Port forward để test local
kubectl port-forward svc/labelstudio-label-studio 8080:80 -n platform-services

# Test trong terminal khác
curl http://localhost:8080/health
curl http://localhost:8080/version
```

### 2. Test external access

```bash
# Test qua Gateway API
curl -k https://common.ospgroup.vn/label-studio/health
curl -k https://common.ospgroup.vn/label-studio/version
```

### 3. Test database connection

```bash
# Exec vào Label Studio pod để test DB connection
kubectl exec -it deployment/labelstudio-label-studio -n platform-services -- /bin/bash

# Trong pod, test connection
python -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='postgresql.platform-services.svc.cluster.local',
        database='labelstudio',
        user='labelstudio',
        password='LS_2025_SecurePass_9x7!'
    )
    print('Database connection successful!')
    conn.close()
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

## Troubleshooting

### 1. Database Connection Issues

```bash
# Kiểm tra PostgreSQL service
kubectl get svc postgresql -n platform-services

# Test connection từ một pod khác
kubectl run postgres-client --rm -it --image=postgres:16-alpine -- /bin/bash
# Trong pod:
# psql -h postgresql.platform-services.svc.cluster.local -U labelstudio -d labelstudio
```

### 2. Redis Connection Issues

```bash
# Kiểm tra Redis service
kubectl get svc redis-master -n platform-services

# Test Redis connection
kubectl run redis-client --rm -it --image=redis:alpine -- redis-cli -h redis-master.platform-services.svc.cluster.local
```

### 3. Gateway API Issues

```bash
# Kiểm tra Traefik logs
kubectl logs -l app.kubernetes.io/name=traefik -n bootstrap

# Kiểm tra Gateway status
kubectl get gateway traefik-gateway -n bootstrap -o yaml
```

## Bảo mật

1. **Database Credentials**: Được lưu trong Kubernetes Secret
2. **Network Policies**: Cần cấu hình nếu yêu cầu isolation
3. **RBAC**: Label Studio sử dụng ServiceAccount riêng
4. **TLS**: Traffic được mã hóa qua HTTPS

## Monitoring và Logging

- **Logs**: Sử dụng `kubectl logs` để xem logs của các pods
- **Metrics**: Label Studio expose metrics tại `/metrics` endpoint
- **Health Check**: Sử dụng `/health` và `/version` endpoints

## Backup và Recovery

- **Database**: Backup PostgreSQL database `labelstudio`
- **Files**: Backup persistent volumes nếu có
- **Configuration**: Tất cả config được lưu trong Git repository
