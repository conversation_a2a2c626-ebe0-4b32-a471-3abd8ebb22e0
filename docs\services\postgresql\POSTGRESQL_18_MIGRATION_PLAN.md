# Plan Nâng cấp PostgreSQL 16.4 lên 18.0 trong K8s Cluster

## 🔍 Tình trạng hiện tại

### PostgreSQL hiện tại trong K8s
- **Version**: PostgreSQL 16.4 (docker.io/bitnami/postgresql:16.4.0-debian-12-r9)
- **Namespace**: platform-services
- **Pod**: postgresql-0 (StatefulSet)
- **Chart**: postgresql-15.5.32 (Bitnami Helm Chart)
- **Replicas**: 1 (Single instance)
- **Password**: postgres123 (từ secret)
- **Port**: 5432 (với NodePort 32000)
- **Metrics**: Bật prometheus exporter (port 9187)

### Cấu hình Storage
- **PVC Retention Policy**: whenDeleted=Retain, whenScaled=Retain
- **Data Persistence**: /bitnami/postgresql/data
- **Management Policy**: OrderedReady

## 🎯 <PERSON><PERSON><PERSON> tiêu nâng cấp

**Từ**: PostgreSQL 16.4 → **Đến**: PostgreSQL 18.0
**Timeline**: Q4 2024 (sau khi PostgreSQL 18 ổn định)
**Downtime**: Tối thiểu hoá (dưới 15 phút)

## 📋 Plan chi tiết từng bước

### Phase 1: Chuẩn bị và Validation (1-2 tuần)

#### 1.1 Pre-migration Assessment
```bash
# Kiểm tra database size và performance baseline
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "\l+"

kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "
    SELECT schemaname, tablename, attname, n_distinct, correlation
    FROM pg_stats
    ORDER BY schemaname, tablename;"

# Kiểm tra extensions đang sử dụng
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "\dx"
```

#### 1.2 Backup Strategy Setup
```bash
# Tạo full backup trước khi upgrade
kubectl create job postgresql-backup-pre-18 --from=cronjob/postgresql-backup \
  -n platform-services

# Verify backup integrity
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 pg_dumpall -U postgres > full-backup-pre-18.sql
```

#### 1.3 Test Environment Setup
```yaml
# staging-postgresql-18.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: postgresql-staging
---
apiVersion: helm.cattle.io/v1
kind: HelmChart
metadata:
  name: postgresql-18-staging
  namespace: postgresql-staging
spec:
  chart: postgresql
  repo: https://charts.bitnami.com/bitnami
  version: "16.0.0"  # Latest chart với PostgreSQL 18
  targetNamespace: postgresql-staging
  valuesContent: |-
    image:
      tag: "18.0.0-debian-12-r0"
    auth:
      enablePostgresUser: true
      postgresPassword: "postgres123"
    primary:
      persistence:
        enabled: true
        size: 10Gi
      resources:
        requests:
          memory: "256Mi"
          cpu: "250m"
        limits:
          memory: "512Mi"
          cpu: "500m"
```

### Phase 2: Compatibility Testing (1 tuần)

#### 2.1 Application Compatibility
```python
# Test script cho OSP Agent với PostgreSQL 18
# File: test_pg18_compatibility.py

import asyncio
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine

async def test_postgresql_18_compatibility():
    """Test OSP Agent compatibility với PostgreSQL 18"""

    # Test 1: Basic connection
    conn = await asyncpg.connect(
        "postgresql://postgres:postgres123@localhost:5432/postgres"
    )
    version = await conn.fetchval("SELECT version()")
    print(f"Connected to: {version}")

    # Test 2: UUID v7 support (PostgreSQL 18 feature)
    try:
        uuid_v7 = await conn.fetchval("SELECT uuidv7()")
        print(f"UUID v7 generated: {uuid_v7}")
    except Exception as e:
        print(f"UUID v7 not supported: {e}")

    # Test 3: Performance với skip scan
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS test_tasks (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            status VARCHAR(50),
            created_at TIMESTAMP DEFAULT NOW(),
            title TEXT
        )
    """)

    # Insert test data
    for i in range(10000):
        await conn.execute(
            "INSERT INTO test_tasks (status, title) VALUES ($1, $2)",
            "pending" if i % 3 == 0 else "completed",
            f"Task {i}"
        )

    # Test skip scan performance
    import time
    start = time.time()
    result = await conn.fetch("""
        SELECT DISTINCT status, created_at::date
        FROM test_tasks
        ORDER BY status, created_at::date
    """)
    end = time.time()
    print(f"Skip scan query took: {end - start:.4f}s")

    await conn.close()

# Chạy test
asyncio.run(test_postgresql_18_compatibility())
```

#### 2.2 Extension Compatibility Check
```bash
# Kiểm tra extensions cần thiết cho OSP Agent
kubectl exec postgresql-18-staging-0 -n postgresql-staging -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "
    CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";
    CREATE EXTENSION IF NOT EXISTS \"pg_trgm\";
    CREATE EXTENSION IF NOT EXISTS \"btree_gin\";
    SELECT * FROM pg_available_extensions WHERE name IN
    ('uuid-ossp', 'pg_trgm', 'btree_gin', 'pg_stat_statements');
  "
```

### Phase 3: Migration Execution (Scheduled Maintenance Window)

#### 3.1 Pre-migration Checklist
- [ ] Thông báo maintenance window cho team
- [ ] Backup verification completed
- [ ] Staging tests passed
- [ ] Monitoring alerts configured
- [ ] Rollback plan ready

#### 3.2 Blue-Green Migration Strategy

**Step 1: Deploy PostgreSQL 18 instance**
```bash
# Deploy PostgreSQL 18 song song với 16.4
kubectl apply -f - <<EOF
apiVersion: helm.cattle.io/v1
kind: HelmChart
metadata:
  name: postgresql-18
  namespace: platform-services
spec:
  chart: postgresql
  repo: https://charts.bitnami.com/bitnami
  version: "16.0.0"
  targetNamespace: platform-services
  valuesContent: |-
    nameOverride: "postgresql-18"
    fullnameOverride: "postgresql-18"
    image:
      tag: "18.0.0-debian-12-r0"
    auth:
      enablePostgresUser: true
      postgresPassword: "postgres123"
    primary:
      persistence:
        enabled: true
        size: 20Gi  # Tăng size cho safety
        storageClass: "fast-ssd"
      resources:
        requests:
          memory: "512Mi"
          cpu: "500m"
        limits:
          memory: "1Gi"
          cpu: "1"
      service:
        type: ClusterIP
        ports:
          postgresql: 5433  # Port khác để tránh conflict
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true
EOF
```

**Step 2: Data Migration với minimal downtime**
```bash
# 1. Tạo logical replication slot trên PostgreSQL 16.4
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "
    SELECT pg_create_logical_replication_slot('migration_slot', 'pgoutput');
  "

# 2. Dump schema và data đến PostgreSQL 18
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 pg_dumpall -U postgres --schema-only | \
kubectl exec -i postgresql-18-0 -n platform-services -- \
  env PGPASSWORD=postgres123 psql -U postgres

# 3. Dump data với --no-owner --no-privileges
kubectl exec postgresql-0 -n platform-services -c postgresql -- \
  env PGPASSWORD=postgres123 pg_dumpall -U postgres --data-only --no-owner --no-privileges | \
kubectl exec -i postgresql-18-0 -n platform-services -- \
  env PGPASSWORD=postgres123 psql -U postgres

# 4. Verify data integrity
kubectl exec postgresql-18-0 -n platform-services -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "\l+"
```

**Step 3: Application Cutover**
```bash
# 1. Scale down OSP Agent
kubectl scale deployment osp-agent -n platform-services --replicas=0

# 2. Final data sync (nếu có logical replication)
# 3. Update ConfigMap/Secret để point đến PostgreSQL 18
kubectl patch secret postgresql -n platform-services --type='json' \
  -p='[{"op": "add", "path": "/data/host", "value": "cG9zdGdyZXNxbC0xOA=="}]'  # base64 của "postgresql-18"

# 4. Scale up OSP Agent với PostgreSQL 18
kubectl scale deployment osp-agent -n platform-services --replicas=2
```

### Phase 4: Post-migration Validation (2-3 ngày)

#### 4.1 Functional Testing
```bash
# Health check endpoints
curl -f http://osp-agent.platform-services.svc.cluster.local:8000/health

# Database connectivity test
kubectl exec deployment/osp-agent -n platform-services -- \
  python -c "
import asyncpg
import asyncio
async def test():
    conn = await asyncpg.connect('****************************************************/postgres')
    result = await conn.fetchval('SELECT version()')
    print(f'Connected to: {result}')
    await conn.close()
asyncio.run(test())
"
```

#### 4.2 Performance Monitoring
```bash
# Monitor key metrics
kubectl exec postgresql-18-0 -n platform-services -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "
    SELECT
      schemaname,
      tablename,
      seq_scan,
      seq_tup_read,
      idx_scan,
      idx_tup_fetch,
      n_tup_ins,
      n_tup_upd,
      n_tup_del
    FROM pg_stat_user_tables;
  "

# I/O performance comparison (expect 2-3x improvement)
kubectl exec postgresql-18-0 -n platform-services -- \
  env PGPASSWORD=postgres123 psql -U postgres -c "
    SELECT datname, blks_read, blks_hit,
           round(blks_hit*100.0/(blks_hit+blks_read), 2) as cache_hit_ratio
    FROM pg_stat_database
    WHERE datname NOT IN ('template0', 'template1');
  "
```

#### 4.3 Cleanup Phase (sau 1 tuần stable)
```bash
# Xóa PostgreSQL 16.4 cũ (sau khi confirm mọi thứ OK)
kubectl delete statefulset postgresql -n platform-services
kubectl delete pvc data-postgresql-0 -n platform-services  # Cẩn thận!
kubectl delete secret postgresql-old -n platform-services

# Rename PostgreSQL 18 thành primary
kubectl patch statefulset postgresql-18 -n platform-services --type='json' \
  -p='[{"op": "replace", "path": "/metadata/name", "value": "postgresql"}]'
```

## 🚨 Rollback Plan

### Nếu migration thất bại:
```bash
# 1. Scale down OSP Agent
kubectl scale deployment osp-agent -n platform-services --replicas=0

# 2. Revert database connection về PostgreSQL 16.4
kubectl patch secret postgresql -n platform-services --type='json' \
  -p='[{"op": "replace", "path": "/data/host", "value": "cG9zdGdyZXNxbA=="}]'  # base64 của "postgresql"

# 3. Scale up OSP Agent
kubectl scale deployment osp-agent -n platform-services --replicas=2

# 4. Verify functionality
curl -f http://osp-agent.platform-services.svc.cluster.local:8000/health
```

## 📊 Expected Benefits

### Performance Improvements
- **I/O Performance**: 2-3x improvement với AIO subsystem
- **Query Performance**: Skip scan cho B-tree indexes
- **JSON Processing**: SIMD optimizations
- **Memory Usage**: Better hash join optimizations

### New Features Available
- **UUID v7**: Timestamp-ordered UUIDs cho better B-tree locality
- **Virtual Generated Columns**: Computed columns cho analytics
- **Enhanced RETURNING**: Access both old và new values
- **OAuth 2.0 Integration**: Native support cho authentication

## ⚠️ Risks và Mitigation

### Identified Risks
1. **Data Loss**: Mitigation = Multiple backups + verification
2. **Extended Downtime**: Mitigation = Blue-green deployment
3. **Application Incompatibility**: Mitigation = Staging testing
4. **Performance Regression**: Mitigation = Monitoring + rollback plan

### Monitoring Checklist
- [ ] Database connection metrics
- [ ] Query performance baselines
- [ ] Error rates and logs
- [ ] Memory and CPU usage
- [ ] Disk I/O performance
- [ ] Application health endpoints

## 📅 Timeline Summary

| Phase | Duration | Activities |
|-------|----------|------------|
| **Phase 1** | 1-2 tuần | Chuẩn bị, backup, test environment |
| **Phase 2** | 1 tuần | Compatibility testing, performance validation |
| **Phase 3** | 4-6 giờ | Migration execution (maintenance window) |
| **Phase 4** | 2-3 ngày | Post-migration validation và monitoring |
| **Cleanup** | 1 tuần sau | Remove old instance sau khi stable |

**Total Timeline**: 4-6 tuần từ start đến hoàn thành

## 🎯 Success Criteria

- [ ] PostgreSQL 18 running stable trong production
- [ ] Zero data loss confirmed
- [ ] Performance improvements measurable (2-3x I/O)
- [ ] All OSP Agent features working correctly
- [ ] Monitoring và alerting functional
- [ ] Team training completed on new features

---

**Plan được tạo**: 2025-09-27
**Reviewed by**: [Tên reviewer]
**Approved by**: [Tên approver]
**Next Review**: [Date]