# Kubernetes Deployment Coding Guidelines

## Tổng quan

Tài liệu này định nghĩa các quy tắc, nguyên tắc và hướng dẫn coding cho việc triển khai và quản lý Kubernetes cluster trong dự án k8s-deployment của OSP Group. <PERSON><PERSON><PERSON> hướng dẫn này đảm bảo t<PERSON> nhất quán, b<PERSON><PERSON> mật và hiệu quả trong việc quản lý hạ tầng Kubernetes theo phương pháp GitOps.

## Mục lục

1. [Nguyên tắc kiến trúc](#nguyên-tắc-kiến-trúc)
2. [Quy tắc chung](#quy-tắc-chung)
3. [Quy tắc cho namespace bootstrap](#quy-tắc-cho-namespace-bootstrap)
4. [Quy tắc cho platform services](#quy-tắc-cho-platform-services)
5. [Quy tắc cho ứng dụng](#quy-tắc-cho-ứng-dụng)
6. [Quy tắc cho GitHub Runners](#quy-tắc-cho-github-runners)
7. [Quy tắc về security](#quy-tắc-về-security)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)
9. [Tools và utilities](#tools-và-utilities)

---

## Nguyên tắc kiến trúc

### 1. GitOps là nguồn chân lý duy nhất
- **Nguyên tắc**: Git là nguồn chân lý duy nhất (single source of truth) cho toàn bộ Kubernetes cluster.
- **Triển khai**: Mọi thay đổi phải được thực hiện qua Git và ArgoCD, không sử dụng `kubectl apply` trực tiếp.
- **Ngoại lệ**: Chỉ các dịch vụ trong thư mục `bootstrap` được phép sử dụng `kubectl` để cài đặt ban đầu.

### 2. Tách biệt Platform Services và Applications
- **Platform Services**: Các dịch vụ nền tảng (PostgreSQL, Redis, MinIO, Monitoring, Logging) được triển khai trong namespace `platform-services` với vòng đời độc lập.
- **Applications**: Các ứng dụng business không tự deploy DB/Redis đi kèm, sử dụng external database/redis từ platform services.

### 3. Multi-tenant Database/Redis
- Mỗi ứng dụng có database/user riêng trên cùng một cluster PostgreSQL/Redis.
- Không chia sẻ user/schema giữa các ứng dụng.
- Sử dụng Vault để quản lý credentials với dynamic creds per-app (rotation).

### 4. ArgoCD App of Apps
- Sử dụng ArgoCD App of Apps pattern để quản lý deployment.
- Thứ tự sync: platform services trước, applications sau (sử dụng sync-waves).

---

## Quy tắc chung

### 1. Ngôn ngữ và định dạng
- Luôn sử dụng tiếng Việt trong tài liệu, commit message và comment (trừ thuật ngữ chuyên ngành có thể giữ nguyên tiếng Anh).
- Sử dụng YAML format với indentation 2 spaces.
- Sử dụng lowercase với dashes (-) cho naming conventions.

### 2. KUBECONFIG
- Luôn đảm bảo KUBECONFIG được set đúng trước khi gọi bất kỳ lệnh kubectl (readonly) nào:
```bash
export KUBECONFIG=$(pwd)/.kube/config
```

### 3. Version management
- Luôn kiểm tra cách cấu hình chuẩn nhất với các version k8s, version service đang cài đặt.
- Sử dụng fixed version tags thay vì `latest` cho production.

### 4. Helm Charts
- Luôn ưu tiên cài đặt từ helm chart cho các dịch vụ k8s thay vì cài mới từ đầu.
- Custom values được đặt trong file `values.yaml` riêng cho mỗi service/environment.

### 5. Resource reuse
- Luôn kiểm tra các dịch vụ có sẵn trong namespace default, để tái sử dụng lại các dịch vụ thay vì tạo mới.
- Ví dụ: Nếu k8s cluster namespace default có sẵn mariadb, hãy sử dụng lại thay vì cài mới.

### 6. Gateway API
- K8s cluster ưu tiên sử dụng k8s gateway api (traefik) thay vì sử dụng ingress.
- Mọi expose ra bên ngoài đều phải thông qua k8s gateway api, bao gồm cả http route, tcp route.

---

## Quy tắc cho namespace bootstrap

### 1. Mục đích
Namespace `bootstrap` chứa các dịch vụ nền tảng cần thiết cho Kubernetes cluster:
- HashiCorp Vault: Quản lý secrets
- Traefik Gateway: Kubernetes Gateway API và Load Balancer
- ArgoCD: GitOps Continuous Deployment

### 2. Cấu trúc thư mục
```
bootstrap/
├── README.md                 # Documentation
├── common/                   # Cấu hình chung
│   ├── namespace.yaml       # Định nghĩa namespace bootstrap
│   ├── install.sh           # Script cài đặt tất cả dịch vụ
│   └── *.yaml               # Các manifest chung
├── vault/                   # HashiCorp Vault Helm chart
├── traefik/                 # Traefik Gateway Helm chart
└── argocd/                 # ArgoCD Helm chart
```

### 3. Thứ tự cài đặt
1. **Vault** - Quản lý secrets
2. **Traefik** - Gateway và Load Balancer
3. **ArgoCD** - GitOps continuous deployment

### 4. Quy tắc triển khai
- Chỉ sử dụng `kubectl` trong thư mục bootstrap để cài đặt ban đầu.
- Sau khi cài đặt, mọi thay đổi phải được thực hiện qua Git và ArgoCD.
- Các services trong bootstrap được cấu hình với high availability (nếu có thể).

### 5. Ví dụ cấu hình Gateway
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: traefik-gateway
  namespace: bootstrap
  labels:
    app: traefik
    component: gateway
spec:
  gatewayClassName: traefik
  listeners:
  - name: web
    port: 8000
    protocol: HTTP
    allowedRoutes:
      namespaces:
        from: All
  - name: websecure
    port: 8443
    protocol: HTTPS
    allowedRoutes:
      namespaces:
        from: All
```

---

## Quy tắc cho platform services

### 1. Mục đích
Namespace `platform-services` chứa các dịch vụ nền tảng được chia sẻ giữa các ứng dụng:
- Database: PostgreSQL
- Cache: Redis
- Storage: MinIO
- Registry: Harbor
- Monitoring: SigNoz
- Security: Vault (production)
- Authentication: Keycloak

### 2. Cấu trúc thư mục
```
platform-services/
├── platform-services-apps.yaml    # ArgoCD App of Apps definition
├── applications/                  # ArgoCD application definitions
│   ├── postgresql-app.yaml
│   ├── redis-app.yaml
│   ├── minio-app.yaml
│   └── ...
├── charts/                        # Custom Helm charts
│   ├── redis/
│   └── vault/
├── manifests/                     # Kubernetes manifests
│   ├── postgresql-18-simple.yaml
│   └── ...
└── platform/                      # Platform-specific manifests
    ├── harbor-httproute.yaml
    ├── minio-httproute.yaml
    └── ...
```

### 3. Quy tắc triển khai
- Sử dụng ArgoCD App of Apps pattern với file `platform-services-apps.yaml`.
- Mỗi service có ArgoCD application riêng trong thư mục `applications/`.
- Sử dụng sync-waves để đảm bảo thứ tự triển khai đúng.
- External database/redis configuration cho các ứng dụng.

### 4. Ví dụ ArgoCD Application
```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: postgresql
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"  # Deploy before applications
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/postgresql
    targetRevision: main
    helm:
      values: |
        global:
          postgresql:
            auth:
              postgresPassword: "postgres123"
              database: "postgres"
        primary:
          service:
            type: NodePort
            nodePorts:
              postgresql: 32000
          persistence:
            enabled: true
            size: 20Gi
            storageClass: "local-path"
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
```

### 5. Ví dụ HTTPRoute cho platform service
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: minio-route
  namespace: platform-services
  labels:
    app: minio
    component: route
spec:
  hostnames:
    - minio.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: minio
          port: 9000
```

---

## Quy tắc cho ứng dụng

### 1. Cấu trúc thư mục ứng dụng
```
projects/
└── app-name/
    ├── Chart.yaml                # Helm chart metadata
    ├── values.yaml               # Default values
    ├── values-dev.yaml           # Development environment values
    ├── values-prod.yaml          # Production environment values
    ├── templates/                # Helm templates
    │   ├── deployment.yaml
    │   ├── service.yaml
    │   ├── httproute.yaml
    │   └── ...
    └── app-name-app.yaml         # ArgoCD application definition
```

### 2. Quy tắc triển khai
- Mỗi ứng dụng có Helm chart riêng.
- Sử dụng external database/redis từ platform services.
- Mỗi ứng dụng có database/user riêng trên shared PostgreSQL/Redis cluster.
- Expose service qua Gateway API HTTPRoute.

### 3. Ví dụ cấu hình external database
```yaml
# values.yaml
postgresql:
  enabled: false  # Disable embedded PostgreSQL
  
externalDatabase:
  host: "postgresql.platform-services"
  port: 5432
  user: "app_user"
  password: ""  # Sẽ được lấy từ Vault
  database: "app_db"

redis:
  enabled: false  # Disable embedded Redis
  
externalRedis:
  host: "redis.platform-services"
  port: 6379
  password: ""  # Sẽ được lấy từ Vault
```

### 4. Ví dụ HTTPRoute cho ứng dụng
```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: app-name-route
  namespace: app-namespace
  labels:
    app: app-name
    component: route
spec:
  hostnames:
    - app-name.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: app-name-service
          port: 8080
```

---

## Quy tắc cho GitHub Runners

### 1. Mục đích
GitHub Runners được sử dụng để chạy CI/CD pipelines cho các repository trong dự án.

### 2. Cấu trúc thư mục
```
github-runners/
├── Chart.yaml                    # Helm chart metadata
├── values.yaml                   # Default values
├── values-template.yaml          # Template cho repo mới
├── values-REPO-NAME.yaml         # Values cho repository cụ thể
├── templates/                    # Helm templates
│   ├── statefulset.yaml
│   ├── service.yaml
│   ├── serviceaccount.yaml
│   └── ...
└── README.md                     # Documentation
```

### 3. Quy tắc triển khai
- Mỗi repository có runner riêng với values file riêng.
- Sử dụng self-hosted runners với custom Docker image.
- Cấu hình resources phù hợp với nhu cầu của repository.

### 4. Cách thêm runner cho repository mới
1. Tạo values file mới từ template:
```bash
cp github-runners/values-template.yaml github-runners/values-REPO-NAME.yaml
```

2. Chỉnh sửa values-REPO-NAME.yaml:
```yaml
repository:
  url: "https://github.com/ospgroupvn/REPO-NAME"

runner:
  name: "REPO-NAME-runner"
  labels: "self-hosted,linux,x64,docker"  # Thêm labels phù hợp
```

3. Tạo ArgoCD Application:
```bash
cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/REPO-NAME-runner.yaml
```

4. Commit và push lên Git:
```bash
git add github-runners/values-REPO-NAME.yaml
git add github-runners-apps/REPO-NAME-runner.yaml
git commit -m "feat: Add GitHub runner for REPO-NAME"
git push origin main
```

---

## Quy tắc về security

### 1. Secrets Management
- Sử dụng Vault để quản lý tất cả secrets.
- Không hardcode passwords, tokens trong Git.
- Sử dụng dynamic credentials với automatic rotation.

### 2. Password Generation
- Khi cài đặt các dịch vụ mà cần đặt mật khẩu/secret, hãy tạo một mật khẩu ngẫu nhiên và đủ mạnh thay vì mật khẩu dễ đoán.
- Sử dụng Vault để generate và store passwords.

### 3. Network Policies
- Cấu hình Network Policies để hạn chế traffic giữa namespaces.
- Chỉ cho phép traffic cần thiết giữa services.

### 4. RBAC
- Sử dụng RBAC để giới hạn permissions cho service accounts.
- Không sử dụng cluster-admin trừ khi thực sự cần thiết.

### 5. Image Security
- Sử dụng trusted image registries.
- Scan images for vulnerabilities trước khi deploy.

---

## Ví dụ thực tế

### 1. Triển khai ứng dụng mới với external database

**Bước 1: Tạo Helm chart cho ứng dụng**
```yaml
# Chart.yaml
apiVersion: v2
name: my-app
description: My Application Helm Chart
type: application
version: 0.1.0
appVersion: "1.0.0"
```

**Bước 2: Cấu hình values với external database**
```yaml
# values.yaml
replicaCount: 1

image:
  repository: my-app
  pullPolicy: IfNotPresent
  tag: "1.0.0"

service:
  type: ClusterIP
  port: 8080

# External database configuration
postgresql:
  enabled: false
  
externalDatabase:
  host: "postgresql.platform-services"
  port: 5432
  user: "myapp_user"
  database: "myapp_db"
  existingSecret: "myapp-db-secret"

# Redis configuration
redis:
  enabled: false
  
externalRedis:
  host: "redis.platform-services"
  port: 6379
  existingSecret: "myapp-redis-secret"

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi
```

**Bước 3: Tạo HTTPRoute template**
```yaml
# templates/httproute.yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ include "my-app.fullname" . }}-route
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "my-app.labels" . | nindent 4 }}
    component: route
spec:
  hostnames:
    - {{ .Values.ingress.hostname }}
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: {{ include "my-app.fullname" . }}
          port: {{ .Values.service.port }}
```

**Bước 4: Tạo ArgoCD Application**
```yaml
# my-app-app.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"  # Deploy after platform services
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: projects/my-app
    targetRevision: main
    helm:
      valueFiles:
        - values.yaml
        - values-prod.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: my-app
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
```

### 2. Cấu hình Vault secrets cho ứng dụng

```bash
# Tạo policy cho ứng dụng
vault policy write my-app-policy - <<EOF
path "secret/data/my-app/*" {
  capabilities = ["read"]
}

path "database/creds/my-app-role" {
  capabilities = ["read"]
}
EOF

# Tạo database role cho ứng dụng
vault write database/roles/my-app-role \
    db_name=postgresql \
    creation_statements="CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO \"{{name}}\"; GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO \"{{name}}\";" \
    default_ttl="1h" \
    max_ttl="24h"

# Tạo KV secret cho ứng dụng
vault kv put secret/my-app/config \
    redis_password="my-redis-password" \
    api_key="my-api-key"
```

### 3. Cấu hình GitHub Runner cho repository mới

```yaml
# github-runners/values-my-new-app.yaml
replicaCount: 1

image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  pullPolicy: IfNotPresent
  tag: "1.0"

repository:
  url: "https://github.com/ospgroupvn/my-new-app"

runner:
  name: "my-new-app-runner"
  group: "default"
  labels: "self-hosted,linux,x64,docker,nodejs"
  
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

persistence:
  enabled: true
  size: 10Gi
  storageClass: "local-path"

serviceAccount:
  create: true
  annotations: {}
  name: ""
```

---

## Tools và utilities

### 1. Scripts hữu ích

**Kiểm tra trạng thái cluster:**
```bash
#!/bin/bash
export KUBECONFIG=$(pwd)/.kube/config

echo "=== Checking cluster status ==="
kubectl get nodes
kubectl get namespaces
kubectl get pods --all-namespaces

echo "=== Checking Gateway API resources ==="
kubectl get gateway,httproute --all-namespaces

echo "=== Checking ArgoCD applications ==="
kubectl get applications -n bootstrap
```

**Cài đặt bootstrap services:**
```bash
#!/bin/bash
export KUBECONFIG=$(pwd)/.kube/config

echo "=== Installing namespace ==="
kubectl apply -f bootstrap/common/namespace.yaml

echo "=== Installing Vault ==="
helm install vault ./bootstrap/vault -n bootstrap

echo "=== Installing Traefik ==="
helm install traefik ./bootstrap/traefik -n bootstrap

echo "=== Installing ArgoCD ==="
helm install argocd ./bootstrap/argocd -n bootstrap
```

### 2. Kubectl aliases hữu ích

```bash
# Add to ~/.bashrc or ~/.zshrc
alias k='kubectl'
alias kgp='kubectl get pods'
alias kgs='kubectl get services'
alias kgd='kubectl get deployments'
alias kgn='kubectl get nodes'
alias kga='kubectl get applications -n bootstrap'
alias kex='kubectl exec -it'
alias klogs='kubectl logs -f'
```

### 3. Troubleshooting commands

```bash
# Check pod status and events
kubectl describe pod <pod-name> -n <namespace>

# Check service endpoints
kubectl get endpoints -n <namespace>

# Check network connectivity
kubectl exec -it <pod-name> -n <namespace> -- ping <service-name>

# Check DNS resolution
kubectl exec -it <pod-name> -n <namespace> -- nslookup <service-name>

# Check Gateway API status
kubectl describe gateway traefik-gateway -n bootstrap
kubectl describe httproute <route-name> -n <namespace>

# Check ArgoCD sync status
argocd app get <app-name> --refresh
argocd app sync <app-name>
```

---

## Kết luận

Các quy tắc và hướng dẫn này được thiết kế để đảm bảo tính nhất quán, bảo mật và hiệu quả trong việc quản lý Kubernetes cluster. Việc tuân thủ các quy tắc này sẽ giúp:

1. Đảm bảo consistency trong toàn bộ hệ thống
2. Giảm thiểu errors và downtime
3. Tăng cường security
4. Đơn giản hóa việc troubleshooting
5. Hỗ trợ scaling và maintenance

Để cập nhật hoặc bổ sung các quy tắc mới, vui lòng tạo PR và thảo luận với team trước khi merge vào main branch.