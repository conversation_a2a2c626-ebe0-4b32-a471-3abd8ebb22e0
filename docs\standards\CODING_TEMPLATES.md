# Kubernetes Deployment Coding Templates

## Tổng quan

Tài liệu này cung cấp các template và checklist để áp dụng coding guidelines một cách hiệu quả và nhất quán.

## Mụ<PERSON> lục

1. [Checklist cho việc triển khai dịch vụ mới](#checklist-cho-việc-triển-khai-dịch-vụ-mới)
2. [Templates cho Bootstrap Services](#templates-cho-bootstrap-services)
3. [Templates cho Platform Services](#templates-cho-platform-services)
4. [Templates cho Applications](#templates-cho-applications)
5. [Templates cho GitHub Runners](#templates-cho-github-runners)
6. [Scripts tự động hóa](#scripts-tự-động-hóa)

---

## Checklist cho việc triển khai dịch vụ mới

### 1. Checklist cho Bootstrap Service

- [ ] Tạo thư mục cho service trong `bootstrap/`
- [ ] Tạo Helm chart với `Chart.yaml` và `values.yaml`
- [ ] Cấu hình service với high availability (nếu có thể)
- [ ] Thêm HTTPRoute/TCPRoute nếu cần expose ra bên ngoài
- [ ] Cập nhật script cài đặt `bootstrap/common/install.sh`
- [ ] Cập nhật documentation `bootstrap/README.md`
- [ ] Test cài đặt và verify functionality
- [ ] Commit và push changes

### 2. Checklist cho Platform Service

- [ ] Tạo Helm chart trong `platform-services/charts/`
- [ ] Tạo ArgoCD application trong `platform-services/applications/`
- [ ] Cấu hình với sync-wave phù hợp
- [ ] Tạo HTTPRoute/TCPRoute trong `platform-services/platform/`
- [ ] Cấu hình external database/redis (nếu cần)
- [ ] Tạo secrets trong Vault
- [ ] Test deployment và verify functionality
- [ ] Update documentation
- [ ] Commit và push changes

### 3. Checklist cho Application

- [ ] Tạo thư mục cho ứng dụng trong `projects/`
- [ ] Tạo Helm chart với cấu trúc chuẩn
- [ ] Cấu hình external database/redis từ platform services
- [ ] Tạo HTTPRoute để expose service
- [ ] Tạo ArgoCD application definition
- [ ] Tạo database user và secrets trong Vault
- [ ] Cấu hình CI/CD pipeline
- [ ] Test deployment và verify functionality
- [ ] Update documentation
- [ ] Commit và push changes

### 4. Checklist cho GitHub Runner

- [ ] Copy template values file: `cp github-runners/values-template.yaml github-runners/values-REPO-NAME.yaml`
- [ ] Chỉnh sửa values file với thông tin repository
- [ ] Copy template ArgoCD app: `cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/REPO-NAME-runner.yaml`
- [ ] Chỉnh sửa ArgoCD app file
- [ ] Test runner registration
- [ ] Verify CI/CD pipeline functionality
- [ ] Commit và push changes

---

## Templates cho Bootstrap Services

### 1. Helm Chart Template

```yaml
# bootstrap/SERVICE_NAME/Chart.yaml
apiVersion: v2
name: SERVICE_NAME
description: SERVICE_NAME Bootstrap Service
type: application
version: 0.1.0
appVersion: "VERSION"
```

```yaml
# bootstrap/SERVICE_NAME/values.yaml
replicaCount: 1

image:
  repository: REPOSITORY_NAME
  pullPolicy: IfNotPresent
  tag: "VERSION"

service:
  type: ClusterIP
  port: PORT

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

nodeSelector: {}
tolerations: []
affinity: {}
```

### 2. HTTPRoute Template

```yaml
# bootstrap/common/SERVICE_NAME-httproute.yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: SERVICE_NAME
  namespace: bootstrap
  labels:
    app: SERVICE_NAME
    component: route
spec:
  hostnames:
    - SERVICE_NAME.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: SERVICE_NAME-bootstrap
          port: PORT
```

---

## Templates cho Platform Services

### 1. ArgoCD Application Template

```yaml
# platform-services/applications/SERVICE_NAME-app.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: SERVICE_NAME
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"  # Deploy before applications
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/SERVICE_NAME
    targetRevision: main
    helm:
      values: |
        # Configuration values here
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
```

### 2. HTTPRoute Template

```yaml
# platform-services/platform/SERVICE_NAME-httproute.yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: SERVICE_NAME-route
  namespace: platform-services
  labels:
    app: SERVICE_NAME
    component: route
spec:
  hostnames:
    - SERVICE_NAME.local
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: SERVICE_NAME
          port: PORT
```

---

## Templates cho Applications

### 1. Helm Chart Structure

```
projects/APP_NAME/
├── Chart.yaml
├── values.yaml
├── values-dev.yaml
├── values-prod.yaml
└── templates/
    ├── deployment.yaml
    ├── service.yaml
    ├── httproute.yaml
    ├── configmap.yaml
    ├── secret.yaml
    ├── serviceaccount.yaml
    ├── hpa.yaml
    └── _helpers.tpl
```

### 2. Chart.yaml Template

```yaml
# projects/APP_NAME/Chart.yaml
apiVersion: v2
name: APP_NAME
description: APP_NAME Application Helm Chart
type: application
version: 0.1.0
appVersion: "1.0.0"
```

### 3. values.yaml Template

```yaml
# projects/APP_NAME/values.yaml
replicaCount: 1

image:
  repository: APP_NAME
  pullPolicy: IfNotPresent
  tag: "1.0.0"

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  hostname: APP_NAME.local
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # cert-manager.io/cluster-issuer: letsencrypt-prod

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

# External database configuration
postgresql:
  enabled: false
  
externalDatabase:
  host: "postgresql.platform-services"
  port: 5432
  user: "APP_NAME_user"
  database: "APP_NAME_db"
  existingSecret: "APP_NAME-db-secret"

# Redis configuration
redis:
  enabled: false
  
externalRedis:
  host: "redis.platform-services"
  port: 6379
  existingSecret: "APP_NAME-redis-secret"
```

### 4. ArgoCD Application Template

```yaml
# projects/APP_NAME/APP_NAME-app.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: APP_NAME
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"  # Deploy after platform services
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: projects/APP_NAME
    targetRevision: main
    helm:
      valueFiles:
        - values.yaml
        - values-prod.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: APP_NAME
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
```

---

## Templates cho GitHub Runners

### 1. Values Template

```yaml
# github-runners/values-template.yaml
replicaCount: 1

image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  pullPolicy: IfNotPresent
  tag: "1.0"

repository:
  url: "https://github.com/ospgroupvn/REPO_NAME"

runner:
  name: "REPO_NAME-runner"
  group: "default"
  labels: "self-hosted,linux,x64,docker"
  
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

persistence:
  enabled: true
  size: 10Gi
  storageClass: "local-path"

serviceAccount:
  create: true
  annotations: {}
  name: ""

nodeSelector: {}
tolerations: []
affinity: {}
```

### 2. ArgoCD Application Template

```yaml
# github-runners-apps/TEMPLATE-runner.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: REPO_NAME-runner
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: github-runners
    targetRevision: main
    helm:
      valueFiles:
        - values-REPO_NAME.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: github-runners
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
```

---

## Scripts tự động hóa

### 1. Script tạo ứng dụng mới

```bash
#!/bin/bash
# scripts/create-new-app.sh

set -e

APP_NAME=$1
if [ -z "$APP_NAME" ]; then
  echo "Usage: $0 <app-name>"
  exit 1
fi

echo "Creating new application: $APP_NAME"

# Create directory structure
mkdir -p projects/$APP_NAME/templates

# Create Chart.yaml
cat > projects/$APP_NAME/Chart.yaml << EOF
apiVersion: v2
name: $APP_NAME
description: $APP_NAME Application Helm Chart
type: application
version: 0.1.0
appVersion: "1.0.0"
EOF

# Create values.yaml
cat > projects/$APP_NAME/values.yaml << EOF
replicaCount: 1

image:
  repository: $APP_NAME
  pullPolicy: IfNotPresent
  tag: "1.0.0"

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  hostname: $APP_NAME.local

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# External database configuration
postgresql:
  enabled: false
  
externalDatabase:
  host: "postgresql.platform-services"
  port: 5432
  user: "$APP_NAME_user"
  database: "$APP_NAME_db"
  existingSecret: "$APP_NAME-db-secret"

# Redis configuration
redis:
  enabled: false
  
externalRedis:
  host: "redis.platform-services"
  port: 6379
  existingSecret: "$APP_NAME-redis-secret"
EOF

# Create deployment template
cat > projects/$APP_NAME/templates/deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "$APP_NAME.fullname" . }}
  labels:
    {{- include "$APP_NAME.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "$APP_NAME.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "$APP_NAME.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
EOF

# Create service template
cat > projects/$APP_NAME/templates/service.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: {{ include "$APP_NAME.fullname" . }}
  labels:
    {{- include "$APP_NAME.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "$APP_NAME.selectorLabels" . | nindent 4 }}
EOF

# Create HTTPRoute template
cat > projects/$APP_NAME/templates/httproute.yaml << EOF
{{- if .Values.ingress.enabled }}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ include "$APP_NAME.fullname" . }}-route
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "$APP_NAME.labels" . | nindent 4 }}
    component: route
spec:
  hostnames:
    - {{ .Values.ingress.hostname }}
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: web
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: {{ include "$APP_NAME.fullname" . }}
          port: {{ .Values.service.port }}
{{- end }}
EOF

# Create _helpers.tpl
cat > projects/$APP_NAME/templates/_helpers.tpl << EOF
{{/*
Expand the name of the chart.
*/}}
{{- define "$APP_NAME.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "$APP_NAME.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- \$name := default .Chart.Name .Values.nameOverride }}
{{- if contains \$name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name \$name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "$APP_NAME.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "$APP_NAME.labels" -}}
helm.sh/chart: {{ include "$APP_NAME.chart" . }}
{{ include "$APP_NAME.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "$APP_NAME.selectorLabels" -}}
app.kubernetes.io/name: {{ include "$APP_NAME.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
EOF

# Create ArgoCD application
cat > projects/$APP_NAME/$APP_NAME-app.yaml << EOF
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: $APP_NAME
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: projects/$APP_NAME
    targetRevision: main
    helm:
      valueFiles:
        - values.yaml
        - values-prod.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: $APP_NAME
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
EOF

echo "Application $APP_NAME created successfully!"
echo "Don't forget to:"
echo "1. Update the deployment template with your specific requirements"
echo "2. Create database user and secrets in Vault"
echo "3. Configure CI/CD pipeline"
echo "4. Test the deployment"
```

### 2. Script tạo GitHub Runner mới

```bash
#!/bin/bash
# scripts/create-new-runner.sh

set -e

REPO_NAME=$1
if [ -z "$REPO_NAME" ]; then
  echo "Usage: $0 <repo-name>"
  exit 1
fi

echo "Creating GitHub runner for repository: $REPO_NAME"

# Create values file
cp github-runners/values-template.yaml github-runners/values-$REPO_NAME.yaml

# Update values file
sed -i.bak "s/REPO_NAME/$REPO_NAME/g" github-runners/values-$REPO_NAME.yaml
rm github-runners/values-$REPO_NAME.yaml.bak

# Create ArgoCD application
cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/$REPO_NAME-runner.yaml

# Update ArgoCD application file
sed -i.bak "s/REPO_NAME/$REPO_NAME/g" github-runners-apps/$REPO_NAME-runner.yaml
rm github-runners-apps/$REPO_NAME-runner.yaml.bak

echo "GitHub runner for $REPO_NAME created successfully!"
echo "Files created:"
echo "- github-runners/values-$REPO_NAME.yaml"
echo "- github-runners-apps/$REPO_NAME-runner.yaml"
echo ""
echo "Next steps:"
echo "1. Review and update the values file if needed"
echo "2. Commit and push the changes"
echo "3. Verify the runner registration in GitHub repository settings"
```

### 3. Script kiểm tra trạng thái cluster

```bash
#!/bin/bash
# scripts/check-cluster-status.sh

set -e

export KUBECONFIG=$(pwd)/.kube/config

echo "=== Cluster Information ==="
kubectl cluster-info
echo ""

echo "=== Nodes Status ==="
kubectl get nodes -o wide
echo ""

echo "=== Namespaces ==="
kubectl get namespaces
echo ""

echo "=== Pods in All Namespaces ==="
kubectl get pods --all-namespaces
echo ""

echo "=== Gateway API Resources ==="
echo "Gateways:"
kubectl get gateway --all-namespaces
echo ""
echo "HTTPRoutes:"
kubectl get httproute --all-namespaces
echo ""

echo "=== ArgoCD Applications ==="
kubectl get applications -n bootstrap
echo ""

echo "=== Services in Platform Services ==="
kubectl get services -n platform-services
echo ""

echo "=== Persistent Volumes ==="
kubectl get pv
echo ""

echo "=== Persistent Volume Claims ==="
kubectl get pvc --all-namespaces
```

### 4. Script cài đặt bootstrap services

```bash
#!/bin/bash
# scripts/install-bootstrap.sh

set -e

export KUBECONFIG=$(pwd)/.kube/config

echo "=== Installing Bootstrap Services ==="

echo "Creating namespace..."
kubectl apply -f bootstrap/common/namespace.yaml

echo "Installing Traefik Gateway..."
helm upgrade --install traefik ./bootstrap/traefik -n bootstrap --wait

echo "Installing Vault..."
helm upgrade --install vault ./bootstrap/vault -n bootstrap --wait

echo "Installing ArgoCD..."
helm upgrade --install argocd ./bootstrap/argocd -n bootstrap --wait

echo "Applying Gateway configuration..."
kubectl apply -f bootstrap/common/traefik-gateway.yaml

echo "Applying HTTPRoutes..."
kubectl apply -f bootstrap/common/argocd-httproute.yaml
kubectl apply -f bootstrap/common/vault-httproute.yaml

echo "=== Bootstrap Services Installation Complete ==="
echo ""
echo "Access Information:"
echo "- Traefik Dashboard: http://traefik.local"
echo "- ArgoCD UI: http://argocd.local"
echo "- Vault UI: http://vault.local"
echo ""
echo "Run 'scripts/check-cluster-status.sh' to verify installation"
```

---

## Sử dụng Templates

1. **Tạo ứng dụng mới**:
   ```bash
   chmod +x scripts/create-new-app.sh
   ./scripts/create-new-app.sh my-new-app
   ```

2. **Tạo GitHub Runner mới**:
   ```bash
   chmod +x scripts/create-new-runner.sh
   ./scripts/create-new-runner.sh my-repo
   ```

3. **Kiểm tra trạng thái cluster**:
   ```bash
   chmod +x scripts/check-cluster-status.sh
   ./scripts/check-cluster-status.sh
   ```

4. **Cài đặt bootstrap services**:
   ```bash
   chmod +x scripts/install-bootstrap.sh
   ./scripts/install-bootstrap.sh
   ```

---

## Kết luận

Các templates và scripts này được thiết kế để đơn giản hóa việc áp dụng coding guidelines và đảm bảo tính nhất quán trong toàn bộ hệ thống. Việc sử dụng templates giúp:

1. Giảm thiểu lỗi do cấu hình sai
2. Đảm bảo tuân thủ các quy tắc đã định nghĩa
3. Tăng tốc độ triển khai dịch vụ mới
4. Dễ dàng bảo trì và cập nhật

Để cập nhật hoặc bổ sung templates mới, vui lòng tạo PR và thảo luận với team trước khi merge vào main branch.