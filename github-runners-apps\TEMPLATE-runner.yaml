# Template ArgoCD Application for new GitHub repository runners
# Copy this file and modify for each new repository
#
# Usage:
# 1. Copy: cp TEMPLATE-runner.yaml REPO-NAME-runner.yaml
# 2. Replace REPO-NAME with actual repository name (lowercase, no special chars)
# 3. Update valueFiles to point to correct values file
# 4. Commit and push to Git

apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: REPO-NAME-runner  # Replace REPO-NAME with actual repository name
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "2"  # Deploy after secrets
  labels:
    app.kubernetes.io/name: github-runner
    app.kubernetes.io/component: runner
    app.kubernetes.io/part-of: github-runners
    repository: "REPO-NAME"  # Replace with actual repo name
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: github-runners
    helm:
      valueFiles:
        - values-REPO-NAME.yaml  # Replace REPO-NAME with actual repository name
  destination:
    server: https://kubernetes.default.svc
    namespace: github-runners
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
