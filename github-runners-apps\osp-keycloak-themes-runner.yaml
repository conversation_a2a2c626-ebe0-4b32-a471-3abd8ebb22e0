apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: osp-keycloak-themes-runner
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "2"  # Deploy after secrets
  labels:
    app.kubernetes.io/name: github-runner
    app.kubernetes.io/component: runner
    app.kubernetes.io/part-of: github-runners
    repository: "osp-keycloak-themes"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: github-runners
    helm:
      valueFiles:
        - values-osp-keycloak-themes.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: github-runners
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3