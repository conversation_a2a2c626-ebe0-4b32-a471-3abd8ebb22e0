apiVersion: v1
kind: Secret
metadata:
  name: ospgroup-dockerhub-secret
  namespace: github-runners
  labels:
    app.kubernetes.io/name: dockerhub-secret
    app.kubernetes.io/component: secret
    app.kubernetes.io/part-of: github-runners
type: kubernetes.io/dockerconfigjson
stringData:
  .dockerconfigjson: |
    {
      "auths": {
        "dockerhub.ospgroup.vn": {
          "username": "robot$osp-public+osp-public-admin",
          "password": "jkegA78YcYu6pl6aOvOtBCh9NgZogQQM"
        }
      }
    }
