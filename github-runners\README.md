# GitHub Runners Helm Chart

Helm chart để triển khai GitHub self-hosted runners sử dụng OSP custom runner image trên Kubernetes thông qua ArgoCD.

## Tính năng

- ✅ **Scalable Repository Support**: Dễ dàng thêm runner cho repository mới
- ✅ **Multi-instance Support**: Hỗ trợ nhiều replicas với environment isolation
- ✅ **Docker-in-Docker**: Sử dụng host Docker socket cho hiệu suất tốt nhất
- ✅ **Persistent Storage**: Mỗi runner có storage riêng cho build cache
- ✅ **High Availability**: Anti-affinity và Pod Disruption Budget
- ✅ **GitOps Ready**: Tích hợp hoàn toàn với ArgoCD

## Kiến trúc

```
github-runners/
├── Chart.yaml                    # Helm chart metadata
├── values.yaml                   # Default values
├── values-template.yaml          # Template cho repo mới
├── values-k8s-deployment.yaml    # Values cho k8s-deployment repo
├── templates/
│   ├── _helpers.tpl              # Helper templates
│   ├── statefulset.yaml          # StatefulSet manifest
│   ├── service.yaml              # Headless service
│   ├── serviceaccount.yaml       # ServiceAccount
│   ├── poddisruptionbudget.yaml  # PDB for HA
│   ├── hpa.yaml                  # Horizontal Pod Autoscaler
│   └── networkpolicy.yaml        # Network policy
└── README.md                     # Documentation
```

## Cách thêm runner cho repository mới

### 1. Tạo values file mới

```bash
# Copy template
cp github-runners/values-template.yaml github-runners/values-REPO-NAME.yaml

# Chỉnh sửa file values-REPO-NAME.yaml:
# - Thay thế OWNER/REPO-NAME bằng owner/repo thực tế
# - Điều chỉnh resources, storage nếu cần
# - Thêm labels phù hợp với repository
```

### 2. Tạo ArgoCD Application

```bash
# Copy template
cp github-runners-apps/TEMPLATE-runner.yaml github-runners-apps/REPO-NAME-runner.yaml

# Chỉnh sửa file REPO-NAME-runner.yaml:
# - Thay thế REPO-NAME bằng tên repo thực tế
# - Đảm bảo valueFiles trỏ đúng file values
```

### 3. Commit và push lên Git

```bash
git add github-runners/values-REPO-NAME.yaml
git add github-runners-apps/REPO-NAME-runner.yaml
git commit -m "feat: Add GitHub runner for REPO-NAME"
git push origin main
```

ArgoCD sẽ tự động sync và tạo runner cho repository mới.

## Cấu hình

### Docker Command Equivalent

Helm chart này tương đương với Docker command:

```bash
docker run -d \
  --name osp-runner \
  -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
  -e ACCESS_TOKEN="****************************************" \
  -e RUNNER_NAME="osp-custom-runner" \
  -e RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0
```

### Các biến môi trường chính

- `REPO_URL`: URL của GitHub repository
- `ACCESS_TOKEN`: GitHub Personal Access Token (PAT)
- `RUNNER_NAME`: Tên runner (tự động tạo unique cho mỗi pod)
- `RUNNER_LABELS`: Labels cho runner (comma-separated)
- `RUNNER_WORKDIR`: Thư mục làm việc (unique cho mỗi pod)

### Storage

- Mỗi runner có 1 PVC riêng với kích thước có thể cấu hình
- PVC sử dụng `ReadWriteOnce` để đảm bảo isolation
- Mỗi pod có working directory riêng biệt để tránh xung đột

### Security

- Chạy với privileged mode để hỗ trợ Docker-in-Docker
- Sử dụng host Docker socket thay vì Docker-in-Docker container
- ServiceAccount riêng với permissions tối thiểu cần thiết

## Monitoring và Troubleshooting

### Kiểm tra trạng thái runners

```bash
# Xem pods
kubectl get pods -n github-runners

# Xem logs
kubectl logs -f <pod-name> -n github-runners

# Exec vào pod
kubectl exec -it <pod-name> -n github-runners -- /bin/bash
```

### Kiểm tra Docker functionality

```bash
# Kiểm tra Docker socket
kubectl exec -it <pod-name> -n github-runners -- /check-docker.sh

# Test Docker commands
kubectl exec -it <pod-name> -n github-runners -- docker info
kubectl exec -it <pod-name> -n github-runners -- docker run --rm hello-world
```

### Common Issues

1. **Runner không đăng ký được**: Kiểm tra ACCESS_TOKEN và REPO_URL
2. **Docker commands fail**: Kiểm tra Docker socket mount và permissions
3. **Pod crash**: Kiểm tra resources limits và storage

## Best Practices

### Resource Planning

- **Small repos**: 500m CPU, 1Gi RAM, 5Gi storage
- **Medium repos**: 1 CPU, 2Gi RAM, 10Gi storage  
- **Large repos**: 2+ CPU, 4+ Gi RAM, 20+ Gi storage

### Security

- Sử dụng dedicated nodes cho runners nếu có thể
- Cấu hình Network Policies để hạn chế traffic
- Regular rotation của GitHub tokens
- Monitor resource usage và set appropriate limits

### Scaling

- Bắt đầu với 1 replica, tăng dần theo nhu cầu
- Sử dụng HPA cho auto-scaling (experimental)
- Cân nhắc sử dụng spot instances cho cost optimization
