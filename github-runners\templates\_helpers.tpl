{{/*
Expand the name of the chart.
*/}}
{{- define "github-runners.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "github-runners.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "github-runners.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "github-runners.labels" -}}
helm.sh/chart: {{ include "github-runners.chart" . }}
{{ include "github-runners.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "github-runners.selectorLabels" -}}
app.kubernetes.io/name: {{ include "github-runners.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "github-runners.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "github-runners.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Generate runner name based on repository URL if not specified
*/}}
{{- define "github-runners.runnerName" -}}
{{- if .Values.runner.name }}
{{- .Values.runner.name }}
{{- else }}
{{- $repoUrl := .Values.repository.url | required "repository.url is required" }}
{{- $repoName := regexReplaceAll "^https://github.com/[^/]+/(.+?)(?:\\.git)?/?$" $repoUrl "${1}" }}
{{- printf "%s-runner" $repoName | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Generate unique working directory for each pod
*/}}
{{- define "github-runners.workdir" -}}
{{- printf "%s-${HOSTNAME}" .Values.runner.workdirBase }}
{{- end }}

{{/*
Docker socket volume name
*/}}
{{- define "github-runners.dockerSocketVolumeName" -}}
docker-socket
{{- end }}

{{/*
Persistence volume name
*/}}
{{- define "github-runners.persistenceVolumeName" -}}
runner-data
{{- end }}

{{/*
Generate environment variables for runner
*/}}
{{- define "github-runners.runnerEnv" -}}
- name: REPO_URL
  value: {{ .Values.repository.url | required "repository.url is required" | quote }}
- name: ACCESS_TOKEN
  value: {{ .Values.github.accessToken | required "github.accessToken is required" | quote }}
- name: RUNNER_NAME
  value: "r-{{ include "github-runners.runnerName" . }}-{{ .Release.Name | trunc 8 }}"
- name: RUNNER_LABELS
  value: {{ .Values.runner.labels | quote }}
- name: RUNNER_WORKDIR
  value: {{ include "github-runners.workdir" . | quote }}
- name: RUNNER_GROUP
  value: "default"
- name: RUNNER_SCOPE
  value: "repo"
- name: DISABLE_AUTO_DEREGISTRATION
  value: "false"
- name: RUNNER_ALLOW_RUNASROOT
  value: "1"
- name: ACTIONS_RUNNER_PRINT_LOG_TO_STDOUT
  value: "1"
- name: HOSTNAME
  valueFrom:
    fieldRef:
      fieldPath: metadata.name
{{- if .Values.env }}
{{- range .Values.env }}
- name: {{ .name }}
  value: {{ .value | quote }}
{{- end }}
{{- end }}
{{- end }}
