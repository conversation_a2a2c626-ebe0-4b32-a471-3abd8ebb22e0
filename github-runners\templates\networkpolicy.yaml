{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "github-runners.fullname" . }}
  labels:
    {{- include "github-runners.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "github-runners.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow ingress from same namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: {{ .Release.Namespace }}
    {{- with .Values.networkPolicy.ingress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  egress:
    # Allow all egress by default (runners need to access GitHub, Docker Hub, etc.)
    - {}
    {{- with .Values.networkPolicy.egress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
