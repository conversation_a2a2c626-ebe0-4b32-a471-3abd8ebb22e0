# Values for k8s-deployment repository runner
# Equivalent to: docker run -d --name osp-runner \
#   -e REPO_URL="https://github.com/ospgroupvn/k8s-deployment" \
#   -e ACCESS_TOKEN="<github personal access token>" \
#   -e RUNNER_NAME="osp-custom-runner" \
#   -e RUNNER_LABELS="self-hosted,linux,x64,docker,osp-custom" \
#   -v /var/run/docker.sock:/var/run/docker.sock \
#   dockerhub.ospgroup.vn/osp-public/osp-custom-runner:1.0

# Repository configuration
repository:
  url: "https://github.com/ospgroupvn/k8s-demoFE"

# GitHub authentication
github:
  accessToken: "****************************************"

# Runner configuration
runner:
  name: "osp-custom-runner"
  replicas: 1  # Scale down for resource optimization
  labels: "self-hosted,linux,x64,docker,osp-custom,k8s,devops"
  workdirBase: "/tmp/runner/work"

# Image configuration - OSP Custom Runner
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  tag: "1.0"
  pullPolicy: IfNotPresent

# Image pull secrets for private registries
imagePullSecrets:
  - name: ospgroup-dockerhub-secret

# Resource limits for DevOps workloads (Docker builds, kubectl, etc.)
resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "1"
    memory: "2Gi"

# Storage for build caches and artifacts
persistence:
  enabled: true
  size: 10Gi  # Larger size for Docker data and build artifacts
  storageClass: ""
  accessMode: ReadWriteOnce

# Docker socket configuration - equivalent to -v /var/run/docker.sock:/var/run/docker.sock
dockerSocket:
  enabled: true
  hostPath: /var/run/docker.sock

# Security context - required for Docker-in-Docker
securityContext:
  privileged: true
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  allowPrivilegeEscalation: true
  capabilities:
    add:
      - SYS_ADMIN

# Pod security context
podSecurityContext:
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

# Anti-affinity to spread runners across nodes for better availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - github-runners
          - key: app.kubernetes.io/instance
            operator: In
            values:
            - k8s-deployment-runner
        topologyKey: kubernetes.io/hostname

# Pod disruption budget for high availability
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Probes configuration
livenessProbe:
  enabled: true
  initialDelaySeconds: 120  # Longer delay for runner registration
  periodSeconds: 60
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: true
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

# Additional environment variables (avoid duplicates with _helpers.tpl)
env: []
  # Add custom environment variables here if needed
  # - name: CUSTOM_VAR
  #   value: "custom-value"
