# Values for osp-admin-app repository runner
# OSP Admin application

# Repository configuration
repository:
  url: "https://github.com/ospgroupvn/osp-admin-app"

# GitHub authentication
github:
  accessToken: "****************************************"

# Runner configuration
runner:
  name: "admin-app"
  replicas: 1
  labels: "self-hosted,linux,x64,docker,osp-custom,react,frontend,admin"
  workdirBase: "/tmp/runner/work"

# Image configuration - OSP Custom Runner
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  tag: "1.0"
  pullPolicy: IfNotPresent

# Image pull secrets for private registries
imagePullSecrets:
  - name: ospgroup-dockerhub-secret

# Resource configuration
resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "1"
    memory: "2Gi"

# Storage for build caches and artifacts
persistence:
  enabled: true
  size: 2Gi  # Larger size for Docker data and build artifacts
  storageClass: ""
  accessMode: ReadWriteOnce

# Security configuration
securityContext:
  runAsUser: 1000
  runAsGroup: 1000
  allowPrivilegeEscalation: true
  capabilities:
    add:
      - SYS_ADMIN

# Pod security context
podSecurityContext:
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

# Docker socket configuration for custom builds
dockerSocket:
  enabled: true
  path: "/var/run/docker.sock"

# Service account configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod annotations and labels
podAnnotations: {}
podLabels:
  app.kubernetes.io/component: runner

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Environment variables
env: []

# Volume mounts for additional storage
volumeMounts: []
volumes: []

# Liveness and readiness probes
livenessProbe:
  enabled: false

readinessProbe:
  enabled: false

# Update strategy
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
    maxSurge: 0

# Priority class
priorityClassName: ""

# Pod disruption budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1

# Horizontal Pod Autoscaler
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Network policies
networkPolicy:
  enabled: false

# Metrics and monitoring
metrics:
  enabled: false
  serviceMonitor:
    enabled: false

# Backup configuration
backup:
  enabled: false