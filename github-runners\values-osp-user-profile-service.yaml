# Values for osp-user-profile-service repository runner
# User profile microservice

# Repository configuration
repository:
  url: "https://github.com/ospgroupvn/osp-user-profile-service"

# GitHub authentication
github:
  accessToken: "****************************************"

# Runner configuration
runner:
  name: "user-profile"
  replicas: 1
  labels: "self-hosted,linux,x64,docker,osp-custom,k8s,devops,dotnet,dotnet9"
  workdirBase: "/tmp/runner/work"

# Image configuration - OSP Custom Runner with .NET 9
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner-dotnet-9
  tag: "1.0.1"
  pullPolicy: Always  # Always pull latest image to ensure updates

# Image pull secrets for private registries
imagePullSecrets:
  - name: ospgroup-dockerhub-secret

# Resource configuration for DevOps workloads (Docker builds, kubectl, etc.)
resources:
  requests:
    cpu: "100m"
    memory: "256Mi"
  limits:
    cpu: "500m"
    memory: "1Gi"

# Storage for build caches and artifacts
persistence:
  enabled: false  # Disable persistence for now to avoid scheduling issues
  size: 5Gi  # Reasonable size for Docker data and build artifacts
  storageClass: ""
  accessMode: ReadWriteOnce

# Security context - required for Docker-in-Docker
securityContext:
  privileged: true
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  allowPrivilegeEscalation: true
  capabilities:
    add:
      - SYS_ADMIN

# Pod security context - fsGroup ensures all files are owned by the right group
podSecurityContext:
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000
  supplementalGroups:
    - 1000  # actions-runner group
    - 999   # docker group (common ID)

# Docker socket configuration - equivalent to -v /var/run/docker.sock:/var/run/docker.sock
dockerSocket:
  enabled: true
  hostPath: /var/run/docker.sock

# Service account configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod annotations and labels
podAnnotations: {}
podLabels:
  app.kubernetes.io/component: runner

# Anti-affinity to spread runners across nodes for better availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - github-runners
          - key: app.kubernetes.io/instance
            operator: In
            values:
            - osp-user-profile-service-runner
        topologyKey: kubernetes.io/hostname

# Pod disruption budget for high availability
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Probes configuration
livenessProbe:
  enabled: true
  initialDelaySeconds: 120  # Longer delay for runner registration
  periodSeconds: 60
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: true
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

# Additional environment variables (avoid duplicates with _helpers.tpl)
env: []
  # Add custom environment variables here if needed
  # - name: CUSTOM_VAR
  #   value: "custom-value"

