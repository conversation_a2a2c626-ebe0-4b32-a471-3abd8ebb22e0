# Template values for new GitHub repository runners
# Copy this file and modify for each new repository
# 
# Usage:
# 1. Copy: cp values-template.yaml values-REPO-NAME.yaml
# 2. Replace OWNER/REPO-NAME with actual repository
# 3. Adjust resources, storage, and labels as needed
# 4. Create corresponding ArgoCD Application

# REQUIRED: Repository configuration
repository:
  url: "https://github.com/OWNER/REPO-NAME"  # Replace with actual repo URL

# GitHub authentication (use same token or create repo-specific one)
github:
  accessToken: "****************************************"

# Runner configuration
runner:
  name: "REPO-NAME-runner"  # Replace REPO-NAME with actual repository name
  replicas: 1  # Adjust based on repository needs
  labels: "self-hosted,linux,x64,docker,osp-custom"  # Add specific labels as needed
  workdirBase: "/tmp/runner/work"

# Image configuration - OSP Custom Runner
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  tag: "1.0"
  pullPolicy: IfNotPresent

# Image pull secrets for private registries
imagePullSecrets:
  - name: ospgroup-dockerhub-secret

# Resource configuration (adjust based on repository needs)
resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "2"
    memory: "4Gi"

# Storage configuration
persistence:
  enabled: true
  size: 5Gi  # Adjust size based on repository needs
  storageClass: ""
  accessMode: ReadWriteOnce

# Docker socket configuration
dockerSocket:
  enabled: true
  hostPath: /var/run/docker.sock

# Security context - required for Docker-in-Docker
securityContext:
  privileged: true
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  allowPrivilegeEscalation: true
  capabilities:
    add:
      - SYS_ADMIN

# Pod security context
podSecurityContext:
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

# Anti-affinity to spread runners across nodes
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - github-runners
        topologyKey: kubernetes.io/hostname

# Probes configuration
livenessProbe:
  enabled: true
  initialDelaySeconds: 120
  periodSeconds: 60
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: true
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

# Additional environment variables
env: []
  # - name: CUSTOM_VAR
  #   value: "custom-value"

# Node selector (optional)
nodeSelector: {}
  # kubernetes.io/arch: amd64

# Tolerations (optional)
tolerations: []
  # - key: "dedicated"
  #   operator: "Equal"
  #   value: "runners"
  #   effect: "NoSchedule"
