# Default values for github-runners
# This is a YAML-formatted file.

# Repository configuration
repository:
  url: ""  # Required: GitHub repository URL (e.g., https://github.com/ospgroupvn/k8s-deployment)
  
# GitHub authentication
github:
  accessToken: "****************************************"  # Default PAT token

# Runner configuration
runner:
  # Name will be auto-generated based on repository if not specified
  name: ""
  # Number of runner instances
  replicas: 1
  # Runner labels (comma-separated)
  labels: "self-hosted,linux,x64,docker,osp-custom"
  # Working directory base path - each pod gets unique workdir
  workdirBase: "/tmp/runner/work"

# Image configuration - OSP Custom Runner
image:
  repository: dockerhub.ospgroup.vn/osp-public/osp-custom-runner
  tag: "1.0"
  pullPolicy: IfNotPresent

# Image pull secrets for private registries
imagePullSecrets:
  - name: ospgroup-dockerhub-secret

# Service Account
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# Pod Security Context
podSecurityContext:
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

# Security Context
securityContext:
  # Required for Docker-in-Docker functionality
  privileged: true
  runAsNonRoot: false
  runAsUser: 1000
  runAsGroup: 1000
  allowPrivilegeEscalation: true
  capabilities:
    add:
      - SYS_ADMIN

# Resources
resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "2"
    memory: "4Gi"

# Persistence
persistence:
  enabled: true
  # Storage class name
  storageClass: ""
  # Access mode
  accessMode: ReadWriteOnce
  # Size
  size: 5Gi
  # Annotations
  annotations: {}

# Docker socket configuration
dockerSocket:
  # Mount host Docker socket
  enabled: true
  # Path to Docker socket on host
  hostPath: /var/run/docker.sock

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity - spread runners across nodes for better availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - github-runners
        topologyKey: kubernetes.io/hostname

# Pod annotations
podAnnotations: {}

# Pod labels
podLabels: {}

# Liveness probe
livenessProbe:
  enabled: true
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3
  successThreshold: 1

# Readiness probe
readinessProbe:
  enabled: true
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
  successThreshold: 1

# Environment variables
env: []
  # - name: CUSTOM_VAR
  #   value: "custom-value"

# Extra volumes
extraVolumes: []
  # - name: extra-volume
  #   emptyDir: {}

# Extra volume mounts
extraVolumeMounts: []
  # - name: extra-volume
  #   mountPath: /extra

# Pod disruption budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1
  # maxUnavailable: 1

# Horizontal Pod Autoscaler
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Network policy
networkPolicy:
  enabled: false
  # ingress: []
  # egress: []
