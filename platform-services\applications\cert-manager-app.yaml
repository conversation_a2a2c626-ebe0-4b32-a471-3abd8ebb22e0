apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cert-manager
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"  # Deploy before other platform services
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: platform-service
    app.kubernetes.io/part-of: platform-services
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://charts.jetstack.io
    chart: cert-manager
    targetRevision: v1.16.2
    helm:
      values: |
        # Global configuration
        global:
          leaderElection:
            namespace: platform
        
        # Install CRDs
        crds:
          enabled: true
          keep: true
        
        # Controller configuration
        replicaCount: 1

        # Feature gates
        featureGates: "ExperimentalGatewayAPISupport=true"

        # Resources
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi

        # Node selector for Linux nodes
        nodeSelector:
          kubernetes.io/os: linux

        # Security context
        securityContext:
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault

        containerSecurityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
        
        # Webhook configuration
        webhook:
          replicaCount: 1
          resources:
            requests:
              cpu: 10m
              memory: 32Mi
            limits:
              cpu: 100m
              memory: 128Mi

          nodeSelector:
            kubernetes.io/os: linux

          securityContext:
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault

          containerSecurityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault

        # CA Injector configuration
        cainjector:
          replicaCount: 1
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi

          nodeSelector:
            kubernetes.io/os: linux

          securityContext:
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault

          containerSecurityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
        
        # Startup API check
        startupapicheck:
          enabled: true
          securityContext:
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          
          containerSecurityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
