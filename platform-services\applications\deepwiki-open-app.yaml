apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: deepwiki-open
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: platform-services/charts/deepwiki-open
    helm:
      valueFiles:
        - values.yaml
      parameters:
        - name: global.namespace
          value: platform-services
        - name: app.image.tag
          value: latest
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m