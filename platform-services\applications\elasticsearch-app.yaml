apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: elasticsearch
  namespace: bootstrap
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git # <-- URL repo của bạn
    path: platform-services/charts/elasticsearch
    targetRevision: main
    helm:
      values: |
        # Các giá trị cho chart cha (elasticsearch)
        replicas: 1
        persistence:
          enabled: true
          storageClass: "local-path" # <-- Thay bằng StorageClass của bạn
          size: 20Gi
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"

        # Đ<PERSON><PERSON> là các giá trị sẽ được truyền xuống cho chart con 'common'
        common:
          image:
            repository: docker.elastic.co/elasticsearch/elasticsearch
            tag: "8.14.1"
          service:
            port: 9200
        
        ingress:
          enabled: false
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    # ... (giữ nguyê<PERSON> nh<PERSON> c<PERSON>)
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp