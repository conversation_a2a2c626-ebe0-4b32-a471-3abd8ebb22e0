apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: harbor
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/harbor
    targetRevision: main
    helm:
      values: |
        expose:
          type: clusterIP
          tls:
            enabled: false
          clusterIP:
            name: harbor
        
        externalURL: https://dockerhub.ospgroup.vn
        
        harborAdminPassword: "Harbor12345"
        
        database:
          type: external
          external:
            host: "postgresql.platform-services.svc.cluster.local"
            port: "5432"
            username: "postgres"
            password: "postgres123"
            coreDatabase: "harbor_core"
            notaryServerDatabase: "harbor_notary_server"
            notarySignerDatabase: "harbor_notary_signer"
            sslmode: "disable"
        
        redis:
          type: external
          external:
            addr: "redis-master.platform-services.svc.cluster.local:6379"
            password: "redis123"
        
        
        # Removed nodeSelector constraint to avoid volume mounting issues
        # nodeSelector:
        #   kubernetes.io/hostname: warehouse02
        
        core:
          # nodeSelector:
          #   kubernetes.io/hostname: warehouse02
          podAnnotations:
            kubectl.kubernetes.io/restartedAt: "2025-09-19T17:00:00Z"
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "2Gi"
              cpu: "2000m"
          # Add environment variables for timeout
          extraEnvVars:
            - name: HTTP_PROXY_TIMEOUT
              value: "3600"
            - name: REGISTRY_CLIENT_TIMEOUT
              value: "3600"
        
        registry:
          # nodeSelector:
          #   kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
        
        jobservice:
          # nodeSelector:
          #   kubernetes.io/hostname: warehouse02
          podAnnotations:
            kubectl.kubernetes.io/restartedAt: "2025-09-05T12:30:00Z"
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
        
        portal:
          # nodeSelector:
          #   kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
        
        # Nginx configuration for longer timeouts
        nginx:
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
        

  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
