apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kafka
  namespace: bootstrap
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: main
    path: platform-services/charts/kafka
    helm:
      values: |
        fullnameOverride: kafka
        global:
          storageClass: local-path
        zookeeper:
          replicaCount: 1
          persistence:
            enabled: true
            size: 8Gi
        replicaCount: 1
        persistence:
          enabled: true
          size: 10Gi
        listeners:
          client:
            protocol: SASL_PLAINTEXT
          controller:
            protocol: SASL_PLAINTEXT
          interbroker:
            protocol: SASL_PLAINTEXT
        auth:
          clientProtocol: sasl
          interBrokerProtocol: sasl
          sasl:
            mechanisms: PLAIN
            interBrokerMechanism: PLAIN
          jaas:
            clientUsers:
              - user1
            clientPasswords:
              - password1
            interBrokerUser: admin
            interBrokerPassword: admin-password
            zookeeperUser: ""
            zookeeperPassword: ""
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
