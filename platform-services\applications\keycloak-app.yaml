apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: keycloak
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/keycloak
    targetRevision: main
    helm:
      values: |
        # Global configuration
        global:
          imageRegistry: ""
          imagePullSecrets:
            - ospgroup-dockerhub-secret

        # Keycloak configuration - đúng structure cho Bitnami chart
        keycloak:
          image:
            registry: dockerhub.ospgroup.vn
            repository: osp-public/keycloak-custom-themes
            tag: "20250927-095102-9789885"
          auth:
            adminUser: "admin"
            existingSecret: "keycloak-admin"
            secretKeys:
              adminPasswordKey: "admin-password"

          postgresql:
            enabled: false

          externalDatabase:
            host: postgresql.platform-services.svc.cluster.local
            port: 5432
            user: keycloak
            database: keycloak
            existingSecret: "keycloak-database"
            existingSecretPasswordKey: "password"

          service:
            type: ClusterIP
            port: 8080

          persistence:
            enabled: true
            size: 5Gi
            storageClass: "local-path"

          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"

          metrics:
            enabled: true

          extraEnvVars:
            - name: KC_PROXY
              value: edge
            - name: KC_PROXY_HEADERS
              value: xforwarded
            - name: KC_HOSTNAME_STRICT
              value: "false"
            - name: KC_HTTP_ENABLED
              value: "true"
            - name: KC_HOSTNAME_URL
              value: "https://sso.ospgroup.io.vn"
            - name: KC_HOSTNAME_ADMIN_URL
              value: "https://sso.ospgroup.io.vn"
            - name: KC_PROXY_ADDRESS_FORWARDING
              value: "true"
            - name: KC_FEATURES
              value: "account-api"

          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: kubernetes.io/hostname
                    operator: In
                    values:
                    - warehouse02

          livenessProbe:
            enabled: true
            httpGet:
              path: /health/live
              port: http
            initialDelaySeconds: 120
            timeoutSeconds: 10
            periodSeconds: 15
            failureThreshold: 10

          readinessProbe:
            enabled: true
            httpGet:
              path: /health/ready
              port: http
            initialDelaySeconds: 120
            timeoutSeconds: 10
            periodSeconds: 15
            failureThreshold: 10

          startupProbe:
            enabled: true
            httpGet:
              path: /health/ready
              port: http
            initialDelaySeconds: 60
            timeoutSeconds: 10
            periodSeconds: 15
            failureThreshold: 20

          # Rolling Update Strategy - StatefulSet rolling update
          updateStrategy:
            type: RollingUpdate
            rollingUpdate:
              partition: 0

          # Pod Disruption Budget
          podDisruptionBudget:
            enabled: true
            minAvailable: 1

          replicaCount: 1

          # Custom themes đã được build vào image, không cần mount từ PVC

          # Theme loading config
          extraEnvVarsCM: keycloak-theme-config
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
