apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: keycloak-themes
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"  # Deploy trước keycloak
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/platform
    targetRevision: main
    directory:
      include: |
        keycloak-themes-pvc.yaml
        keycloak-theme-config.yaml
        keycloak-sso-httproute.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m