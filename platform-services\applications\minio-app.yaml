apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: minio
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/minio
    targetRevision: main
    helm:
      values: |
        global:
          minio:
            existingSecret: ""
        
        auth:
          rootUser: "admin"
          rootPassword: "minio123456"
        
        defaultBuckets: "harbor,signoz"
        
        nodeAffinityPreset:
          type: "hard"
          key: "kubernetes.io/hostname"
          values:
            - "warehouse02"
        
        persistence:
          enabled: true
          size: 50Gi
          storageClass: "local-path"
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        
        metrics:
          enabled: true
        
        service:
          type: ClusterIP
          ports:
            api: 9000
            console: 9001
        
        console:
          enabled: true
          autoscaling:
            vpa:
              enabled: true
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
