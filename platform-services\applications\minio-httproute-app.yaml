apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: minio-httproute
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services
    targetRevision: main
    directory:
      include: |
        platform/minio-middleware.yaml
        platform/minio-httproute.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m