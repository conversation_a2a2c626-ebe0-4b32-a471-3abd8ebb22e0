apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: nexus-repository-manager
  namespace: bootstrap
  labels:
    app.kubernetes.io/name: nexus-repository-manager
    app.kubernetes.io/component: argocd-application
    app.kubernetes.io/part-of: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    targetRevision: HEAD
    path: platform-services/charts/nexus-repository-manager
    helm:
      valueFiles:
        - values.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
