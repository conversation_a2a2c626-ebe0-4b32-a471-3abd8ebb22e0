apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: postgresql-18
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"  # Sync sau PostgreSQL 16 (wave -1)
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/postgresql-18
    targetRevision: main
    helm:
      values: |
        # PostgreSQL 18 Configuration - Song song với PostgreSQL 16
        global:
          postgresql:
            auth:
              postgresPassword: "postgres123"
              database: "postgres"
            service:
              ports:
                postgresql: "5433"  # Port khác với PostgreSQL 16
        
        # PostgreSQL 18 Image
        image:
          registry: docker.io
          repository: bitnami/postgresql
          tag: "18.0.0-debian-12-r0"
        
        # Authentication
        auth:
          enablePostgresUser: true
          postgresPassword: "postgres123"
        
        # Primary configuration
        primary:
          # Node affinity - chạy trên warehouse03 (khác với PostgreSQL 16)
          nodeAffinityPreset:
            type: "hard"
            key: "kubernetes.io/hostname"
            values:
              - "warehouse03"
          
          # Pod anti-affinity để tránh cùng node với PostgreSQL 16
          affinity:
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
                - weight: 100
                  podAffinityTerm:
                    labelSelector:
                      matchLabels:
                        app.kubernetes.io/name: postgresql
                    topologyKey: kubernetes.io/hostname
          
          # Service với port khác
          service:
            type: ClusterIP
            ports:
              postgresql: 5433
          
          # Persistence
          persistence:
            enabled: true
            size: 30Gi
            storageClass: "local-path"
          
          # Resources
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1"
          
          # PostgreSQL 18 specific configuration
          configuration: |
            # PostgreSQL 18 specific settings
            shared_preload_libraries = 'pg_stat_statements'
            
            # Performance tuning
            shared_buffers = 256MB
            effective_cache_size = 1GB
            maintenance_work_mem = 64MB
            checkpoint_completion_target = 0.9
            wal_buffers = 16MB
            default_statistics_target = 100
            random_page_cost = 1.1
            effective_io_concurrency = 200
            
            # Logging
            log_destination = 'stderr'
            logging_collector = on
            log_directory = 'log'
            log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
            log_statement = 'all'
            log_min_duration_statement = 1000
            
            # PostgreSQL 18 new features
            log_connections = on
            log_disconnections = on
            log_lock_waits = on
        
        # Metrics
        metrics:
          enabled: true
        
        # Architecture
        architecture: standalone
        
        # Common labels để phân biệt với PostgreSQL 16
        commonLabels:
          app.kubernetes.io/part-of: "postgresql-migration"
          migration.version: "18"
        
        # Common annotations
        commonAnnotations:
          postgresql.version: "18.0.0"
          deployment.purpose: "migration-testing"
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
