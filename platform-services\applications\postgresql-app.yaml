apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: postgresql
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/postgresql
    targetRevision: main
    helm:
      values: |
        global:
          postgresql:
            auth:
              postgresPassword: "postgres123"
              database: "postgres"
        
        primary:
          service:
            type: NodePort
            nodePorts:
              postgresql: 32000
          nodeAffinityPreset:
            type: "hard"
            key: "kubernetes.io/hostname"
            values:
              - "warehouse02"
          
          persistence:
            enabled: true
            size: 20Gi
            storageClass: "local-path"
          
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
        
        metrics:
          enabled: true
          
        architecture: standalone
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
