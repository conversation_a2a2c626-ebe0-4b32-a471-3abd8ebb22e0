apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: redis
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/redis
    targetRevision: main
    helm:
      values: |
        global:
          redis:
            password: "redis123"
        
        architecture: standalone
        
        master:
          nodeAffinityPreset:
            type: "hard"
            key: "kubernetes.io/hostname"
            values:
              - "warehouse02"
          
          persistence:
            enabled: true
            size: 8Gi
            storageClass: "local-path"
          
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "250m"
        
        metrics:
          enabled: true
          
        auth:
          enabled: true
          password: "redis123"
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
