apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: signoz
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/signoz
    targetRevision: main
    helm:
      values: |
        global:
          storageClass: "local-path"
        
        clickhouse:
          installCustomStorageClass: false
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          persistence:
            size: 20Gi
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
        
        zookeeper:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          persistence:
            size: 10Gi
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
        
        frontend:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "250m"
        
        queryService:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
        
        otelCollector:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "250m"
        
        otelCollectorMetrics:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "250m"
        
        alertmanager:
          nodeSelector:
            kubernetes.io/hostname: warehouse02
          resources:
            requests:
              memory: "128Mi"
              cpu: "50m"
            limits:
              memory: "256Mi"
              cpu: "100m"

        schemaMigrator:
          # Tắt ArgoCD hooks để tránh chạy lại migration mỗi khi sync
          upgradeHelmHooks: false
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
