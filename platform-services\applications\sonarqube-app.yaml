apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sonarqube
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/sonarqube
    targetRevision: main
    helm:
      values: |
        # Use official SonarQube Community Build with community branch plugin 25.9.0
        # Configuration is now in values.yaml of the chart
        monitoringPasscode: "b7e4c2f1a9d3e8f6c5a1b2d4e7f8c9a0"

        # Enable persistence for demo
        persistence:
          enabled: true
          size: 10Gi
          storageClass: "local-path"

        service:
          type: ClusterIP
          externalPort: 9000

        # Disable embedded PostgreSQL, use external database
        postgresql:
          enabled: false

        # External PostgreSQL configuration for new SonarQube 25.9.0 database
        jdbcOverwrite:
          enabled: true
          jdbcUrl: "***********************************************************************************"
          jdbcUsername: "sonarqube"
          jdbcSecretName: "sonarqube-database"
          jdbcSecretPasswordKey: "password"

        # Enable ingress for external access
        ingress:
          enabled: true
          hosts:
            - name: sonarqube.local
              path: /
          annotations:
            kubernetes.io/ingress.class: "traefik"

        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "6Gi"
            cpu: "2000m"

        # Configure SonarQube to work with subpath /sonarqube
        sonarWebContext: "/sonarqube"

        # Additional environment variables (merged with values.yaml)
        env:
          # Java agent configuration for Community Branch Plugin (from values.yaml)
          - name: SONAR_WEB_JAVAADDITIONALOPTS
            value: "-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-25.9.0.jar=web"
          - name: SONAR_CE_JAVAADDITIONALOPTS
            value: "-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-25.9.0.jar=ce"
          # Web context for subpath support
          - name: SONAR_WEB_CONTEXT
            value: "/sonarqube"
  
  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp