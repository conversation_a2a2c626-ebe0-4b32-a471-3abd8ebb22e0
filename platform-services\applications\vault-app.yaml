apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: vault
  namespace: bootstrap
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ospgroupvn/k8s-deployment.git
    path: platform-services/charts/vault
    targetRevision: main
    helm:
      values: |
        # Global configuration
        global:
          imageRegistry: ""
          imagePullSecrets:
            - ospgroup-dockerhub-secret

        # HashiCorp Vault configuration
        vault:
          # Global settings
          global:
            enabled: true
            tlsDisable: true

          # Injector settings (disabled for standalone deployment)
          injector:
            enabled: false

          # Server configuration
          server:
            # Image configuration
            image:
              repository: "hashicorp/vault"
              tag: "1.15.2"
              pullPolicy: IfNotPresent

            # Resources
            resources:
              requests:
                memory: 256Mi
                cpu: 250m
              limits:
                memory: 512Mi
                cpu: 500m

            # Readiness and liveness probes - disabled for initial setup
            readinessProbe:
              enabled: false
            livenessProbe:
              enabled: false

            # Service configuration
            service:
              enabled: true
              type: ClusterIP
              port: 8200
              targetPort: 8200

            # Data storage
            dataStorage:
              enabled: true
              size: 10Gi
              storageClass: "local-path"
              accessMode: ReadWriteOnce

            # Audit storage
            auditStorage:
              enabled: true
              size: 5Gi
              storageClass: "local-path"
              accessMode: ReadWriteOnce

            # Standalone mode configuration
            standalone:
              enabled: true
              config: |
                ui = true
                
                listener "tcp" {
                  tls_disable = 1
                  address = "[::]:8200"
                  cluster_address = "[::]:8201"
                }
                
                storage "file" {
                  path = "/vault/data"
                }
                
                # API address for UI
                api_addr = "http://127.0.0.1:8200"
                cluster_addr = "http://127.0.0.1:8201"
                
                # Disable mlock for containers
                disable_mlock = true

            # High Availability mode (disabled for standalone)
            ha:
              enabled: false

            # Node affinity
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                  - matchExpressions:
                    - key: kubernetes.io/hostname
                      operator: In
                      values:
                      - warehouse02

            # Update strategy
            updateStrategy:
              type: RollingUpdate
              rollingUpdate:
                maxUnavailable: 1

            # Pod disruption budget
            podDisruptionBudget:
              maxUnavailable: 1

          # UI configuration
          ui:
            enabled: true
            serviceType: "ClusterIP"
            serviceNodePort: null
            externalPort: 8200

          # CSI Provider (disabled)
          csi:
            enabled: false

          # Server-side TLS configuration
          serverTelemetry:
            serviceMonitor:
              enabled: false

  destination:
    server: https://kubernetes.default.svc
    namespace: platform-services
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp
