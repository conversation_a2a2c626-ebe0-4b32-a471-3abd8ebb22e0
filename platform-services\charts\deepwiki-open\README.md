# DeepWiki-Open Deployment Guide

## Giới thiệu
DeepWiki-Open là một AI-powered wiki generator cho GitHub, GitLab, và Bitbucket repositories. Chart này triển khai DeepWiki-Open trên Kubernetes với cấu hình subpath để truy cập qua `https://common.ospgroup.vn/wiki/`.

## <PERSON><PERSON><PERSON> cầu trước khi triển khai

### 1. API Keys
Cần có ít nhất một trong các API keys sau:

#### Google Gemini API Key (Khuyến nghị)
1. T<PERSON>y cập [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Tạo project mới hoặc chọn project hiện có
3. Enable Gemini API
4. Tạo API key mới
5. Copy API key và lưu trữ an toàn

#### OpenAI API Key
1. T<PERSON>y cập [OpenAI Platform](https://platform.openai.com/api-keys)
2. Tạo API key mới
3. Copy API key và lưu trữ an toàn

#### OpenRouter API Key (T<PERSON>y chọn)
1. <PERSON><PERSON><PERSON> cập [OpenRouter](https://openrouter.ai/keys)
2. Tạo account và API key
3. Copy API key và lưu trữ an toàn

### 2. Cập nhật Secret
Chỉnh sửa file `platform-services/platform/deepwiki-open-secrets.yaml`:

```bash
# Encode API keys thành base64
echo -n "your-actual-google-api-key" | base64
echo -n "your-actual-openai-api-key" | base64
echo -n "your-actual-openrouter-api-key" | base64

# Thay thế các giá trị placeholder trong secret file
```

## Cấu trúc Deployment

### Services
- **deepwiki-open-backend**: Backend API service (port 8001)
- **deepwiki-open-frontend**: Frontend Next.js service (port 3000)

### Gateway API Routes
- `/wiki/api/*` → Backend service (port 8001)
- `/wiki/_next/*` → Frontend service (port 3000) - Static assets
- `/wiki/static/*` → Frontend service (port 3000) - Static assets  
- `/wiki/*` → Frontend service (port 3000) - Main application

### Persistence
- **data-pvc**: Lưu trữ dữ liệu ứng dụng (10Gi)
- **logs-pvc**: Lưu trữ logs (1Gi)

### Environment Variables
- `NEXT_PUBLIC_BASE_PATH`: "/wiki" - Cấu hình subpath cho Next.js
- `NEXT_PUBLIC_SERVER_BASE_URL`: "https://common.ospgroup.vn/wiki"
- `LOG_LEVEL`: "INFO"
- `LOG_FILE_PATH`: "/app/logs/deepwiki.log"

## Triển khai

### 1. Cập nhật Secret với API keys thực tế
```bash
# Chỉnh sửa file secrets
vi platform-services/platform/deepwiki-open-secrets.yaml

# Thay thế các placeholder bằng API keys đã encode base64
```

### 2. Commit và Push
```bash
git add platform-services/
git commit -m "feat: Add DeepWiki-Open deployment configuration

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
git push origin main
```

### 3. ArgoCD sẽ tự động sync và deploy

## Truy cập ứng dụng
Sau khi deployment thành công, truy cập: `https://common.ospgroup.vn/wiki/`

## Troubleshooting

### 1. Pod không start
```bash
kubectl logs -f deployment/deepwiki-open -n platform-services
```

### 2. Kiểm tra secret
```bash
kubectl get secret deepwiki-secrets -n platform-services -o yaml
```

### 3. Kiểm tra service
```bash
kubectl get svc -n platform-services | grep deepwiki
```

### 4. Kiểm tra HTTPRoute
```bash
kubectl get httproute -n platform-services deepwiki-open-common-route -o yaml
```

## Cấu hình nâng cao

### Scaling
Để tăng số replicas:
```yaml
app:
  replicaCount: 3
```

### Resource Limits
Để tăng resource limits:
```yaml
app:
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "4Gi"
      cpu: "2000m"
```

### Storage
Để tăng storage:
```yaml
persistence:
  data:
    size: 50Gi
  logs:
    size: 5Gi
```

## Security Notes
- API keys được lưu trữ trong Kubernetes Secret
- Container chạy với non-root user (UID 1000)
- Read-only root filesystem được disable do yêu cầu của ứng dụng
- Network policies có thể được enable nếu cần thiết