apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.app.name }}
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/component: web
    app.kubernetes.io/part-of: platform-services
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.app.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ .Values.app.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ .Values.app.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/version: {{ .Chart.AppVersion }}
        app.kubernetes.io/component: web
    spec:
      {{- if .Values.app.serviceAccount.create }}
      serviceAccountName: {{ include "deepwiki-open.serviceAccountName" . }}
      {{- end }}
      {{- if .Values.app.podSecurityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.app.podSecurityContext.fsGroup }}
      {{- end }}
      containers:
      - name: {{ .Values.app.name }}
        image: "{{ .Values.app.image.repository }}:{{ .Values.app.image.tag }}"
        imagePullPolicy: {{ .Values.app.image.pullPolicy }}
        {{- if .Values.app.containerSecurityContext.enabled }}
        securityContext:
          runAsUser: {{ .Values.app.containerSecurityContext.runAsUser }}
          runAsNonRoot: {{ .Values.app.containerSecurityContext.runAsNonRoot }}
          allowPrivilegeEscalation: {{ .Values.app.containerSecurityContext.allowPrivilegeEscalation }}
          readOnlyRootFilesystem: {{ .Values.app.containerSecurityContext.readOnlyRootFilesystem }}
        {{- end }}
        ports:
        - name: backend
          containerPort: {{ .Values.app.service.targetBackendPort }}
          protocol: TCP
        - name: frontend
          containerPort: {{ .Values.app.service.targetFrontendPort }}
          protocol: TCP
        env:
        {{- range .Values.app.env }}
        - name: {{ .name }}
          {{- if .value }}
          value: {{ .value | quote }}
          {{- else if .valueFrom }}
          valueFrom:
            {{- if .valueFrom.secretKeyRef }}
            secretKeyRef:
              name: {{ .valueFrom.secretKeyRef.name }}
              key: {{ .valueFrom.secretKeyRef.key }}
              {{- if .valueFrom.secretKeyRef.optional }}
              optional: {{ .valueFrom.secretKeyRef.optional }}
              {{- end }}
            {{- end }}
          {{- end }}
        {{- end }}
        {{- if .Values.app.livenessProbe.enabled }}
        livenessProbe:
          httpGet:
            path: {{ .Values.app.livenessProbe.httpGet.path }}
            port: {{ .Values.app.livenessProbe.httpGet.port }}
          initialDelaySeconds: {{ .Values.app.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.app.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.app.livenessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.app.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.app.readinessProbe.enabled }}
        readinessProbe:
          httpGet:
            path: {{ .Values.app.readinessProbe.httpGet.path }}
            port: {{ .Values.app.readinessProbe.httpGet.port }}
          initialDelaySeconds: {{ .Values.app.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.app.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.app.readinessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.app.readinessProbe.failureThreshold }}
        {{- end }}
        resources:
          {{- toYaml .Values.app.resources | nindent 12 }}
        volumeMounts:
        {{- if .Values.persistence.data.enabled }}
        - name: data-volume
          mountPath: {{ .Values.persistence.data.mountPath }}
        {{- end }}
        {{- if .Values.persistence.logs.enabled }}
        - name: logs-volume
          mountPath: {{ .Values.persistence.logs.mountPath }}
        {{- end }}
        {{- if .Values.configMap.enabled }}
        - name: config-volume
          mountPath: /app/generator.json
          subPath: generator.json
        {{- end }}
      volumes:
      {{- if .Values.persistence.data.enabled }}
      - name: data-volume
        persistentVolumeClaim:
          claimName: {{ .Values.app.name }}-data-pvc
      {{- end }}
      {{- if .Values.persistence.logs.enabled }}
      - name: logs-volume
        persistentVolumeClaim:
          claimName: {{ .Values.app.name }}-logs-pvc
      {{- end }}
      {{- if .Values.configMap.enabled }}
      - name: config-volume
        configMap:
          name: {{ .Values.app.name }}-config
      {{- end }}
      {{- with .Values.app.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.app.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.app.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}