{{- if .Values.persistence.data.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.app.name }}-data-pvc
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: platform-services
spec:
  accessModes:
    - {{ .Values.persistence.data.accessMode }}
  {{- if .Values.persistence.data.storageClass }}
  storageClassName: {{ .Values.persistence.data.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.data.size }}
{{- end }}
---
{{- if .Values.persistence.logs.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.app.name }}-logs-pvc
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: platform-services
spec:
  accessModes:
    - {{ .Values.persistence.logs.accessMode }}
  {{- if .Values.persistence.logs.storageClass }}
  storageClassName: {{ .Values.persistence.logs.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.logs.size }}
{{- end }}