apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.app.name }}-backend
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: platform-services
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: {{ .Values.app.service.type }}
  ports:
  - port: {{ .Values.app.service.backendPort }}
    targetPort: {{ .Values.app.service.targetBackendPort }}
    protocol: TCP
    name: backend
  selector:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.app.name }}-frontend
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/component: frontend
    app.kubernetes.io/part-of: platform-services
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: {{ .Values.app.service.type }}
  ports:
  - port: {{ .Values.app.service.frontendPort }}
    targetPort: {{ .Values.app.service.targetFrontendPort }}
    protocol: TCP
    name: frontend
  selector:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}