{{- if .Values.app.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "deepwiki-open.serviceAccountName" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.app.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: serviceaccount
    app.kubernetes.io/part-of: platform-services
  {{- with .Values.app.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}