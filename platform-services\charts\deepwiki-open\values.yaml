# DeepWiki-Open Helm Chart Values
# C<PERSON>u hình cho triển khai DeepWiki-Open trên Kubernetes

# Global configuration
global:
  # Image configuration
  image:
    repository: ghcr.io/asyncfuncai/deepwiki-open
    tag: "latest"
    pullPolicy: Always

  # Namespace
  namespace: platform-services

# Application configuration
app:
  name: deepwiki-open
  
  # Replicas
  replicaCount: 1

  # Image configuration
  image:
    repository: ghcr.io/asyncfuncai/deepwiki-open
    tag: "latest"
    pullPolicy: Always

  # Service configuration
  service:
    type: ClusterIP
    # Backend API port
    backendPort: 8001
    # Frontend port  
    frontendPort: 3000
    targetBackendPort: 8001
    targetFrontendPort: 3000

  # Environment variables
  env:
    # AI API Keys (sẽ được lấy từ secret)
    - name: GOOGLE_API_KEY
      valueFrom:
        secretKeyRef:
          name: deepwiki-secrets
          key: google-api-key
    - name: OPENAI_API_KEY
      valueFrom:
        secretKeyRef:
          name: deepwiki-secrets
          key: openai-api-key
    - name: OPENROUTER_API_KEY
      valueFrom:
        secretKeyRef:
          name: deepwiki-secrets
          key: openrouter-api-key
          optional: true
    # Next.js configuration cho subdomain deployment
    - name: NEXT_PUBLIC_SERVER_BASE_URL
      value: "https://wiki.ospgroup.vn"
    # Backend API base URL configuration
    - name: SERVER_BASE_URL
      value: "https://wiki.ospgroup.vn"
    # Logging configuration - disable file logging to avoid permission issues
    - name: LOG_LEVEL
      value: "INFO"

  # Resource limits và requests
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

  # Liveness và readiness probes
  livenessProbe:
    enabled: true
    httpGet:
      path: /health
      port: 8001
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3

  readinessProbe:
    enabled: true
    httpGet:
      path: /health
      port: 8001
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

  # Security context - disable to allow write access
  podSecurityContext:
    enabled: false
    fsGroup: 1000

  containerSecurityContext:
    enabled: false
    runAsUser: 1000
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false

  # Service account
  serviceAccount:
    create: true
    name: ""
    annotations: {}

  # Node selector và affinity
  nodeSelector: {}
  affinity: {}
  tolerations: []

# Persistence configuration
persistence:
  enabled: true
  # PVC để lưu data
  data:
    enabled: true
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 10Gi
    mountPath: /root/.adalflow
  # PVC để lưu logs
  logs:
    enabled: true
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 1Gi
    mountPath: /app/logs

# Secret configuration (cần tạo manually hoặc qua external-secrets)
secrets:
  # Tên secret chứa API keys
  name: deepwiki-secrets
  # Dữ liệu secret (chỉ dùng cho dev, production nên dùng external-secrets)
  data:
    google-api-key: ""  # Cần điền giá trị thực tế
    openai-api-key: ""  # Cần điền giá trị thực tế
    openrouter-api-key: ""  # Optional

# ConfigMap configuration
configMap:
  enabled: true
  data:
    # Cấu hình cho generator.json nếu cần
    generator.json: |
      {
        "output": {
          "directory": "./docs",
          "format": "mdx",
          "publicPath": "/wiki"
        }
      }

# Ingress configuration - disable vì dùng Gateway API
ingress:
  enabled: false

# Gateway API configuration sẽ được định nghĩa riêng
gateway:
  enabled: false

# HPA configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Network policies
networkPolicy:
  enabled: false

# Pod disruption budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1

# Monitoring và observability
monitoring:
  enabled: false
  serviceMonitor:
    enabled: false