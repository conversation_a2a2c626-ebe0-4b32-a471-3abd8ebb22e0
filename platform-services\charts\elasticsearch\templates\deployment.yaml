apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "elasticsearch.fullname" . }}
  labels:
    {{- include "elasticsearch.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicas | default 1 }}
  selector:
    matchLabels:
      {{- include "elasticsearch.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "elasticsearch.selectorLabels" . | nindent 8 }}
    spec:
      containers:
      - name: elasticsearch
        image: "{{ .Values.common.image.repository }}:{{ .Values.common.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.common.image.pullPolicy | default "IfNotPresent" }}
        ports:
        - name: http
          containerPort: 9200
          protocol: TCP
        - name: transport
          containerPort: 9300
          protocol: TCP
        env:
        - name: discovery.type
          value: "single-node"
        - name: ES_JAVA_OPTS
          value: "-Xms512m -Xmx512m"
        - name: xpack.security.enabled
          value: "false"
        {{- if .Values.resources }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.enabled }}
        volumeMounts:
        - name: data
          mountPath: /usr/share/elasticsearch/data
        {{- end }}
      {{- if .Values.nodeAffinityPreset }}
      affinity:
        nodeAffinity:
          {{- if eq .Values.nodeAffinityPreset.type "hard" }}
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinityPreset.key }}
                operator: In
                values:
                {{- range .Values.nodeAffinityPreset.values }}
                - {{ . | quote }}
                {{- end }}
          {{- end }}
      {{- end }}
      {{- if .Values.persistence.enabled }}
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: elasticsearch-data
      {{- end }}