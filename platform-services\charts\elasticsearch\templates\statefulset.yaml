apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "common.fullname" . }}
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      {{- include "common.selectorLabels" . | nindent 6 }}
  serviceName: {{ include "common.fullname" . }}
  template:
    metadata:
      labels:
        {{- include "common.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.common.image.repository }}:{{ .Values.common.image.tag }}"
          imagePullPolicy: {{ .Values.common.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 9200
              protocol: TCP
          env:
            - name: discovery.type
              value: "single-node"
            - name: "ES_JAVA_OPTS"
              value: "-Xms512m -Xmx512m"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
  # ... <PERSON><PERSON><PERSON> hình volumeClaimTemplates cho persistence