apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "elasticsearch.fullname" . }}-test-connection"
  labels:
    {{- include "elasticsearch.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "elasticsearch.fullname" . }}:{{ .Values.common.service.port }}']
  restartPolicy: Never
