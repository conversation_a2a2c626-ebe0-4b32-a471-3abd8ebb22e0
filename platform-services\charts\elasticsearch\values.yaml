# Các giá trị sẽ được ghi đè bởi elasticsearch-app.yaml trong Argo CD

# Các giá trị được truyền xuống cho common subchart
common:
  nameOverride: ""
  fullnameOverride: ""
  image:
    repository: docker.elastic.co/elasticsearch/elasticsearch
    tag: "8.14.1" # Cần khớp với appVersion
    pullPolicy: IfNotPresent
  service:
    type: NodePort
    port: 9200 # Port mặc định của Elasticsearch

# Các giá trị dành riêng cho Elasticsearch
replicas: 1
clusterName: "es-cluster"
roles:
  - master
  - data
  - ingest

persistence:
  enabled: true
  storageClass: "local-path"
  size: 20Gi

resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1"

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: elasticsearch.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Autoscaling configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80