apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "harbor.core" . }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: core
    app.kubernetes.io/component: core
spec:
  replicas: {{ .Values.core.replicas }}
  revisionHistoryLimit: {{ .Values.core.revisionHistoryLimit }}
  selector:
    matchLabels:
{{ include "harbor.matchLabels" . | indent 6 }}
      component: core
  template:
    metadata:
      labels:
{{ include "harbor.labels" . | indent 8 }}
        component: core
        app.kubernetes.io/component: core
{{- if .Values.core.podLabels }}
{{ toYaml .Values.core.podLabels | indent 8 }}
{{- end }}
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/core/core-cm.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/core/core-secret.yaml") . | sha256sum }}
        checksum/secret-jobservice: {{ include (print $.Template.BasePath "/jobservice/jobservice-secrets.yaml") . | sha256sum }}
{{- if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "auto") }}
        checksum/tls: {{ include (print $.Template.BasePath "/internal/auto-tls.yaml") . | sha256sum }}
{{- else if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "manual") }}
        checksum/tls: {{ include (print $.Template.BasePath "/core/core-tls.yaml") . | sha256sum }}
{{- end }}
{{- if .Values.core.podAnnotations }}
{{ toYaml .Values.core.podAnnotations | indent 8 }}
{{- end }}
    spec:
      securityContext:
        runAsUser: 10000
        fsGroup: 10000
{{- if .Values.core.serviceAccountName }}
      serviceAccountName: {{ .Values.core.serviceAccountName }}
{{- end -}}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.core.automountServiceAccountToken | default false }}
      terminationGracePeriodSeconds: 120
{{- with .Values.core.topologySpreadConstraints}}
      topologySpreadConstraints:
{{- range . }}
      - {{ . | toYaml | indent 8 | trim }}
        labelSelector:
          matchLabels:
{{ include "harbor.matchLabels" $ | indent 12 }}
            component: core
{{- end }}
{{- end }}
      {{- with .Values.core.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: core
        image: {{ .Values.core.image.repository }}:{{ .Values.core.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        {{- if .Values.core.startupProbe.enabled }}
        startupProbe:
          httpGet:
            path: /api/v2.0/ping
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.core.containerPort" . }}
          failureThreshold: 360
          initialDelaySeconds: {{ .Values.core.startupProbe.initialDelaySeconds }}
          periodSeconds: 10
        {{- end }}
        livenessProbe:
          httpGet:
            path: /api/v2.0/ping
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.core.containerPort" . }}
          failureThreshold: 2
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v2.0/ping
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.core.containerPort" . }}
          failureThreshold: 2
          periodSeconds: 10
        envFrom:
        - configMapRef:
            name: "{{ template "harbor.core" . }}"
        - secretRef:
            name: "{{ template "harbor.core" . }}"
        env:
          - name: CORE_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ default (include "harbor.core" .) .Values.core.existingSecret }}
                key: secret
          - name: JOBSERVICE_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ default (include "harbor.jobservice" .) .Values.jobservice.existingSecret }}
                {{- if .Values.jobservice.existingSecret }}
                key: {{ .Values.jobservice.existingSecretKey }}
                {{- else }}
                key: JOBSERVICE_SECRET
                {{- end }}
          {{- if .Values.existingSecretAdminPassword }}
          - name: HARBOR_ADMIN_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ .Values.existingSecretAdminPassword }}
                key: {{ .Values.existingSecretAdminPasswordKey }}
          {{- end }}
          {{- if .Values.internalTLS.enabled }}
          - name: INTERNAL_TLS_ENABLED
            value: "true"
          - name: INTERNAL_TLS_KEY_PATH
            value: /etc/harbor/ssl/core/tls.key
          - name: INTERNAL_TLS_CERT_PATH
            value: /etc/harbor/ssl/core/tls.crt
          - name: INTERNAL_TLS_TRUST_CA_PATH
            value: /etc/harbor/ssl/core/ca.crt
          {{- end }}
          {{- if .Values.database.external.existingSecret }}
          - name: POSTGRESQL_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ .Values.database.external.existingSecret }}
                key: password
          {{- end }}
          {{- if .Values.registry.credentials.existingSecret }}
          - name: REGISTRY_CREDENTIAL_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ .Values.registry.credentials.existingSecret }}
                key: REGISTRY_PASSWD
          {{- end }}
          {{- if .Values.core.existingXsrfSecret }}
          - name: CSRF_KEY
            valueFrom:
              secretKeyRef:
                name: {{ .Values.core.existingXsrfSecret }}
                key: {{ .Values.core.existingXsrfSecretKey }}
          {{- end }}
{{- with .Values.core.extraEnvVars }}
{{- toYaml . | nindent 10 }}
{{- end }}
        {{- if not (empty .Values.containerSecurityContext) }}
        securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: {{ template "harbor.core.containerPort" . }}
        volumeMounts:
        - name: config
          mountPath: /etc/core/app.conf
          subPath: app.conf
        - name: secret-key
          mountPath: /etc/core/key
          subPath: key
        - name: token-service-private-key
          mountPath: /etc/core/private_key.pem
          subPath: tls.key
        {{- if .Values.expose.tls.enabled }}
        - name: ca-download
          mountPath: /etc/core/ca
        {{- end }}
        {{- if .Values.uaaSecretName }}
        - name: auth-ca-cert
          mountPath: /etc/core/auth-ca/auth-ca.crt
          subPath: auth-ca.crt
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: core-internal-certs
          mountPath: /etc/harbor/ssl/core
        {{- end }}
        - name: psc
          mountPath: /etc/core/token
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
{{- if .Values.core.resources }}
        resources:
{{ toYaml .Values.core.resources | indent 10 }}
{{- end }}
      volumes:
      - name: config
        configMap:
          name: {{ template "harbor.core" . }}
          items:
            - key: app.conf
              path: app.conf
      - name: secret-key
        secret:
          {{- if .Values.existingSecretSecretKey }}
          secretName: {{ .Values.existingSecretSecretKey }}
          {{- else }}
          secretName: {{ template "harbor.core" . }}
          {{- end }}
          items:
            - key: secretKey
              path: key
      - name: token-service-private-key
        secret:
          {{- if .Values.core.secretName }}
          secretName: {{ .Values.core.secretName }}
          {{- else }}
          secretName: {{ template "harbor.core" . }}
          {{- end }}
      {{- if .Values.expose.tls.enabled }}
      - name: ca-download
        secret:
        {{- if .Values.caSecretName }}
          secretName: {{ .Values.caSecretName }}
        {{- else if eq (include "harbor.autoGenCertForIngress" .) "true" }}
          secretName: "{{ template "harbor.ingress" . }}"
        {{- else if eq (include "harbor.autoGenCertForNginx" .) "true" }}
          secretName: {{ template "harbor.tlsSecretForNginx" . }}
        {{- end }}
      {{- end }}
      {{- if .Values.uaaSecretName }}
      - name: auth-ca-cert
        secret:
          secretName: {{ .Values.uaaSecretName }}
          items:
            - key: ca.crt
              path: auth-ca.crt
      {{- end }}
      {{- if .Values.internalTLS.enabled }}
      - name: core-internal-certs
        secret:
          secretName: {{ template "harbor.internalTLS.core.secretName" . }}
      {{- end }}
      - name: psc
        emptyDir: {}
      {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolume" . | indent 6 }}
      {{- end }}
    {{- with .Values.core.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.core.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.core.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if .Values.core.priorityClassName }}
      priorityClassName: {{ .Values.core.priorityClassName }}
    {{- end }}
