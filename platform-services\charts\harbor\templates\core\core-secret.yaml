{{- $existingSecret := lookup "v1" "Secret" .Release.Namespace (include "harbor.core" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "harbor.core" . }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: Opaque
data:
  {{- if not .Values.existingSecretSecretKey }}
  secretKey: {{ .Values.secretKey | b64enc | quote }}
  {{- end }}
  {{- if not .Values.core.existingSecret }}
  secret: {{ .Values.core.secret | default (include "harbor.secretKeyHelper" (dict "key" "secret" "data" $existingSecret.data)) | default (randAlphaNum 16) | b64enc | quote }}
  {{- end }}
  {{- if not .Values.core.secretName }}
  {{- $ca := genCA "harbor-token-ca" 365 }}
  tls.key: {{ .Values.core.tokenKey | default $ca.Key | b64enc | quote }}
  tls.crt: {{ .Values.core.tokenCert | default $ca.Cert | b64enc | quote }}
  {{- end }}
  {{- if not .Values.existingSecretAdminPassword }}
  HARBOR_ADMIN_PASSWORD: {{ .Values.harborAdminPassword | b64enc | quote }}
  {{- end }}
  {{- if not .Values.database.external.existingSecret }}
  POSTGRESQL_PASSWORD: {{ template "harbor.database.encryptedPassword" . }}
  {{- end }}
  {{- if not .Values.registry.credentials.existingSecret }}
  REGISTRY_CREDENTIAL_PASSWORD: {{ .Values.registry.credentials.password | b64enc | quote }}
  {{- end }}
  {{- if not .Values.core.existingXsrfSecret }}
  CSRF_KEY: {{ .Values.core.xsrfKey | default (include "harbor.secretKeyHelper" (dict "key" "CSRF_KEY" "data" $existingSecret.data)) | default (randAlphaNum 32) | b64enc | quote }}
  {{- end }}
{{- if .Values.core.configureUserSettings }}
  CONFIG_OVERWRITE_JSON: {{ .Values.core.configureUserSettings | b64enc | quote }}
{{- end }}
  {{- template "harbor.traceJaegerPassword" . }}
