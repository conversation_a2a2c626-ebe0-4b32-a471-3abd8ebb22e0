{{- if .Values.metrics.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "harbor.exporter" . }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: exporter
    app.kubernetes.io/component: exporter
spec:
  replicas: {{ .Values.exporter.replicas }}
  revisionHistoryLimit: {{ .Values.exporter.revisionHistoryLimit }}
  selector:
    matchLabels:
{{ include "harbor.matchLabels" . | indent 6 }}
      component: exporter
  template:
    metadata:
      labels:
{{ include "harbor.labels" . | indent 8 }}
        component: exporter
        app.kubernetes.io/component: exporter
{{- if .Values.exporter.podLabels }}
{{ toYaml .Values.exporter.podLabels | indent 8 }}
{{- end }}
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/exporter/exporter-cm-env.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/exporter/exporter-secret.yaml") . | sha256sum }}
{{- if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "auto") }}
        checksum/tls: {{ include (print $.Template.BasePath "/internal/auto-tls.yaml") . | sha256sum }}
{{- else if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "manual") }}
        checksum/tls: {{ include (print $.Template.BasePath "/core/core-tls.yaml") . | sha256sum }}
{{- end }}
{{- if .Values.exporter.podAnnotations }}
{{ toYaml .Values.exporter.podAnnotations | indent 8 }}
{{- end }}
    spec:
      securityContext:
        runAsUser: 10000
        fsGroup: 10000
{{- if .Values.exporter.serviceAccountName }}
      serviceAccountName: {{ .Values.exporter.serviceAccountName }}
{{- end -}}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.exporter.automountServiceAccountToken | default false }}
{{- with .Values.exporter.topologySpreadConstraints }}
      topologySpreadConstraints:
{{- range . }}
      - {{ . | toYaml | indent 8 | trim }}
        labelSelector:
          matchLabels:
{{ include "harbor.matchLabels" $ | indent 12 }}
            component: exporter
{{- end }}
{{- end }}
      containers:
      - name: exporter
        image: {{ .Values.exporter.image.repository }}:{{ .Values.exporter.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /
            port: {{ .Values.metrics.exporter.port }}
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: {{ .Values.metrics.exporter.port }}
          initialDelaySeconds: 30
          periodSeconds: 10
        args: ["-log-level", "{{ .Values.logLevel }}"]
        envFrom:
        - configMapRef:
            name: "{{ template "harbor.exporter" . }}-env"
        - secretRef:
            name: "{{ template "harbor.exporter" . }}"
        env:
        {{- if .Values.database.external.existingSecret }}
        - name: HARBOR_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.database.external.existingSecret }}
              key: password
        {{- end }}
        {{- if .Values.existingSecretAdminPassword }}
        - name: HARBOR_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.existingSecretAdminPassword }}
              key: {{ .Values.existingSecretAdminPasswordKey }}
        {{- end }}
{{- if .Values.exporter.resources }}
        resources:
{{ toYaml .Values.exporter.resources | indent 10 }}
{{- end }}
{{- with .Values.exporter.extraEnvVars }}
        env:
{{- toYaml . | nindent 10 }}
{{- end }}
        {{- if not (empty .Values.containerSecurityContext) }}
        securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: {{ .Values.metrics.exporter.port }}
        volumeMounts:
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: core-internal-certs
          mountPath: /etc/harbor/ssl/core
          # There are some metric data are collectd from harbor core.
          # When internal TLS is enabled, the Exporter need the CA file to collect these data.
        {{- end }}
      volumes:
      - name: config
        secret:
          secretName: "{{ template "harbor.exporter" . }}"
      {{- if .Values.internalTLS.enabled }}
      - name: core-internal-certs
        secret:
          secretName: {{ template "harbor.internalTLS.core.secretName" . }}
      {{- end }}
      {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolume" . | indent 6 }}
      {{- end }}
    {{- with .Values.exporter.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.exporter.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.exporter.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if .Values.exporter.priorityClassName }}
      priorityClassName: {{ .Values.exporter.priorityClassName }}
    {{- end }}
{{ end }}
