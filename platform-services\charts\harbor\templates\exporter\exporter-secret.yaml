{{- if .Values.metrics.enabled}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "harbor.exporter" . }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: Opaque
data:
{{- if not .Values.existingSecretAdminPassword }}
  HARBOR_ADMIN_PASSWORD: {{ .Values.harborAdminPassword | b64enc | quote }}
{{- end }}
{{- if not .Values.database.external.existingSecret }}
  HARBOR_DATABASE_PASSWORD: {{ template "harbor.database.encryptedPassword" . }}
{{- end }}
{{- end }}
