{{- if eq (include "harbor.autoGenCertForIngress" .) "true" }}
{{- $ca := genCA "harbor-ca" 365 }}
{{- $cert := genSignedCert .Values.expose.ingress.hosts.core nil (list .Values.expose.ingress.hosts.core) 365 $ca }}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ template "harbor.ingress" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: kubernetes.io/tls
data:
  tls.crt: {{ $cert.Cert | b64enc | quote }}
  tls.key: {{ $cert.Key | b64enc | quote }}
  ca.crt: {{ $ca.Cert | b64enc | quote }}
{{- end }}