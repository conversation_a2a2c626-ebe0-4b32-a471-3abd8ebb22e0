apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ template "harbor.jobservice" . }}-env"
  labels:
{{ include "harbor.labels" . | indent 4 }}
data:
  CORE_URL: "{{ template "harbor.coreURL" . }}"
  TOKEN_SERVICE_URL: "{{ template "harbor.tokenServiceURL" . }}"
  REGISTRY_URL: "{{ template "harbor.registryURL" . }}"
  REGISTRY_CONTROLLER_URL: "{{ template "harbor.registryControllerURL" . }}"
  REGISTRY_CREDENTIAL_USERNAME: "{{ .Values.registry.credentials.username }}"

  JOBSERVICE_WEBHOOK_JOB_MAX_RETRY: "{{ .Values.jobservice.notification.webhook_job_max_retry }}"
  JOBSERVICE_WEBHOOK_JOB_HTTP_CLIENT_TIMEOUT: "{{ .Values.jobservice.notification.webhook_job_http_client_timeout }}"

  {{- if has "jobservice" .Values.proxy.components }}
  HTTP_PROXY: "{{ .Values.proxy.httpProxy }}"
  HTTPS_PROXY: "{{ .Values.proxy.httpsProxy }}"
  NO_PROXY: "{{ template "harbor.noProxy" . }}"
  {{- end }}
  {{- if .Values.metrics.enabled}}
  METRIC_NAMESPACE: harbor
  METRIC_SUBSYSTEM: jobservice
  {{- end }}
  {{- template "harbor.traceEnvsForJobservice" . }}
  {{- if .Values.cache.enabled }}
  _REDIS_URL_CORE: "{{ template "harbor.redis.urlForCore" . }}"
  CACHE_ENABLED: "true"
  CACHE_EXPIRE_HOURS: "{{ .Values.cache.expireHours }}"
  {{- end }}
  {{- if or (and (eq .Values.redis.type "internal") .Values.redis.internal.cacheLayerDatabaseIndex) (and (eq .Values.redis.type "external") .Values.redis.external.cacheLayerDatabaseIndex) }}
  _REDIS_URL_CACHE_LAYER: "{{ template "harbor.redis.urlForCache" . }}"
  {{- end }}
