apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ template "harbor.jobservice" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
data:
  config.yml: |+
    #Server listening port
    protocol: "{{ template "harbor.component.scheme" . }}"
    port: {{ template "harbor.jobservice.containerPort". }}
    {{- if .Values.internalTLS.enabled }}
    https_config:
      cert: "/etc/harbor/ssl/jobservice/tls.crt"
      key: "/etc/harbor/ssl/jobservice/tls.key"
    {{- end }}
    worker_pool:
      workers: {{ .Values.jobservice.maxJobWorkers }}
      backend: "redis"
      redis_pool:
        redis_url: "{{ template "harbor.redis.urlForJobservice" . }}"
        namespace: "harbor_job_service_namespace"
        idle_timeout_second: 3600
    job_loggers:
      {{- if has "file" .Values.jobservice.jobLoggers }}
      - name: "FILE"
        level: {{ .Values.logLevel | upper }}
        settings: # Customized settings of logger
          base_dir: "/var/log/jobs"
        sweeper:
          duration: {{ .Values.jobservice.loggerSweeperDuration }} #days
          settings: # Customized settings of sweeper
            work_dir: "/var/log/jobs"
      {{- end }}
      {{- if has "database" .Values.jobservice.jobLoggers }}
      - name: "DB"
        level: {{ .Values.logLevel | upper }}
        sweeper:
          duration: {{ .Values.jobservice.loggerSweeperDuration }} #days
      {{- end }}
      {{- if has "stdout" .Values.jobservice.jobLoggers }}
      - name: "STD_OUTPUT"
        level: {{ .Values.logLevel | upper }}
      {{- end }}
    metric:
      enabled: {{ .Values.metrics.enabled }}
      path: {{ .Values.metrics.jobservice.path }}
      port: {{ .Values.metrics.jobservice.port }}
    #Loggers for the job service
    loggers:
      - name: "STD_OUTPUT"
        level: {{ .Values.logLevel | upper }}
    reaper:
      # the max time to wait for a task to finish, if unfinished after max_update_hours, the task will be mark as error, but the task will continue to run, default value is 24
      max_update_hours: {{ .Values.jobservice.reaper.max_update_hours }}
      # the max time for execution in running state without new task created
      max_dangling_hours: {{ .Values.jobservice.reaper.max_dangling_hours }}
