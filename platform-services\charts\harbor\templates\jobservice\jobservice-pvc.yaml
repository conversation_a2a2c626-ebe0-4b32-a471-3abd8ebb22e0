{{- $jobLog := .Values.persistence.persistentVolumeClaim.jobservice.jobLog -}}
{{- if and .Values.persistence.enabled (not $jobLog.existingClaim) (has "file" .Values.jobservice.jobLoggers) }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ template "harbor.jobservice" . }}
  annotations:
  {{- range $key, $value := $jobLog.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- if eq .Values.persistence.resourcePolicy "keep" }}
    helm.sh/resource-policy: keep
  {{- end }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: jobservice
    app.kubernetes.io/component: jobservice
spec:
  accessModes: 
    - {{ $jobLog.accessMode }}
  resources:
    requests:
      storage: {{ $jobLog.size }}
  {{- if $jobLog.storageClass }}
    {{- if eq "-" $jobLog.storageClass }}
  storageClassName: ""
    {{- else }}
  storageClassName: {{ $jobLog.storageClass }}
    {{- end }}
  {{- end }}
{{- end }}
