{{- $existingSecret := lookup "v1" "Secret" .Release.Namespace (include "harbor.jobservice" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ template "harbor.jobservice" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: Opaque
data:
  {{- if not .Values.jobservice.existingSecret }}
  JOBSERVICE_SECRET: {{ .Values.jobservice.secret | default (include "harbor.secretKeyHelper" (dict "key" "JOBSERVICE_SECRET" "data" $existingSecret.data)) | default (randAlphaNum 16) | b64enc | quote }}
  {{- end }}
  {{- if not .Values.registry.credentials.existingSecret }}
  REGISTRY_CREDENTIAL_PASSWORD: {{ .Values.registry.credentials.password | b64enc | quote }}
  {{- end }}
  {{- template "harbor.traceJaegerPassword" . }}
