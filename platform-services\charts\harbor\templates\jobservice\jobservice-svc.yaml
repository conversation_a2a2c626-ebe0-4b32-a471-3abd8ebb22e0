apiVersion: v1
kind: Service
metadata:
  name: "{{ template "harbor.jobservice" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
spec:
  ports:
    - name: {{ ternary "https-jobservice" "http-jobservice" .Values.internalTLS.enabled }}
      port: {{ template "harbor.jobservice.servicePort" . }}
      targetPort: {{ template "harbor.jobservice.containerPort" . }}
{{- if .Values.metrics.enabled }}
    - name: {{ template "harbor.metricsPortName" . }}
      port: {{ .Values.metrics.jobservice.port }}
{{- end }}
  selector:
{{ include "harbor.matchLabels" . | indent 4 }}
    component: jobservice
