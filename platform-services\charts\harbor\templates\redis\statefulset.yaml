{{- if eq .Values.redis.type "internal" -}}
{{- $redis := .Values.persistence.persistentVolumeClaim.redis -}}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "harbor.redis" . }}
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: redis
    app.kubernetes.io/component: redis
spec:
  replicas: 1
  serviceName: {{ template "harbor.redis" . }}
  selector:
    matchLabels:
{{ include "harbor.matchLabels" . | indent 6 }}
      component: redis
  template:
    metadata:
      labels:
{{ include "harbor.labels" . | indent 8 }}
        component: redis
        app.kubernetes.io/component: redis
{{- if .Values.redis.podLabels }}
{{ toYaml .Values.redis.podLabels | indent 8 }}
{{- end }}
{{- if .Values.redis.podAnnotations }}
      annotations:
{{ toYaml .Values.redis.podAnnotations | indent 8 }}
{{- end }}
    spec:
      securityContext:
        runAsUser: 999
        fsGroup: 999
{{- if .Values.redis.internal.serviceAccountName }}
      serviceAccountName: {{ .Values.redis.internal.serviceAccountName }}
{{- end -}}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.redis.internal.automountServiceAccountToken | default false }}
      terminationGracePeriodSeconds: 120
      {{- with .Values.redis.internal.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: redis
        image: {{ .Values.redis.internal.image.repository }}:{{ .Values.redis.internal.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        {{- if not (empty .Values.containerSecurityContext) }}
        securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 10 }}
        {{- end }}
        livenessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 1
          periodSeconds: 10
{{- if .Values.redis.internal.resources }}
        resources:
{{ toYaml .Values.redis.internal.resources | indent 10 }}
{{- end }}
{{- with .Values.redis.internal.extraEnvVars }}
        env:
{{- toYaml . | nindent 10 }}
{{- end }}
        volumeMounts:
        - name: data
          mountPath: /var/lib/redis
          subPath: {{ $redis.subPath }}
      {{- if not .Values.persistence.enabled }}
      volumes:
      - name: data
        emptyDir: {}
      {{- else if $redis.existingClaim }}
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: {{ $redis.existingClaim }}
      {{- end -}}
    {{- with .Values.redis.internal.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.redis.internal.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.redis.internal.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if .Values.redis.internal.priorityClassName }}
      priorityClassName: {{ .Values.redis.internal.priorityClassName }}
    {{- end }}
  {{- if and .Values.persistence.enabled (not $redis.existingClaim) }}
  volumeClaimTemplates:
  - metadata:
      name: data
      labels:
{{ include "harbor.legacy.labels" . | indent 8 }}
      annotations:
      {{- range $key, $value := $redis.annotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    spec:
      accessModes: [{{ $redis.accessMode | quote }}]
      {{- if $redis.storageClass }}
      {{- if (eq "-" $redis.storageClass) }}
      storageClassName: ""
      {{- else }}
      storageClassName: "{{ $redis.storageClass }}"
      {{- end }}
      {{- end }}
      resources:
        requests:
          storage: {{ $redis.size | quote }}
  {{- end -}}
  {{- end -}}
