{{- $storage := .Values.persistence.imageChartStorage }}
{{- $type := $storage.type }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ template "harbor.registry" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: registry
    app.kubernetes.io/component: registry
spec:
  replicas: {{ .Values.registry.replicas }}
  revisionHistoryLimit: {{ .Values.registry.revisionHistoryLimit }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "Recreate" }}
    rollingUpdate: null
    {{- end }}
  selector:
    matchLabels:
{{ include "harbor.matchLabels" . | indent 6 }}
      component: registry
  template:
    metadata:
      labels:
{{ include "harbor.labels" . | indent 8 }}
        component: registry
        app.kubernetes.io/component: registry
{{- if .Values.registry.podLabels }}
{{ toYaml .Values.registry.podLabels | indent 8 }}
{{- end }}
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/registry/registry-cm.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/registry/registry-secret.yaml") . | sha256sum }}
        checksum/secret-jobservice: {{ include (print $.Template.BasePath "/jobservice/jobservice-secrets.yaml") . | sha256sum }}
        checksum/secret-core: {{ include (print $.Template.BasePath "/core/core-secret.yaml") . | sha256sum }}
{{- if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "auto") }}
        checksum/tls: {{ include (print $.Template.BasePath "/internal/auto-tls.yaml") . | sha256sum }}
{{- else if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "manual") }}
        checksum/tls: {{ include (print $.Template.BasePath "/registry/registry-tls.yaml") . | sha256sum }}
{{- end }}
{{- if .Values.registry.podAnnotations }}
{{ toYaml .Values.registry.podAnnotations | indent 8 }}
{{- end }}
    spec:
      securityContext:
        runAsUser: 10000
        fsGroup: 10000
        fsGroupChangePolicy: OnRootMismatch
{{- if .Values.registry.serviceAccountName }}
      serviceAccountName: {{ .Values.registry.serviceAccountName }}
{{- end -}}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.registry.automountServiceAccountToken | default false }}
      terminationGracePeriodSeconds: 120
{{- with .Values.registry.topologySpreadConstraints}}
      topologySpreadConstraints:
{{- range . }}
      - {{ . | toYaml | indent 8 | trim }}
        labelSelector:
          matchLabels:
{{ include "harbor.matchLabels" $ | indent 12 }}
            component: registry
{{- end }}
{{- end }}
      {{- with .Values.registry.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: registry
        image: {{ .Values.registry.registry.image.repository }}:{{ .Values.registry.registry.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registry.containerPort" . }}
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registry.containerPort" . }}
          initialDelaySeconds: 1
          periodSeconds: 10
{{- if .Values.registry.registry.resources }}
        resources:
{{ toYaml .Values.registry.registry.resources | indent 10 }}
{{- end }}
        {{- if not (empty .Values.containerSecurityContext) }}
        securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 10 }}
        {{- end }}
        args: ["serve", "/etc/registry/config.yml"]
        envFrom:
        - secretRef:
            name: "{{ template "harbor.registry" . }}"
        {{- if .Values.persistence.imageChartStorage.s3.existingSecret }}
        - secretRef:
            name: {{ .Values.persistence.imageChartStorage.s3.existingSecret }}
        {{- end }}
        env:
        {{- if .Values.registry.existingSecret }}
        - name: REGISTRY_HTTP_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.registry.existingSecret }}
              key: {{ .Values.registry.existingSecretKey }}
        {{- end }}
        {{- if has "registry" .Values.proxy.components }}
        - name: HTTP_PROXY
          value: "{{ .Values.proxy.httpProxy }}"
        - name: HTTPS_PROXY
          value: "{{ .Values.proxy.httpsProxy }}"
        - name: NO_PROXY
          value: "{{ template "harbor.noProxy" . }}"
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: INTERNAL_TLS_ENABLED
          value: "true"
        - name: INTERNAL_TLS_KEY_PATH
          value: /etc/harbor/ssl/registry/tls.key
        - name: INTERNAL_TLS_CERT_PATH
          value: /etc/harbor/ssl/registry/tls.crt
        - name: INTERNAL_TLS_TRUST_CA_PATH
          value: /etc/harbor/ssl/registry/ca.crt
        {{- end }}
        {{- if .Values.redis.external.existingSecret }}
        - name: REGISTRY_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.redis.external.existingSecret }}
              key: REDIS_PASSWORD
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.azure.existingSecret }}
        - name: REGISTRY_STORAGE_AZURE_ACCOUNTKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.azure.existingSecret }}
              key: AZURE_STORAGE_ACCESS_KEY
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.swift.existingSecret }}
        - name: REGISTRY_STORAGE_SWIFT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_PASSWORD
        - name: REGISTRY_STORAGE_SWIFT_SECRETKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_SECRETKEY
              optional: true
        - name: REGISTRY_STORAGE_SWIFT_ACCESSKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_ACCESSKEY
              optional: true
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.oss.existingSecret }}
        - name: REGISTRY_STORAGE_OSS_ACCESSKEYSECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.oss.existingSecret }}
              key: REGISTRY_STORAGE_OSS_ACCESSKEYSECRET
              optional: true
        {{- end}}
{{- with .Values.registry.registry.extraEnvVars }}
{{- toYaml . | nindent 8 }}
{{- end }}
        ports:
        - containerPort: {{ template "harbor.registry.containerPort" . }}
        - containerPort: {{ ternary .Values.metrics.registry.port 5001 .Values.metrics.enabled }}
        volumeMounts:
        - name: registry-data
          mountPath: {{ .Values.persistence.imageChartStorage.filesystem.rootdirectory }}
          subPath: {{ .Values.persistence.persistentVolumeClaim.registry.subPath }}
        - name: registry-htpasswd
          mountPath: /etc/registry/passwd
          subPath: passwd
        - name: registry-config
          mountPath: /etc/registry/config.yml
          subPath: config.yml
        {{- if .Values.internalTLS.enabled }}
        - name: registry-internal-certs
          mountPath: /etc/harbor/ssl/registry
        {{- end }}
        {{- if and (and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs")) (not .Values.persistence.imageChartStorage.gcs.useWorkloadIdentity) }}
        - name: gcs-key
          mountPath: /etc/registry/gcs-key.json
          subPath: gcs-key.json
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
        - name: storage-service-ca
          mountPath: /harbor_cust_cert/custom-ca-bundle.crt
          subPath: ca.crt
        {{- end }}
        {{- if .Values.registry.middleware.enabled }}
        {{- if eq .Values.registry.middleware.type "cloudFront" }}
        - name: cloudfront-key
          mountPath: /etc/registry/pk.pem
          subPath: pk.pem
        {{- end }}
        {{- end }}
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
      - name: registryctl
        image: {{ .Values.registry.controller.image.repository }}:{{ .Values.registry.controller.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /api/health
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registryctl.containerPort" . }}
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registryctl.containerPort" . }}
          initialDelaySeconds: 1
          periodSeconds: 10
{{- if .Values.registry.controller.resources }}
        resources:
{{ toYaml .Values.registry.controller.resources | indent 10 }}
{{- end }}
        {{- if not (empty .Values.containerSecurityContext) }}
        securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 10 }}
        {{- end }}
        envFrom:
        - configMapRef:
            name: "{{ template "harbor.registryCtl" . }}"
        - secretRef:
            name: "{{ template "harbor.registry" . }}"
        - secretRef:
            name: "{{ template "harbor.registryCtl" . }}"
        {{- if .Values.persistence.imageChartStorage.s3.existingSecret }}
        - secretRef:
            name: {{ .Values.persistence.imageChartStorage.s3.existingSecret }}
        {{- end }}
        env:
        {{- if .Values.registry.existingSecret }}
        - name: REGISTRY_HTTP_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.registry.existingSecret }}
              key: {{ .Values.registry.existingSecretKey }}
        {{- end }}
        - name: CORE_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ default (include "harbor.core" .) .Values.core.existingSecret }}
              key: secret
        - name: JOBSERVICE_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ default (include "harbor.jobservice" .) .Values.jobservice.existingSecret }}
              {{- if .Values.jobservice.existingSecret }}
              key: {{ .Values.jobservice.existingSecretKey }}
              {{- else }}
              key: JOBSERVICE_SECRET
              {{- end }}
        {{- if has "registry" .Values.proxy.components }}
        - name: HTTP_PROXY
          value: "{{ .Values.proxy.httpProxy }}"
        - name: HTTPS_PROXY
          value: "{{ .Values.proxy.httpsProxy }}"
        - name: NO_PROXY
          value: "{{ template "harbor.noProxy" . }}"
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: INTERNAL_TLS_ENABLED
          value: "true"
        - name: INTERNAL_TLS_KEY_PATH
          value: /etc/harbor/ssl/registry/tls.key
        - name: INTERNAL_TLS_CERT_PATH
          value: /etc/harbor/ssl/registry/tls.crt
        - name: INTERNAL_TLS_TRUST_CA_PATH
          value: /etc/harbor/ssl/registry/ca.crt
        {{- end }}
        {{- if .Values.redis.external.existingSecret }}
        - name: REGISTRY_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.redis.external.existingSecret }}
              key: REDIS_PASSWORD
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.azure.existingSecret }}
        - name: REGISTRY_STORAGE_AZURE_ACCOUNTKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.azure.existingSecret }}
              key: AZURE_STORAGE_ACCESS_KEY
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.swift.existingSecret }}
        - name: REGISTRY_STORAGE_SWIFT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_PASSWORD
        - name: REGISTRY_STORAGE_SWIFT_SECRETKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_SECRETKEY
              optional: true
        - name: REGISTRY_STORAGE_SWIFT_ACCESSKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.swift.existingSecret }}
              key: REGISTRY_STORAGE_SWIFT_ACCESSKEY
              optional: true
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.oss.existingSecret }}
        - name: REGISTRY_STORAGE_OSS_ACCESSKEYSECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.persistence.imageChartStorage.oss.existingSecret }}
              key: REGISTRY_STORAGE_OSS_ACCESSKEYSECRET
              optional: true
        {{- end}}
{{- with .Values.registry.controller.extraEnvVars }}
{{- toYaml . | nindent 8 }}
{{- end }}
        ports:
        - containerPort: {{ template "harbor.registryctl.containerPort" . }}
        volumeMounts:
        - name: registry-data
          mountPath: {{ .Values.persistence.imageChartStorage.filesystem.rootdirectory }}
          subPath: {{ .Values.persistence.persistentVolumeClaim.registry.subPath }}
        - name: registry-config
          mountPath: /etc/registry/config.yml
          subPath: config.yml
        - name: registry-config
          mountPath: /etc/registryctl/config.yml
          subPath: ctl-config.yml
        {{- if .Values.internalTLS.enabled }}
        - name: registry-internal-certs
          mountPath: /etc/harbor/ssl/registry
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
        - name: storage-service-ca
          mountPath: /harbor_cust_cert/custom-ca-bundle.crt
          subPath: ca.crt
        {{- end }}
        {{- if and (and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs")) (not .Values.persistence.imageChartStorage.gcs.useWorkloadIdentity ) }}
        - name: gcs-key
          mountPath: /etc/registry/gcs-key.json
          subPath: gcs-key.json
        {{- end }}
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
      volumes:
      - name: registry-htpasswd
        secret:
          {{- if not .Values.registry.credentials.existingSecret }}
          secretName: {{ template "harbor.registry" . }}-htpasswd
          {{ else }}
          secretName: {{ .Values.registry.credentials.existingSecret }}
          {{- end }}
          items:
            - key: REGISTRY_HTPASSWD
              path: passwd
      - name: registry-config
        configMap:
          name: "{{ template "harbor.registry" . }}"
      - name: registry-data
      {{- if and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "filesystem") }}
        persistentVolumeClaim:
          claimName: {{ .Values.persistence.persistentVolumeClaim.registry.existingClaim | default (include "harbor.registry" .) }}
      {{- else }}
        emptyDir: {}
      {{- end }}
      {{- if .Values.internalTLS.enabled }}
      - name: registry-internal-certs
        secret:
          secretName: {{ template "harbor.internalTLS.registry.secretName" . }}
      {{- end }}
      {{- if and (and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs")) (not .Values.persistence.imageChartStorage.gcs.useWorkloadIdentity ) }}
      - name: gcs-key
        secret:
          {{- if and (eq $type "gcs") $storage.gcs.existingSecret }}
          secretName: {{ $storage.gcs.existingSecret }}
          {{- else }}
          secretName: {{ template "harbor.registry" . }}
          {{- end }}
          items:
            - key: GCS_KEY_DATA
              path: gcs-key.json
      {{- end }}
      {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
      - name: storage-service-ca
        secret:
          secretName: {{ .Values.persistence.imageChartStorage.caBundleSecretName }}
      {{- end }}
      {{- if .Values.registry.middleware.enabled }}
      {{- if eq .Values.registry.middleware.type "cloudFront" }}
      - name: cloudfront-key
        secret:
          secretName: {{ .Values.registry.middleware.cloudFront.privateKeySecret }}
          items:
            - key: CLOUDFRONT_KEY_DATA
              path: pk.pem
      {{- end }}
      {{- end }}
      {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolume" . | indent 6 }}
      {{- end }}
    {{- with .Values.registry.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.registry.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.registry.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if .Values.registry.priorityClassName }}
      priorityClassName: {{ .Values.registry.priorityClassName }}
    {{- end }}
