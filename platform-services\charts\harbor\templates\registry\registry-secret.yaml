{{- $existingSecret := lookup "v1" "Secret" .Release.Namespace (include "harbor.registry" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ template "harbor.registry" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: Opaque
data:
  {{- if not .Values.registry.existingSecret }}
  REGISTRY_HTTP_SECRET: {{ .Values.registry.secret | default (include "harbor.secretKeyHelper" (dict "key" "REGISTRY_HTTP_SECRET" "data" $existingSecret.data)) | default (randAlphaNum 16) | b64enc | quote }}
  {{- end }}
  {{- if not .Values.redis.external.existingSecret }}
  REGISTRY_REDIS_PASSWORD: {{ include "harbor.redis.password" . | b64enc | quote }}
  {{- end }}
  {{- $storage := .Values.persistence.imageChartStorage }}
  {{- $type := $storage.type }}
  {{- if and (eq $type "azure") (not $storage.azure.existingSecret) }}
  REGISTRY_STORAGE_AZURE_ACCOUNTKEY: {{ $storage.azure.accountkey | b64enc | quote }}
  {{- else if and (and (eq $type "gcs") (not $storage.gcs.existingSecret)) (not $storage.gcs.useWorkloadIdentity) }}
  GCS_KEY_DATA: {{ $storage.gcs.encodedkey | quote }}
  {{- else if eq $type "s3" }}
  {{- if and (not $storage.s3.existingSecret) ($storage.s3.accesskey) }}
  REGISTRY_STORAGE_S3_ACCESSKEY: {{ $storage.s3.accesskey | b64enc | quote }}
  {{- end }}
  {{- if and (not $storage.s3.existingSecret) ($storage.s3.secretkey) }}
  REGISTRY_STORAGE_S3_SECRETKEY: {{ $storage.s3.secretkey | b64enc | quote }}
  {{- end }}
  {{- else if and (eq $type "swift") (not ($storage.swift.existingSecret)) }}
  REGISTRY_STORAGE_SWIFT_PASSWORD: {{ $storage.swift.password | b64enc | quote }}
  {{- if $storage.swift.secretkey }}
  REGISTRY_STORAGE_SWIFT_SECRETKEY: {{ $storage.swift.secretkey | b64enc | quote }}
  {{- end }}
  {{- if $storage.swift.accesskey }}
  REGISTRY_STORAGE_SWIFT_ACCESSKEY: {{ $storage.swift.accesskey | b64enc | quote }}
  {{- end }}
  {{- else if and (eq $type "oss") ((not ($storage.oss.existingSecret))) }}
  REGISTRY_STORAGE_OSS_ACCESSKEYSECRET: {{ $storage.oss.accesskeysecret | b64enc | quote }}
  {{- end }}
{{- if not .Values.registry.credentials.existingSecret }}
---
apiVersion: v1
kind: Secret
metadata:
  name: "{{ template "harbor.registry" . }}-htpasswd"
  labels:
{{ include "harbor.labels" . | indent 4 }}
type: Opaque
data:
  {{- if .Values.registry.credentials.htpasswdString }}
  REGISTRY_HTPASSWD: {{ .Values.registry.credentials.htpasswdString | b64enc | quote }}
  {{- else }}
  REGISTRY_HTPASSWD: {{ htpasswd .Values.registry.credentials.username .Values.registry.credentials.password | b64enc | quote }}
  {{- end }}
{{- end }}
