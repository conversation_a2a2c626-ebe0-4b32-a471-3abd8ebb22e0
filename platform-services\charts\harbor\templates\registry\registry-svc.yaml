apiVersion: v1
kind: Service
metadata:
  name: "{{ template "harbor.registry" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
spec:
  ports:
    - name: {{ ternary "https-registry" "http-registry" .Values.internalTLS.enabled }}
      port: {{ template "harbor.registry.servicePort" . }}

    - name: {{ ternary "https-controller" "http-controller" .Values.internalTLS.enabled }}
      port: {{ template "harbor.registryctl.servicePort" . }}
{{- if .Values.metrics.enabled}}
    - name: {{ template "harbor.metricsPortName" . }}
      port: {{ .Values.metrics.registry.port }}
{{- end }}
  selector:
{{ include "harbor.matchLabels" . | indent 4 }}
    component: registry