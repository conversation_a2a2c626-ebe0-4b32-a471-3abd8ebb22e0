{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $replicaCount := int .Values.broker.replicaCount }}
{{- if gt $replicaCount 0 }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-broker-headless" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.externalAccess.broker.service.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: broker
    app.kubernetes.io/part-of: kafka
  {{- if or .Values.service.headless.broker.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.broker.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    - name: tcp-interbroker
      port: {{ .Values.service.ports.interbroker }}
      protocol: TCP
      targetPort: interbroker
    - name: tcp-client
      port: {{ .Values.service.ports.client }}
      protocol: TCP
      targetPort: client
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.broker.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: broker
    app.kubernetes.io/part-of: kafka
{{- end }}
