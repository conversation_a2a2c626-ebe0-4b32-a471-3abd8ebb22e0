{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $replicaCount := int .Values.controller.replicaCount }}
{{- if and .Values.kraft.enabled (include "kafka.controller.createConfigmap" .) (gt $replicaCount 0)}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-controller-configuration" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  {{- if or .Values.config .Values.controller.config }}
  server.properties: {{- include "common.tplvalues.render" ( dict "value" (coalesce .Values.controller.config .Values.config) "context" $ ) | nindent 4 }}
  {{- else }}
  server.properties: |-
    # Listeners configuration
    listeners={{ include "kafka.listeners" ( dict "isController" true "context" $ ) }}
    {{- if not .Values.controller.controllerOnly }}
    advertised.listeners={{ include "kafka.advertisedListeners" . }}
    {{- end }}
    listener.security.protocol.map={{ include "kafka.securityProtocolMap" . }}
    {{- if .Values.kraft.enabled }}
    # KRaft process roles
    process.roles={{ ternary "controller" "controller,broker" .Values.controller.controllerOnly }}
    {{- include "kafka.kraftConfig" . | nindent 4 }}
    {{- end }}
    {{- if or .Values.zookeeper.enabled .Values.externalZookeeper.servers }}
    # Zookeeper configuration
    zookeeper.metadata.migration.enable=true
    inter.broker.protocol.version=3.4
    inter.broker.protocol.version={{ default (regexFind "^[0-9].[0-9]+" .Chart.AppVersion) .Values.interBrokerProtocolVersion }}
    {{- include "kafka.zookeeperConfig" . | nindent 4 }}
    {{- end }}
    # Kafka data logs directory
    log.dir={{ printf "%s/data" .Values.controller.persistence.mountPath }}
    # Kafka application logs directory
    logs.dir={{ .Values.controller.logPersistence.mountPath }}
    {{- include "kafka.commonConfig" . | nindent 4 }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.extraConfig "context" $ ) | nindent 4 }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.controller.extraConfig "context" $ ) | nindent 4 }}
  {{- end }}
{{- end }}
