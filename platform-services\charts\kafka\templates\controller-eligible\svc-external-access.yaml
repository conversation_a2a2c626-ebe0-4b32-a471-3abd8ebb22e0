{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.kraft.enabled .Values.externalAccess.enabled }}
{{- $fullname := include "common.names.fullname" . }}
{{- if or .Values.externalAccess.controller.forceExpose (not .Values.controller.controllerOnly)}}
{{- $replicaCount := .Values.controller.replicaCount | int }}
{{- range $i := until $replicaCount }}
{{- $targetPod := printf "%s-controller-%d" $fullname $i }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-controller-%d-external" $fullname $i | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" $ | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list $.Values.externalAccess.controller.service.labels $.Values.commonLabels ) "context" $ ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: kafka
    pod: {{ $targetPod }}
  {{- if or $.Values.externalAccess.controller.service.annotations $.Values.commonAnnotations $.Values.externalAccess.controller.service.loadBalancerAnnotations }}
  annotations:
    {{- if and (not (empty $.Values.externalAccess.controller.service.loadBalancerAnnotations)) (eq (len $.Values.externalAccess.controller.service.loadBalancerAnnotations) $replicaCount) }}
    {{ include "common.tplvalues.render" ( dict "value" (index $.Values.externalAccess.controller.service.loadBalancerAnnotations $i) "context" $) | nindent 4 }}
    {{- end }}
    {{- if or $.Values.externalAccess.controller.service.annotations $.Values.commonAnnotations }}
    {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list $.Values.externalAccess.controller.service.annotations $.Values.commonAnnotations ) "context" $ ) }}
    {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: {{ $.Values.externalAccess.controller.service.type }}
  {{- if eq $.Values.externalAccess.controller.service.type "LoadBalancer" }}
  allocateLoadBalancerNodePorts: {{  $.Values.externalAccess.controller.service.allocateLoadBalancerNodePorts }}
  {{- if and (not (empty $.Values.externalAccess.controller.service.loadBalancerIPs)) (eq (len $.Values.externalAccess.controller.service.loadBalancerIPs) $replicaCount) }}
  loadBalancerIP: {{ index $.Values.externalAccess.controller.service.loadBalancerIPs $i }}
  {{- end }}
  {{- if $.Values.externalAccess.controller.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml $.Values.externalAccess.controller.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  {{- end }}
  publishNotReadyAddresses: {{ $.Values.externalAccess.controller.service.publishNotReadyAddresses }}
  ports:
    - name: tcp-kafka
      port: {{ $.Values.externalAccess.controller.service.ports.external }}
      {{- if le (add $i 1) (len $.Values.externalAccess.controller.service.nodePorts) }}
      nodePort: {{ index $.Values.externalAccess.controller.service.nodePorts $i }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: external
    {{- if $.Values.externalAccess.controller.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" $.Values.externalAccess.controller.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- if and (eq $.Values.externalAccess.controller.service.type "NodePort") (le (add $i 1) (len $.Values.externalAccess.controller.service.externalIPs)) }}
  externalIPs: [{{ index $.Values.externalAccess.controller.service.externalIPs $i | quote }}]
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list $.Values.controller.podLabels $.Values.commonLabels ) "context" $ ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/part-of: kafka
    app.kubernetes.io/component: controller-eligible
    statefulset.kubernetes.io/pod-name: {{ $targetPod }}
---
{{- end }}
{{- end }}
{{- end }}
