{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $replicaCount := int .Values.controller.replicaCount }}
{{- if and .Values.kraft.enabled (gt $replicaCount 0) }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-controller-headless" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.controller.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
  {{- if or .Values.service.headless.controller.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.controller.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    {{- if or (not .Values.kraft.enabled) (not .Values.controller.controllerOnly) }}
    - name: tcp-interbroker
      port: {{ .Values.service.ports.interbroker }}
      protocol: TCP
      targetPort: interbroker
    - name: tcp-client
      port: {{ .Values.service.ports.client }}
      protocol: TCP
      targetPort: client
    {{- end }}
    {{- if .Values.kraft.enabled }}
    - name: tcp-controller
      protocol: TCP
      port: {{ .Values.service.ports.controller }}
      targetPort: controller
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.controller.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
{{- end }}
