{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.metrics.kafka.enabled }}
{{- $releaseNamespace := include "common.names.namespace" . -}}
{{- $clusterDomain := .Values.clusterDomain -}}
{{- $fullname := include "common.names.fullname" . -}}
{{- $containerPort := int .Values.listeners.client.containerPort -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "kafka.metrics.kafka.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" ( include "common.images.version" ( dict "imageRoot" .Values.metrics.kafka.image "chart" .Chart ) ) }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonLabels $versionLabel ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cluster-metrics
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.kafka.podLabels .Values.commonLabels $versionLabel ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: cluster-metrics
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: cluster-metrics
      {{- if .Values.metrics.kafka.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
    spec:
      {{- include "kafka.imagePullSecrets" . | nindent 6 }}
      automountServiceAccountToken: {{ .Values.metrics.kafka.automountServiceAccountToken }}
      {{- if .Values.metrics.kafka.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.metrics.kafka.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.kafka.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.metrics.kafka.podAffinityPreset "component" "cluster-metrics"  "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.metrics.kafka.podAntiAffinityPreset "component" "cluster-metrics"  "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.metrics.kafka.nodeAffinityPreset.type "key" .Values.metrics.kafka.nodeAffinityPreset.key "values" .Values.metrics.kafka.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.metrics.kafka.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.kafka.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.metrics.kafka.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.metrics.kafka.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.metrics.kafka.priorityClassName }}
      priorityClassName: {{ .Values.metrics.kafka.priorityClassName }}
      {{- end }}
      {{- if .Values.metrics.kafka.schedulerName }}
      schedulerName: {{ .Values.metrics.kafka.schedulerName }}
      {{- end }}
      {{- if .Values.metrics.kafka.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.metrics.kafka.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ template "kafka.metrics.kafka.serviceAccountName" . }}
      enableServiceLinks: {{ .Values.metrics.kafka.enableServiceLinks }}
      {{- if .Values.metrics.kafka.initContainers }}
      initContainers: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.initContainers "context" $) | nindent 8 }}
      {{- end }}
      containers:
        - name: kafka-exporter
          image: {{ include "kafka.metrics.kafka.image" . }}
          imagePullPolicy: {{ .Values.metrics.kafka.image.pullPolicy | quote }}
          {{- if .Values.metrics.kafka.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.metrics.kafka.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.metrics.kafka.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - bash
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.metrics.kafka.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.args "context" $) | nindent 12 }}
          {{- else }}
          args:
            - -ce
            - |
              kafka_exporter \
              {{- range $i := until (int .Values.controller.replicaCount) }}
              --kafka.server={{ $fullname }}-controller-{{ $i }}.{{ $fullname }}-controller-headless.{{ $releaseNamespace }}.svc.{{ $clusterDomain }}:{{ $containerPort }} \
              {{- end }}
              {{- range $i := until (int .Values.broker.replicaCount) }}
              --kafka.server={{ $fullname }}-broker-{{ $i }}.{{ $fullname }}-broker-headless.{{ $releaseNamespace }}.svc.{{ $clusterDomain }}:{{ $containerPort }} \
              {{- end }}
              {{- if regexFind "SASL" (upper .Values.listeners.client.protocol) }}
              --sasl.enabled \
              --sasl.username=$SASL_USERNAME \
              --sasl.password=$SASL_USER_PASSWORD \
              --sasl.mechanism={{ include "kafka.metrics.kafka.saslMechanism" . }} \
              {{- end }}
              {{- if regexFind "SSL" (upper .Values.listeners.client.protocol) }}
              --tls.enabled \
              {{- if .Values.metrics.kafka.certificatesSecret }}
              --tls.key-file=/opt/bitnami/kafka-exporter/certs/{{ .Values.metrics.kafka.tlsKey }} \
              --tls.cert-file=/opt/bitnami/kafka-exporter/certs/{{ .Values.metrics.kafka.tlsCert }} \
              {{- if .Values.metrics.kafka.tlsCaSecret }}
              --tls.ca-file=/opt/bitnami/kafka-exporter/cacert/{{ .Values.metrics.kafka.tlsCaCert }} \
              {{- else }}
              --tls.ca-file=/opt/bitnami/kafka-exporter/certs/{{ .Values.metrics.kafka.tlsCaCert }} \
              {{- end }}
              {{- end }}
              {{- end }}
              {{- range $key, $value := .Values.metrics.kafka.extraFlags }}
              --{{ $key }}{{ if $value }}={{ $value }}{{ end }} \
              {{- end }}
              --web.listen-address=:{{ .Values.metrics.kafka.containerPorts.metrics }}
          {{- end }}
          {{- if regexFind "SASL" (upper .Values.listeners.client.protocol) }}
          env:
            - name: SASL_USERNAME
              value: {{ index .Values.sasl.client.users 0 | quote }}
            - name: SASL_USER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "kafka.saslSecretName" . }}
                  key: system-user-password
            {{- end }}
          ports:
            - name: metrics
              containerPort: {{ .Values.metrics.kafka.containerPorts.metrics }}
          {{- if .Values.metrics.kafka.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.kafka.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.kafka.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- if .Values.metrics.kafka.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.kafka.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.kafka.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- if .Values.metrics.kafka.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.kafka.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.kafka.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- if .Values.metrics.kafka.resources }}
          resources: {{ toYaml .Values.metrics.kafka.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- if .Values.metrics.kafka.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
            {{- if and (regexFind "SSL" (upper .Values.listeners.client.protocol)) .Values.metrics.kafka.certificatesSecret }}
            - name: kafka-exporter-certificates
              mountPath: /opt/bitnami/kafka-exporter/certs/
              readOnly: true
            {{- if .Values.metrics.kafka.tlsCaSecret }}
            - name: kafka-exporter-ca-certificate
              mountPath: /opt/bitnami/kafka-exporter/cacert/
              readOnly: true
            {{- end }}
            {{- end }}
        {{- if .Values.metrics.kafka.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if .Values.metrics.kafka.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.metrics.kafka.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
        {{- if and (regexFind "SSL" (upper .Values.listeners.client.protocol)) .Values.metrics.kafka.certificatesSecret }}
        - name: kafka-exporter-certificates
          secret:
            secretName: {{ .Values.metrics.kafka.certificatesSecret }}
            defaultMode: 0440
        {{- if .Values.metrics.kafka.tlsCaSecret }}
        - name: kafka-exporter-ca-certificate
          secret:
            secretName: {{ .Values.metrics.kafka.tlsCaSecret }}
            defaultMode: 0440
        {{- end }}
        {{- end }}
{{- end }}
