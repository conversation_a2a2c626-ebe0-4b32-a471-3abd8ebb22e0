{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.metrics.jmx.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-jmx-metrics" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: metrics
  {{- if or .Values.metrics.jmx.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.jmx.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  sessionAffinity: {{ .Values.metrics.jmx.service.sessionAffinity }}
  {{- if .Values.metrics.jmx.service.clusterIP }}
  clusterIP: {{ .Values.metrics.jmx.service.clusterIP }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ .Values.metrics.jmx.service.ports.metrics }}
      protocol: TCP
      targetPort: metrics
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/part-of: kafka
{{- end }}
