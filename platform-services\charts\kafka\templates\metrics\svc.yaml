{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.metrics.kafka.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" ( include "common.images.version" ( dict "imageRoot" .Values.metrics.kafka.image "chart" .Chart ) ) }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonLabels $versionLabel ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cluster-metrics
  {{- if or .Values.metrics.kafka.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.kafka.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  sessionAffinity: {{ .Values.metrics.kafka.service.sessionAffinity }}
  {{- if .Values.metrics.kafka.service.clusterIP }}
  clusterIP: {{ .Values.metrics.kafka.service.clusterIP }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ .Values.metrics.kafka.service.ports.metrics }}
      protocol: TCP
      targetPort: metrics
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.kafka.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cluster-metrics
{{- end }}
