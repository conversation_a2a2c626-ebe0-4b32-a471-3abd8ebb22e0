{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.provisioning.enabled (regexFind "SSL" (upper .Values.listeners.client.protocol)) (not .Values.provisioning.auth.tls.passwordsSecret)  }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "kafka.client.passwordsSecretName" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  truststore-password: {{ default "" .Values.provisioning.auth.tls.keystorePassword | b64enc | quote }}
  keystore-password: {{ default "" .Values.provisioning.auth.tls.truststorePassword | b64enc | quote }}
  key-password: {{ default "" .Values.provisioning.auth.tls.keyPassword | b64enc | quote }}
{{- end }}
