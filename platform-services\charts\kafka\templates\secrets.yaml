{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "kafka.createSaslSecret" .) }}
{{- $secretName := printf "%s-user-passwords" (include "common.names.fullname" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if (include "kafka.client.saslEnabled" .) }}
  {{- $secretValue := "" }}
  {{- $secretData := (lookup "v1" "Secret" (include "common.names.namespace" .) $secretName).data }}
  {{- if and $secretData (hasKey $secretData "client-passwords")}}
    {{- $secretValue = index $secretData "client-passwords" }}
  {{- end }}
  {{- if or (empty $secretValue) (not (eq (len .Values.sasl.client.users) (len (splitList "," (b64dec $secretValue))))) }}
    {{- $clientPasswords := .Values.sasl.client.passwords }}
    {{- if empty $clientPasswords }}
      {{- $clientPasswords = list }}
      {{- range .Values.sasl.client.users }}
        {{- $clientPasswords = append $clientPasswords (randAlphaNum 10) }}
      {{- end }}
    {{- end }}
    {{- $secretValue = join "," $clientPasswords | toString | b64enc }}
  {{- end }}
  {{- if (include "kafka.saslUserPasswordsEnabled" .) }}
  client-passwords: {{ $secretValue | quote }}
  system-user-password: {{ index (splitList "," (b64dec $secretValue)) 0 | b64enc | quote }}
  {{- end }}
  {{- end }}
  {{- if or .Values.sasl.zookeeper.user .Values.zookeeper.auth.client.enabled }}
  zookeeper-password: {{ include "common.secrets.passwords.manage" (dict "secret" $secretName "key" "zookeeper-password" "providedValues" (list "sasl.zookeeper.password" "zookeeper.auth.client.clientPassword") "failOnNew" false "context" $) }}
  {{- end }}
  {{- if regexFind "SASL" (upper .Values.listeners.interbroker.protocol) }}
  {{- if (include "kafka.saslUserPasswordsEnabled" .) }}
  inter-broker-password: {{ include "common.secrets.passwords.manage" (dict "secret" $secretName "key" "inter-broker-password" "providedValues" (list "sasl.interbroker.password") "failOnNew" false "context" $) }}
  {{- end }}
  {{- if (include "kafka.saslClientSecretsEnabled" .) }}
  inter-broker-client-secret: {{ include "common.secrets.passwords.manage" (dict "secret" $secretName "key" "inter-broker-client-secret" "providedValues" (list "sasl.interbroker.clientSecret") "failOnNew" false "context" $) }}
  {{- end }}
  {{- end }}
  {{- if regexFind "SASL" (upper .Values.listeners.controller.protocol) }}
  {{- if (include "kafka.saslUserPasswordsEnabled" .) }}
  controller-password: {{ include "common.secrets.passwords.manage" (dict "secret" $secretName "key" "controller-password" "providedValues" (list "sasl.controller.password") "failOnNew" false "context" $) }}
  {{- end }}
  {{- if (include "kafka.saslClientSecretsEnabled" .) }}  
  controller-client-secret: {{ include "common.secrets.passwords.manage" (dict "secret" $secretName "key" "controller-client-secret" "providedValues" (list "sasl.controller.clientSecret") "failOnNew" false "context" $) }}
  {{- end }}
  {{- end }}
{{- if .Values.serviceBindings.enabled }}

{{- if (include "kafka.client.saslEnabled" .) }}
{{- $host := list }}
{{- $port := .Values.service.ports.client }}
{{- $bootstrapServers := list }}
{{- if not .Values.controller.controllerOnly }}
  {{- range $i, $e := until (int .Values.controller.replicaCount) }}
    {{- $controller := printf "%s-controller-%s.%s-headless.%s.svc.%s" (include "common.names.fullname" $) (print $i) (include "common.names.fullname" $) $.Release.Namespace $.Values.clusterDomain }}
    {{- $host = append $host $controller }}
    {{- $bootstrapServers = append $bootstrapServers (printf "%s:%s" $controller $.Values.service.ports.client) }}
  {{- end }}
{{- end }}
{{- range $i, $e := until (int .Values.broker.replicaCount) }}
  {{- $broker := printf "%s-broker-%s.%s-headless.%s.svc.%s" (include "common.names.fullname" $) (print $i) (include "common.names.fullname" $) $.Release.Namespace $.Values.clusterDomain }}
  {{- $host = append $host $broker }}
  {{- $bootstrapServers = append $bootstrapServers (printf "%s:%s" $broker $.Values.service.ports.client) }}
{{- end }}
{{- range $i, $e := until (len .Values.sasl.client.users) }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" $ }}-svcbind-user-{{ $i }}
  namespace: {{ $.Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $.Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if $.Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/kafka
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "kafka" | b64enc | quote }}
  username: {{ index $.Values.sasl.client.users $i | b64enc | quote }}
  password: {{ index $.Values.sasl.client.passwords $i | b64enc | quote }}
  host: {{ join "," $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  bootstrap-servers: {{ join "," $bootstrapServers | b64enc | quote }}
{{- end }}
{{- else }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/kafka
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "kafka" | b64enc | quote }}
  host: {{ join "," $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  bootstrap-servers: {{ join "," $bootstrapServers | b64enc | quote }}
{{- end }}
{{- end }}
{{- end }}
{{- if and .Values.kraft.enabled (not .Values.kraft.existingClusterIdSecret) }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-kraft-cluster-id" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  kraft-cluster-id: {{ include "common.secrets.passwords.manage" (dict "secret" (printf "%s-kraft-cluster-id" (include "common.names.fullname" .)) "key" "kraft-cluster-id" "providedValues" (list "kraft.clusterId") "length" 22 "context" $) }}
{{- end }}
