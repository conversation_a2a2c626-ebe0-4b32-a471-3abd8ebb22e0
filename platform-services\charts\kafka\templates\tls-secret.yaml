{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "kafka.createTlsSecret" .) }}
{{- $releaseNamespace := include "common.names.namespace" . }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $fullname := include "common.names.fullname" . }}
{{- $secretName := printf "%s-tls" (include "common.names.fullname" .) }}
{{- $altNames := list (printf "%s.%s.svc.%s" $fullname $releaseNamespace $clusterDomain) (printf "%s.%s" $fullname $releaseNamespace) $fullname }}
{{- $replicaCount := int .Values.broker.replicaCount }}
{{- range $i := until $replicaCount }}
{{- $replicaHost := printf "%s-broker-%d.%s-broker-headless" $fullname $i $fullname }}
{{- $altNames = append $altNames (printf "%s.%s.svc.%s" $replicaHost $releaseNamespace $clusterDomain) }}
{{- $altNames = append $altNames (printf "%s.%s" $replicaHost $releaseNamespace) }}
{{- $altNames = append $altNames $replicaHost }}
{{- end }}
{{- $replicaCount := int .Values.controller.replicaCount }}
{{- range $i := until $replicaCount }}
{{- $replicaHost := printf "%s-controller-%d.%s-controller-headless" $fullname $i $fullname }}
{{- $altNames = append $altNames (printf "%s.%s.svc.%s" $replicaHost $releaseNamespace $clusterDomain) }}
{{- $altNames = append $altNames (printf "%s.%s" $replicaHost $releaseNamespace) }}
{{- $altNames = append $altNames $replicaHost }}
{{- end }}
{{- $ca := genCA "kafka-ca" 365 }}
{{- $cert := genSignedCert $fullname nil $altNames 365 $ca }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  kafka.crt: {{ include "common.secrets.lookup" (dict "secret" $secretName "key" "kafka.crt" "defaultValue" $cert.Cert "context" $) }}
  kafka.key: {{ include "common.secrets.lookup" (dict "secret" $secretName "key" "kafka.key" "defaultValue" $cert.Key "context" $) }}
  kafka-ca.crt: {{ include "common.secrets.lookup" (dict "secret" $secretName "key" "kafka-ca.crt" "defaultValue" $ca.Cert "context" $) }}
---
{{- end }}
{{- if (include "kafka.createTlsPasswordsSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-tls-passwords" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{ .Values.tls.passwordsSecretKeystoreKey }}: {{ include "common.secrets.passwords.manage" (dict "secret" (printf "%s-tls-passwords" (include "common.names.fullname" .)) "key" .Values.tls.passwordsSecretKeystoreKey "providedValues" (list "tls.keystorePassword") "context" $) }}
  {{ .Values.tls.passwordsSecretTruststoreKey }}: {{ include "common.secrets.passwords.manage" (dict "secret" (printf "%s-tls-passwords" (include "common.names.fullname" .)) "key" .Values.tls.passwordsSecretTruststoreKey "providedValues" (list "tls.truststorePassword") "context" $) }}
  {{- if .Values.tls.keyPassword }}
  {{ default "key-password" .Values.tls.passwordsSecretPemPasswordKey }}: {{ .Values.tls.keyPassword | b64enc | quote }}
  {{- end }}
---
{{- end }}
{{- if (include "kafka.zookeeper.createTlsPasswordsSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-zookeeper-tls-passwords" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .Values.tls.zookeeper.keystorePassword }}
  {{ .Values.tls.zookeeper.passwordsSecretKeystoreKey }}: {{ .Values.tls.zookeeper.keystorePassword | b64enc | quote }}
  {{- end }}
  {{- if .Values.tls.zookeeper.truststorePassword }}
  {{ .Values.tls.zookeeper.passwordsSecretTruststoreKey }}: {{ .Values.tls.zookeeper.truststorePassword | b64enc | quote }}
  {{- end }}
---
{{- end }}
