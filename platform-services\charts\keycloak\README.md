# Keycloak Deployment Guide

## Tổng quan
Keycloak được triển khai trên namespace `platform-services` sử dụng Helm chart wrapper với chart con `common`. Keycloak sử dụng Gateway API trên namespace `bootstrap` và có thể truy cập qua domain `http://keycloak.local`.

## Cấu trúc thư mục
```
platform-services/charts/keycloak/
├── Chart.yaml              # Wrapper chart configuration
├── values.yaml             # Values cho wrapper chart
├── charts/
│   ├── common/             # Chart con chứa helper templates
│   │   ├── Chart.yaml      # Common chart configuration
│   │   └── templates/
│   │       └── _helpers.tpl # Helper templates
│   ├── common-0.1.0.tgz    # Packaged common chart
│   └── keycloak-20.0.1.tgz # Bitnami Keycloak chart
└── templates/              # (Empty - sử dụng subcharts)
```

## Triển khai

### 1. Thêm host local

**Windows:**
Chạy script PowerShell với quyền Administrator:
```powershell
.\platform-services\scripts\add-keycloak-host.ps1
```

**Linux/Mac:**
Chạy script bash với quyền sudo:
```bash
sudo ./platform-services/scripts/add-keycloak-host.sh
```

**Thủ công:**
Thêm vào file hosts (`C:\Windows\System32\drivers\etc\hosts` trên Windows hoặc `/etc/hosts` trên Linux/Mac):
```
*************    keycloak.local
```

**Lưu ý:** IP ************* là địa chỉ của Traefik Gateway trên namespace bootstrap.

### 2. Khởi tạo database
Database sẽ được tự động khởi tạo thông qua Job `keycloak-db-init` với sync-wave "-1".

### 3. Deploy via ArgoCD
Keycloak sẽ được deploy tự động thông qua ArgoCD Application `platform-services`.

## Cấu hình

### Database
- Host: `postgresql.platform-services.svc.cluster.local`
- Port: `5432`
- Database: `keycloak`
- User: `keycloak`
- Password: `keycloak-db-password123`

### Admin Credentials
- Username: `admin`
- Password: `admin123`

### Resources
- CPU Request: 250m
- Memory Request: 512Mi
- CPU Limit: 500m  
- Memory Limit: 1Gi

### Persistence
- Enabled: true
- Size: 5Gi
- StorageClass: local-path

### Gateway Configuration
- Gateway: traefik-gateway (namespace: bootstrap)
- Gateway IP: *************
- HTTPRoute: keycloak-httproute (namespace: platform-services)
- Hostname: keycloak.local

## Truy cập
- URL: http://keycloak.local
- Admin Console: http://keycloak.local/admin

## Kiểm tra trạng thái
```bash
# Kiểm tra pods
kubectl get pods -n platform-services | grep keycloak

# Kiểm tra service
kubectl get svc -n platform-services | grep keycloak

# Kiểm tra HTTPRoute
kubectl get httproute -n platform-services keycloak-httproute

# Xem logs
kubectl logs -n platform-services deployment/keycloak-wrapper
```

## Troubleshooting

### 1. Pod không start
```bash
kubectl describe pod -n platform-services keycloak-wrapper-xxx
```

### 2. Database connection issues
```bash
kubectl logs -n platform-services job/keycloak-db-init
```

### 3. HTTPRoute không hoạt động
```bash
kubectl describe httproute -n platform-services keycloak-httproute
```

## Customization
Để thay đổi cấu hình, chỉnh sửa file `platform-services/platform/keycloak-app.yaml` trong phần `helm.values`.
