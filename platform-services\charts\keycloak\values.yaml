# Values for Keycloak Wrapper Chart
# Tất cả cấu hình sẽ được chuyển tới các subcharts

# Global configuration để đảm bả<PERSON> không bị override
global:
  imageRegistry: ""  # Để trống để sử dụng registry từ keycloak.image.registry
  imagePullSecrets:
    - ospgroup-dockerhub-secret

# C<PERSON>u hình cho Keycloak subchart
keycloak:
  # Custom image với themes
  image:
    registry: dockerhub.ospgroup.vn
    repository: osp-public/keycloak-custom-themes
    tag: "latest"  # Sẽ được update tự động bởi CI/CD
    pullPolicy: "Always"  # Luôn pull image mới nhất cho tag latest
    digest: ""  # Để trống để sử dụng tag

  # Image pull secrets để pull từ private registry
  imagePullSecrets:
    - name: ospgroup-dockerhub-secret

  # External database configuration
  postgresql:
    enabled: false
  
  networkPolicy:
    enabled: false

  externalDatabase:
    host: "postgresql.platform-services.svc.cluster.local"
    port: 5432
    user: "keycloak"
    database: "keycloak"
    existingSecret: "keycloak-database"
    existingSecretPasswordKey: "password"

  # Service configuration
  service:
    type: ClusterIP
    port: 8080

  # Keycloak admin user
  auth:
    adminUser: "admin"
    existingSecret: "keycloak-admin"
    secretKeys:
      adminPasswordKey: "admin-password"

  # Persistence
  persistence:
    enabled: true
    size: 8Gi
    storageClass: "local-path"

  # Resources
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi

  # Node affinity  
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: kubernetes.io/hostname
            operator: In
            values:
            - warehouse02

  # Startup probe
  startupProbe:
    enabled: true
    httpGet:
      path: /health/ready
      port: http
    initialDelaySeconds: 30
    timeoutSeconds: 5
    periodSeconds: 10
    successThreshold: 1
    failureThreshold: 30

  # Liveness probe
  livenessProbe:
    enabled: true
    httpGet:
      path: /health/live
      port: http
    initialDelaySeconds: 60
    timeoutSeconds: 5
    periodSeconds: 10
    successThreshold: 1
    failureThreshold: 5

  # Readiness probe
  readinessProbe:
    enabled: true
    httpGet:
      path: /health/ready
      port: http
    initialDelaySeconds: 30
    timeoutSeconds: 5
    periodSeconds: 10
    successThreshold: 1
    failureThreshold: 3

  # Zero-downtime deployment with rolling update
  replicaCount: 1

  # Rolling update strategy for StatefulSet
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      partition: 0

  # Pod Disruption Budget
  podDisruptionBudget:
    enabled: true
    minAvailable: 1

# Cấu hình cho common subchart (nếu cần)
common: {}
