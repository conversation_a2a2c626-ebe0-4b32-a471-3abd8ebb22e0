# Label Studio Helm Chart Values
# <PERSON><PERSON><PERSON> hình cho triển khai Label Studio trên Kubernetes với external PostgreSQL và Redis

global:
  # Image configuration
  image:
    repository: heartexlabs/label-studio
    tag: "1.19.0"
    pullPolicy: IfNotPresent

  # PostgreSQL configuration - sử dụng external PostgreSQL từ platform services
  pgConfig:
    host: "postgresql.platform-services.svc.cluster.local"
    port: 5432
    dbName: "labelstudio"
    userName: "labelstudio"
    password:
      secretName: "labelstudio-db-secret"
      secretKey: "password"

  # Redis configuration - sử dụng external Redis từ platform services
  redisConfig:
    host: "redis://:<EMAIL>:6379/2"

  # Environment variables
  extraEnvironmentVars:
    # Cấu hình subpath cho Label Studio
    LABEL_STUDIO_HOST: "https://common.ospgroup.vn/label-studio"
    # Disable signup để bảo mật
    LABEL_STUDIO_DISABLE_SIGNUP_WITHOUT_LINK: "true"
    # <PERSON><PERSON><PERSON> hình logging
    LOG_LEVEL: "INFO"
    # <PERSON><PERSON><PERSON> hình CORS
    LABEL_STUDIO_CORS_ALLOW_ALL_ORIGINS: "false"

  # Persistence configuration - sử dụng local volume
  persistence:
    enabled: true
    type: "volume"

# Application configuration
app:
  # Số lượng replicas
  replicas: 1
  
  # Resource limits và requests
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "4Gi"
      cpu: "2000m"

  # Service configuration
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080

  # Ingress configuration - disable vì sử dụng Gateway API
  ingress:
    enabled: false

  # Liveness và readiness probes
  livenessProbe:
    enabled: true
    path: "/health"
    initialDelaySeconds: 60
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

  readinessProbe:
    enabled: true
    path: "/health"
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 2

  # Nginx sidecar configuration - disable probes vì có vấn đề
  nginx:
    readinessProbe:
      enabled: false
    livenessProbe:
      enabled: false

  # Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001

  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true

  # Service account
  serviceAccount:
    create: true
    name: ""
    annotations: {}

  # Node selector và affinity
  nodeSelector: {}
  affinity: {}
  tolerations: []

# RQ Worker configuration
rqworker:
  enabled: true
  
  # Queue configuration
  queues:
    high:
      replicas: 1
      args: "high"
    low:
      replicas: 1
      args: "low"
    default:
      replicas: 1
      args: "default"
    critical:
      replicas: 1
      args: "critical"

  # Resource limits cho workers
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

  # Security context cho workers
  podSecurityContext:
    enabled: true
    fsGroup: 1001

  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true

# Disable built-in PostgreSQL và Redis
postgresql:
  enabled: false

redis:
  enabled: false

# Enterprise features - disable cho community version
enterprise:
  enabled: false
