annotations:
  category: Infrastructure
  images: |
    - name: minio
      image: docker.io/bitnami/minio:2025.7.23-debian-12-r3
    - name: minio-client
      image: docker.io/bitnami/minio-client:2025.7.21-debian-12-r2
    - name: minio-object-browser
      image: docker.io/bitnami/minio-object-browser:2.0.2-debian-12-r3
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r50
  licenses: Apache-2.0
  tanzuCategory: service
apiVersion: v2
appVersion: 2025.7.23
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: MinIO(R) is an object storage server, compatible with Amazon S3 cloud
  storage service, mainly used for storing unstructured data (such as photos, videos,
  log files, etc.).
home: https://bitnami.com
icon: https://dyltqmyl993wv.cloudfront.net/assets/stacks/minio/img/minio-stack-220x234.png
keywords:
- minio
- storage
- object-storage
- s3
- cluster
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: minio
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/minio
version: 17.0.21
