CHART NAME: {{ .Chart.Name }}
CHART VERSION: {{ .Chart.Version }}
APP VERSION: {{ .Chart.AppVersion }}

⚠ WARNING: Since August 28th, 2025, only a limited subset of images/charts are available for free.
    Subscribe to Bitnami Secure Images to receive continued support and security updates.
    More info at https://bitnami.com and https://github.com/bitnami/containers/issues/83267

** Please be patient while the chart is being deployed **

Minio(R) can be accessed via port {{ .Values.service.ports.api }} on the following DNS name from within your cluster:

   {{ include "common.names.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}

To get your credentials run:

   export ROOT_USER=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "minio.secretName" . }} -o jsonpath="{.data.{{ include "minio.rootUserKey" . }}}" | base64 -d)
   export ROOT_PASSWORD=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "minio.secretName" . }} -o jsonpath="{.data.{{ include "minio.rootPasswordKey" . }}}" | base64 -d)

To connect to your Minio(R) server using a client:

- Run a Minio(R) Client pod and append the desired command (e.g. 'admin info'):

   kubectl run --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }}-client \
     --rm --tty -i --restart='Never' \
     --env MINIO_SERVER_ROOT_USER=$ROOT_USER \
     --env MINIO_SERVER_ROOT_PASSWORD=$ROOT_PASSWORD \
     --env MINIO_SERVER_HOST={{ include "common.names.fullname" . }} \
     {{- if and (.Values.networkPolicy.enabled) (not .Values.networkPolicy.allowExternal) }}
     --labels="{{ include "common.names.fullname" . }}-client=true" \
     {{- end }}
     --image {{ template "minio.clientImage" . }} -- admin info minio

{{- if and (.Values.networkPolicy.enabled) (not .Values.networkPolicy.allowExternal) }}

   NOTE: Since NetworkPolicy is enabled, only pods with label
   "{{ template "common.names.fullname" . }}-client=true" will be able to connect to Minio(R).

{{- end }}
{{- if .Values.console.enabled }}

To access the Minio(R) Console:

- Get the Minio(R) Console URL:

{{- if .Values.console.ingress.enabled }}

   You should be able to access your new Minio(R) Console through

   {{ if .Values.console.ingress.tls }}https{{ else }}http{{ end }}://{{ .Values.console.ingress.hostname }}
{{- else if contains "LoadBalancer" .Values.console.service.type }}

   NOTE: It may take a few minutes for the LoadBalancer IP to be available.
         Watch the status with: 'kubectl get svc --namespace {{ .Release.Namespace }} -w {{ include "minio.console.fullname" . }}'

   {{- $port := .Values.console.service.ports.http | toString }}
   export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "minio.console.fullname" . }} --template "{{ "{{ range (index .status.loadBalancer.ingress 0) }}{{ . }}{{ end }}" }}")
   echo "Minio(R) Console URL: http://$SERVICE_IP{{- if ne $port "80" }}:{{ .Values.console.service.ports.http }}{{ end }}"

{{- else if contains "ClusterIP"  .Values.console.service.type }}

   echo "Minio(R) Console URL: http://127.0.0.1:{{ .Values.console.containerPorts.http }}"
   kubectl port-forward --namespace {{ .Release.Namespace }} svc/{{ include "minio.console.fullname" . }} {{ .Values.console.containerPorts.http }}:{{ .Values.console.service.ports.http }}

{{- else if contains "NodePort" .Values.console.service.type }}

   export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "minio.console.fullname" . }})
   export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
   echo "Minio(R) Console URL: http://$NODE_IP:$NODE_PORT"

{{- end }}
{{- else }}

   WARN: Minio(R) Console is disabled.
{{- end }}

{{- include "common.warnings.rollingTag" .Values.image }}
{{- include "common.warnings.rollingTag" .Values.clientImage }}
{{- include "common.warnings.rollingTag" .Values.console.image }}
{{- include "common.warnings.rollingTag" .Values.defaultInitContainers.volumePermissions.image }}
{{- include "minio.validateValues" . }}
{{- include "common.warnings.resources" (dict "sections" (list "" "console" "provisioning" "defaultInitContainers.volumePermissions") "context" .) }}
{{- include "common.warnings.modifiedImages" (dict "images" (list .Values.image .Values.clientImage .Values.console.image .Values.defaultInitContainers.volumePermissions.image) "context" .) }}
{{- include "common.errors.insecureImages" (dict "images" (list .Values.image .Values.clientImage .Values.console.image .Values.defaultInitContainers.volumePermissions.image) "context" .) }}
