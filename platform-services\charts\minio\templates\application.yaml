{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $fullname := include "common.names.fullname" . }}
{{- $headlessService := printf "%s-headless" (include "common.names.fullname" .) | trunc 63 }}
{{- $releaseNamespace := include "common.names.namespace" . }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $apiPort := toString .Values.containerPorts.api }}
{{- $replicaCount := int .Values.statefulset.replicaCount }}
{{- $zoneCount := int .Values.statefulset.zones }}
{{- $drivesPerNode := int .Values.statefulset.drivesPerNode }}
{{- $mountPath := .Values.persistence.mountPath }}
apiVersion: {{ ternary (include "common.capabilities.statefulset.apiVersion" .) (include "common.capabilities.deployment.apiVersion" .) (eq .Values.mode "distributed") }}
kind: {{ ternary "StatefulSet" "Deployment" (eq .Values.mode "distributed") }}
metadata:
  name: {{ $fullname }}
  namespace: {{ $releaseNamespace | quote  }}
  labels: {{- include "common.labels.standard" (dict "customLabels" .Values.commonLabels "context" .) | nindent 4 }}
    app.kubernetes.io/component: minio
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" (dict "customLabels" $podLabels "context" .) | nindent 6 }}
      app.kubernetes.io/component: minio
      app.kubernetes.io/part-of: minio
  {{- if eq .Values.mode "distributed" }}
  podManagementPolicy: {{ .Values.statefulset.podManagementPolicy }}
  replicas: {{ mul $zoneCount $replicaCount }}
  serviceName: {{ $headlessService }}
  {{- end }}
  {{- if .Values.updateStrategy }}
  {{ ternary "updateStrategy" "strategy" (eq .Values.mode "distributed") }}: {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" (dict "customLabels" $podLabels "context" .) | nindent 8 }}
        app.kubernetes.io/component: minio
        app.kubernetes.io/part-of: minio
      {{- if or .Values.podAnnotations (include "minio.createSecret" .) }}
      annotations:
        {{- if (include "minio.createSecret" .) }}
        checksum/credentials-secret: {{ include (print $.Template.BasePath "/secrets.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" .) | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      {{- include "minio.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      serviceAccountName: {{ template "minio.serviceAccountName" . }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" .) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "component" "minio" "customLabels" $podLabels "context" .) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "component" "minio" "customLabels" $podLabels "context" .) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" .) | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.runtimeClassName }}
      runtimeClassName: {{ .Values.runtimeClassName | quote }}
      {{- end}}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.podSecurityContext "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      {{- end }}
      initContainers:
        {{- if and .Values.defaultInitContainers.volumePermissions.enabled .Values.persistence.enabled }}
        {{- include "minio.defaultInitContainers.volumePermissions" . | nindent 8 }}
        {{- end }}
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" .) | nindent 8 }}
        {{- end }}
      containers:
        - name: minio
          image: {{ include "minio.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" .) | nindent 12 }}
          {{- end }}
          {{- if .Values.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.command "context" .) | nindent 12 }}
          {{- end }}
          {{- if .Values.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.args "context" .) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" .Values.image.debug | quote }}
            - name: MINIO_DISTRIBUTED_MODE_ENABLED
              value: {{ ternary "yes" "no" (eq .Values.mode "distributed") | quote }}
            {{- if eq .Values.mode "distributed" }}
            - name: MINIO_DISTRIBUTED_NODES
              {{- $clusters := list }}
              {{- range $i := until $zoneCount }}
                  {{- $factor := mul $i $replicaCount }}
                  {{- $endIndex := sub (add $factor $replicaCount) 1 }}
                  {{- $beginIndex := mul $i $replicaCount }}
                  {{- $bucket := ternary (printf "%s-{0...%d}" $mountPath (sub $drivesPerNode 1)) $mountPath (gt $drivesPerNode 1) }}
                  {{- $clusters = append $clusters (printf "%s-{%d...%d}.%s.%s.svc.%s:%s%s" $fullname $beginIndex $endIndex $headlessService $releaseNamespace $clusterDomain $apiPort $bucket) }}
              {{- end }}
              value: {{ join "," $clusters | quote }}
            {{- end }}
            - name: MINIO_SCHEME
              value: {{ ternary "https" "http" .Values.tls.enabled | quote }}
            - name: MINIO_FORCE_NEW_KEYS
              value: {{ ternary "yes" "no" .Values.auth.forceNewKeys | quote }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: MINIO_ROOT_USER_FILE
              value: {{ printf "/opt/bitnami/minio/secrets/%s" (include "minio.rootUserKey" .) }}
            - name: MINIO_ROOT_PASSWORD_FILE
              value: {{ printf "/opt/bitnami/minio/secrets/%s" (include "minio.rootPasswordKey" .) }}
            {{- else }}
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "minio.secretName" . }}
                  key: {{ include "minio.rootUserKey" . }}
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "minio.secretName" . }}
                  key: {{ include "minio.rootPasswordKey" . }}
            {{- end }}
            - name: MINIO_SKIP_CLIENT
              value: {{ ternary "yes" "no" (empty .Values.defaultBuckets) | quote }}
            {{- if .Values.defaultBuckets }}
            - name: MINIO_DEFAULT_BUCKETS
              value: {{ .Values.defaultBuckets }}
            {{- end }}
            - name: MINIO_API_PORT_NUMBER
              value: {{ .Values.containerPorts.api | quote }}
            - name: MINIO_BROWSER
              value: "off"
            - name: MINIO_PROMETHEUS_AUTH_TYPE
              value: {{ .Values.metrics.prometheusAuthType | quote }}
            - name: MINIO_DATA_DIR
              value: {{ ternary (printf "%s-0" $mountPath) $mountPath (and (eq .Values.mode "distributed") (gt $drivesPerNode 1)) | quote }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" .) | nindent 12 }}
            {{- end }}
          {{- if or .Values.extraEnvVarsCM .Values.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" .) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" .) }}
            {{- end }}
          {{- end }}
          ports:
            - name: api
              containerPort: {{ .Values.containerPorts.api }}
          {{- if .Values.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customLivenessProbe "context" .) | nindent 12 }}
          {{- else if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /minio/health/live
              port: api
              scheme: {{ ternary "HTTPS" "HTTP" .Values.tls.enabled | quote }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customReadinessProbe "context" .) | nindent 12 }}
          {{- else if .Values.readinessProbe.enabled }}
          readinessProbe:
            tcpSocket:
              port: api
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customStartupProbe "context" .) | nindent 12 }}
          {{- else if .Values.startupProbe.enabled }}
          startupProbe:
            tcpSocket:
              port: api
            initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.startupProbe.timeoutSeconds }}
            successThreshold: {{ .Values.startupProbe.successThreshold }}
            failureThreshold: {{ .Values.startupProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- else if ne .Values.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.resourcesPreset) | nindent 12 }}
          {{- end }}
          {{- if .Values.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.lifecycleHooks "context" .) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/minio/tmp
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /.mc
              subPath: app-mc-dir
            {{- if and .Values.auth.useSecret .Values.auth.usePasswordFiles }}
            - name: minio-credentials
              mountPath: /opt/bitnami/minio/secrets/
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: tls-certs
              mountPath: /certs
              readOnly: true
            {{- end }}
            {{- if and (eq .Values.mode "distributed") (gt $drivesPerNode 1) }}
            {{- range $diskId := until $drivesPerNode }}
            - name: data-{{ $diskId }}
              mountPath: {{ $mountPath }}-{{ $diskId }}
            {{- end }}
            {{- else }}
            - name: data
              mountPath: {{ $mountPath }}
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" .) | nindent 12 }}
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" .) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        {{- if and .Values.auth.useSecret .Values.auth.usePasswordFiles }}
        - name: minio-credentials
          secret:
            secretName: {{ include "minio.secretName" . }}
        {{- end }}
        {{- if .Values.tls.enabled }}
        - name: tls-certs
          projected:
            sources:
              - secret:
                  name: {{ template "minio.tls.ca.secretName" . }}
                  items:
                    - key: tls.crt
                      path: CAs/public.crt
              - secret:
                  name: {{ template "minio.tls.server.secretName" . }}
                  items:
                    - key: tls.crt
                      path: public.crt
                    - key: tls.key
                      path: private.key
        {{- end }}
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" .) | nindent 8 }}
        {{- end }}
  {{- if eq .Values.mode "standalone" }}
        - name: data
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "minio.claimName" . }}
          {{- else }}
          emptyDir: {}
          {{- end }}
  {{- else if and (not .Values.persistence.enabled) (gt $drivesPerNode 1) }}
  {{- range $diskId := until $drivesPerNode }}
        - name: data-{{ $diskId }}
          emptyDir: {}
  {{- end }}
  {{- else if not .Values.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else }}
  volumeClaimTemplates:
  {{- if gt $drivesPerNode 1 }}
    {{- range $diskId := until $drivesPerNode }}
    - metadata:
        name: data-{{ $diskId }}
        labels: {{- include "common.labels.matchLabels" (dict "customLabels" $.Values.commonLabels "context" $ ) | nindent 10 }}
        {{- if $.Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" $.Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range $.Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ $.Values.persistence.size | quote }}
        {{- if $.Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" $.Values.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{- include "common.storage.class" (dict "persistence" $.Values.persistence "global" $.Values.global) | nindent 8 }}
    {{- end }}
  {{- else }}
    - metadata:
        name: data
        labels: {{- include "common.labels.matchLabels" (dict "customLabels" .Values.commonLabels "context" .) | nindent 10 }}
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.annotations "context" .) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{- if .Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.selector "context" .) | nindent 10 }}
        {{- end }}
        {{- include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) | nindent 8 }}
  {{- end }}
  {{- end }}
