{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.tls.enabled .Values.tls.autoGenerated.enabled (eq .Values.tls.autoGenerated.engine "cert-manager") }}
{{- if empty .Values.tls.autoGenerated.certManager.existingIssuer }}
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: {{ printf "%s-clusterissuer" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
spec:
  selfSigned: {}
---
{{- end }}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ printf "%s-ca-crt" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
spec:
  secretName: {{ template "minio.tls.ca.secretName" . }}
  commonName: {{ printf "%s-root-ca" (include "common.names.fullname" .) }}
  isCA: true
  issuerRef:
    name: {{ default (printf "%s-clusterissuer" (include "common.names.fullname" .)) .Values.tls.autoGenerated.certManager.existingIssuer }}
    kind: {{ default "Issuer" .Values.tls.autoGenerated.certManager.existingIssuerKind }}
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: {{ printf "%s-ca-issuer" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
spec:
  ca:
    secretName: {{ template "minio.tls.ca.secretName" . }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ printf "%s-crt" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
    app.kubernetes.io/component: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
spec:
  secretName: {{ template "minio.tls.server.secretName" . }}
  commonName: {{ printf "%s.%s.svc.%s" (include "common.names.fullname" .) (include "common.names.namespace" .) .Values.clusterDomain }}
  issuerRef:
    name: {{ printf "%s-ca-issuer" (include "common.names.fullname" .) }}
    kind: Issuer
  subject:
    organizations:
      - "MinIO"
  dnsNames:
    - '*.{{ include "common.names.namespace" . }}'
    - '*.{{ include "common.names.namespace" . }}.svc'
    - '*.{{ include "common.names.namespace" . }}.svc.{{ .Values.clusterDomain }}'
    - '*.{{ include "common.names.fullname" . }}'
    - '*.{{ include "common.names.fullname" . }}.{{ include "common.names.namespace" . }}'
    - '*.{{ include "common.names.fullname" . }}.{{ include "common.names.namespace" . }}.svc'
    - '*.{{ include "common.names.fullname" . }}.{{ include "common.names.namespace" . }}.svc.{{ .Values.clusterDomain }}'
    - '*.{{ printf "%s-headless" (include "common.names.fullname" .) }}'
    - '*.{{ printf "%s-headless" (include "common.names.fullname" .) }}.{{ include "common.names.namespace" . }}'
    - '*.{{ printf "%s-headless" (include "common.names.fullname" .) }}.{{ include "common.names.namespace" . }}.svc'
    - '*.{{ printf "%s-headless" (include "common.names.fullname" .) }}.{{ include "common.names.namespace" . }}.svc.{{ .Values.clusterDomain }}'
  privateKey:
    algorithm: {{ .Values.tls.autoGenerated.certManager.keyAlgorithm }}
    size: {{ int .Values.tls.autoGenerated.certManager.keySize }}
  duration: {{ .Values.tls.autoGenerated.certManager.duration }}
  renewBefore: {{ .Values.tls.autoGenerated.certManager.renewBefore }}
{{- end }}
