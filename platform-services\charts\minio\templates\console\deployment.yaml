{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.console.enabled }}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if or .Values.console.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.console.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.console.autoscaling.hpa.enabled }}
  replicas: {{ .Values.console.replicaCount }}
  {{- end }}
  {{- if .Values.console.updateStrategy }}
  strategy: {{- toYaml .Values.console.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.console.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" (dict "customLabels" $podLabels "context" .) | nindent 6 }}
      app.kubernetes.io/component: console
      app.kubernetes.io/part-of: minio
  template:
    metadata:
      {{- if .Values.console.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.console.podAnnotations "context" .) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" (dict "customLabels" $podLabels "context" .) | nindent 8 }}
        app.kubernetes.io/component: console
        app.kubernetes.io/part-of: minio
    spec:
      {{- include "minio.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ template "minio.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.console.automountServiceAccountToken }}
      {{- if .Values.console.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.console.hostAliases "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.console.affinity "context" .) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.console.podAffinityPreset "component" "console" "customLabels" $podLabels "context" .) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.console.podAntiAffinityPreset "component" "console" "customLabels" $podLabels "context" .) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.console.nodeAffinityPreset.type "key" .Values.console.nodeAffinityPreset.key "values" .Values.console.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.console.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.console.nodeSelector "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.console.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.priorityClassName }}
      priorityClassName: {{ .Values.console.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.console.schedulerName }}
      schedulerName: {{ .Values.console.schedulerName | quote }}
      {{- end }}
      {{- if .Values.console.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.console.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.console.podSecurityContext "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.console.terminationGracePeriodSeconds }}
      {{- end }}
      {{- if .Values.console.initContainers }}
      initContainers: {{- include "common.tplvalues.render" (dict "value" .Values.console.initContainers "context" .) | nindent 8 }}
      {{- end }}
      containers:
        - name: console
          image: {{ template "minio.console.image" . }}
          imagePullPolicy: {{ .Values.console.image.pullPolicy }}
          {{- if .Values.console.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.console.containerSecurityContext "context" .) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.console.command "context" .) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.console.args "context" .) | nindent 12 }}
          {{- else }}
          args:
            - server
            - --host
            - "0.0.0.0"
            - --port
            - {{ .Values.console.containerPorts.http | quote }}
          {{- end }}
          env:
            - name: CONSOLE_MINIO_SERVER
              value: {{ printf "%s://%s:%d" (ternary "https" "http" .Values.tls.enabled) (include "common.names.fullname" .) (int .Values.service.ports.api) | quote }}
            {{- if .Values.console.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVars "context" .) | nindent 12 }}
            {{- end }}
          {{- if or .Values.console.extraEnvVarsCM .Values.console.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.console.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVarsCM "context" .) }}
            {{- end }}
            {{- if .Values.console.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVarsSecret "context" .) }}
            {{- end }}
          {{- end }}
          {{- if .Values.console.resources }}
          resources: {{- toYaml .Values.console.resources | nindent 12 }}
          {{- else if ne .Values.console.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.console.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.console.containerPorts.http }}
            {{- if .Values.console.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.console.extraContainerPorts "context" .) | nindent 12 }}
            {{- end }}
          {{- if .Values.console.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customLivenessProbe "context" .) | nindent 12 }}
          {{- else if .Values.console.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.livenessProbe "enabled") "context" .) | nindent 12 }}
            tcpSocket:
              port: http
          {{- end }}
          {{- if .Values.console.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customReadinessProbe "context" .) | nindent 12 }}
          {{- else if .Values.console.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.readinessProbe "enabled") "context" .) | nindent 12 }}
            httpGet:
              path: /minio
              port: http
          {{- end }}
          {{- if .Values.console.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customStartupProbe "context" .) | nindent 12 }}
          {{- else if .Values.console.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.startupProbe "enabled") "context" .) | nindent 12 }}
            tcpSocket:
              port: http
          {{- end }}
          {{- if .Values.console.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.console.lifecycleHooks "context" .) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /.console
              subPath: app-console-dir
          {{- if .Values.console.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.console.extraVolumeMounts "context" .) | nindent 12 }}
          {{- end }}
        {{- if .Values.console.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.console.sidecars "context" .) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        {{- if .Values.console.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.console.extraVolumes "context" .) | nindent 8 }}
        {{- end }}
{{- end }}
