{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.console.enabled .Values.console.autoscaling.hpa.enabled }}
apiVersion: {{ include "common.capabilities.hpa.apiVersion" (dict "context" .) }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
    kind: Deployment
    name: {{ template "minio.console.fullname" . }}
  minReplicas: {{ .Values.console.autoscaling.hpa.minReplicas }}
  maxReplicas: {{ .Values.console.autoscaling.hpa.maxReplicas }}
  metrics:
    {{- if .Values.console.autoscaling.hpa.targetMemory }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.console.autoscaling.hpa.targetMemory }}
    {{- end }}
    {{- if .Values.console.autoscaling.hpa.targetCPU }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.console.autoscaling.hpa.targetCPU }}
    {{- end }}
{{- end }}
