{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.console.enabled .Values.console.ingress.enabled }}
apiVersion: {{ include "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if or .Values.console.ingress.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.console.ingress.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.console.ingress.ingressClassName }}
  ingressClassName: {{ .Values.console.ingress.ingressClassName | quote }}
  {{- end }}
  rules:
    {{- if .Values.console.ingress.hostname }}
    - host: {{ tpl .Values.console.ingress.hostname . }}
      http:
        paths:
          {{- if .Values.console.ingress.extraPaths }}
          {{- toYaml .Values.console.ingress.extraPaths | nindent 10 }}
          {{- end }}
          - path: {{ .Values.console.ingress.path }}
            pathType: {{ .Values.console.ingress.pathType }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "minio.console.fullname" .) "servicePort" "http" "context" .)  | nindent 14 }}
    {{- end }}
    {{- range .Values.console.ingress.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default "/" .path }}
            pathType: {{ default "ImplementationSpecific" .pathType }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "minio.console.fullname" $) "servicePort" "http" "context" $) | nindent 14 }}
    {{- end }}
    {{- if .Values.console.ingress.extraRules }}
    {{- include "common.tplvalues.render" (dict "value" .Values.console.ingress.extraRules "context" .) | nindent 4 }}
    {{- end }}
  {{- if or (and .Values.console.ingress.tls (or (include "common.ingress.certManagerRequest" (dict "annotations" .Values.console.ingress.annotations)) .Values.console.ingress.selfSigned)) .Values.console.ingress.extraTls }}
  tls:
    {{- if and .Values.console.ingress.tls (or (include "common.ingress.certManagerRequest" (dict "annotations" .Values.console.ingress.annotations)) .Values.console.ingress.selfSigned) }}
    - hosts:
        - {{ tpl .Values.console.ingress.hostname . }}
      secretName: {{ printf "%s-tls" (tpl .Values.console.ingress.hostname .) }}
    {{- end }}
    {{- if .Values.console.ingress.extraTls }}
    {{- include "common.tplvalues.render" (dict "value" .Values.console.ingress.extraTls "context" .) | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
