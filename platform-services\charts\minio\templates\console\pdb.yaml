{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.console.enabled .Values.console.pdb.create }}
apiVersion: {{ include "common.capabilities.policy.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.console.pdb.minAvailable }}
  minAvailable: {{ .Values.console.pdb.minAvailable }}
  {{- end  }}
  {{- if or .Values.console.pdb.maxUnavailable ( not .Values.console.pdb.minAvailable ) }}
  maxUnavailable: {{ .Values.console.pdb.maxUnavailable | default 1 }}
  {{- end  }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" ( list .Values.console.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" (dict "customLabels" $podLabels "context" .) | nindent 6 }}
      app.kubernetes.io/component: console
      app.kubernetes.io/part-of: minio
{{- end }}
