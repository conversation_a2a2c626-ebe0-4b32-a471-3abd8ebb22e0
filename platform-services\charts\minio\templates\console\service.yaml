{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.console.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if or .Values.console.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.console.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.console.service.type }}
  {{- if and .Values.console.service.clusterIP (eq .Values.console.service.type "ClusterIP") }}
  clusterIP: {{ .Values.console.service.clusterIP }}
  {{- end }}
  {{- if .Values.console.service.sessionAffinity }}
  sessionAffinity: {{ .Values.console.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.console.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.console.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.console.service.type "LoadBalancer") (eq .Values.console.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.console.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.console.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.console.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.console.service.ports.http }}
      targetPort: http
      {{- if and (or (eq .Values.console.service.type "NodePort") (eq .Values.console.service.type "LoadBalancer")) (not (empty .Values.console.service.nodePorts.http)) }}
      nodePort: {{ .Values.console.service.nodePorts.http }}
      {{- else if eq .Values.console.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.console.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.console.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.console.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" (dict "customLabels" $podLabels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
{{- end }}
