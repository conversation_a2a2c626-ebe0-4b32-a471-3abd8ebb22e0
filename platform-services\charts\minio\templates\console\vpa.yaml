{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.console.enabled (include "common.capabilities.apiVersions.has" ( dict "version" "autoscaling.k8s.io/v1/VerticalPodAutoscaler" "context" . )) .Values.console.autoscaling.vpa.enabled }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ template "minio.console.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" (include "common.images.version" (dict "imageRoot" .Values.console.image "chart" .Chart)) }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.commonLabels $versionLabel) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: console
    app.kubernetes.io/part-of: minio
  {{- if or .Values.console.autoscaling.vpa.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" ( list .Values.console.autoscaling.vpa.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  resourcePolicy:
    containerPolicies:
    - containerName: console
      {{- with .Values.console.autoscaling.vpa.controlledResources }}
      controlledResources:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.console.autoscaling.vpa.maxAllowed }}
      maxAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.console.autoscaling.vpa.minAllowed }}
      minAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
    kind: Deployment
    name: {{ template "minio.console.fullname" . }}
  {{- if .Values.console.autoscaling.vpa.updatePolicy }}
  updatePolicy:
    {{- with .Values.console.autoscaling.vpa.updatePolicy.updateMode }}
    updateMode: {{ . }}
    {{- end }}
  {{- end }}
{{- end }}
