{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.provisioning.enabled .Values.provisioning.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ include "common.capabilities.networkPolicy.apiVersion" . }}
metadata:
  name: {{ printf "%s-provisioning" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" (dict "customLabels" .Values.commonLabels "context" .) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  podSelector:
    matchLabels:
      app.kubernetes.io/component: provisioning
      app.kubernetes.io/part-of: minio
  policyTypes:
    - Ingress
    - Egress
  {{- if .Values.provisioning.networkPolicy.allowExternalEgress }}
  egress:
    - {}
  {{- else }}
  egress:
    # Allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
    # Allow outbound connections to other cluster pods
    - ports:
        - port: {{ .Values.containerPorts.api }}
        - port: {{ .Values.service.ports.api }}
      to:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" (dict "customLabels" $podLabels "context" .) | nindent 14 }}
    {{- if .Values.provisioning.networkPolicy.extraEgress }}
    {{- include "common.tplvalues.render" (dict "value" .Values.dataCoord.networkPolicy.extraEgress "context" .) | nindent 4 }}
    {{- end }}
  {{- end }}
  ingress:
    {{- if .Values.provisioning.networkPolicy.extraIngress }}
    {{- include "common.tplvalues.render" (dict "value" .Values.initJob.networkPolicy.extraIngress "context" .) | nindent 4 }}
    {{- end }}
{{- end }}
