{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "minio.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" (dict "customLabels" .Values.commonLabels "context" .) | nindent 4 }}
    app.kubernetes.io/component: minio
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" .) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  root-user: {{ include "minio.secret.userValue" . }}
  root-password: {{ include "minio.secret.passwordValue" . }}
{{- end }}
