{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.metrics.enabled .Values.metrics.serviceMonitor.enabled }}
apiVersion: {{ default "monitoring.coreos.com/v1" .Values.metrics.serviceMonitor.apiVersion }}
kind: ServiceMonitor
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ default (include "common.names.namespace" .) .Values.metrics.serviceMonitor.namespace | quote }}
  {{- $labels := include "common.tplvalues.merge" (dict "values" (list .Values.metrics.serviceMonitor.labels .Values.commonLabels) "context" .) }}
  labels: {{- include "common.labels.standard" (dict "customLabels" $labels "context" .) | nindent 4 }}
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: minio
  {{- if or .Values.metrics.serviceMonitor.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.metrics.serviceMonitor.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    {{- range (.Values.metrics.serviceMonitor.paths | uniq) }}
    - port: tcp-api
      path: {{ . }}
      {{- if $.Values.metrics.serviceMonitor.interval }}
      interval: {{ $.Values.metrics.serviceMonitor.interval }}
      {{- end }}
      {{- if $.Values.metrics.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ $.Values.metrics.serviceMonitor.scrapeTimeout }}
      {{- end }}
      {{- if $.Values.metrics.serviceMonitor.honorLabels }}
      honorLabels: {{ $.Values.metrics.serviceMonitor.honorLabels }}
      {{- end }}
      {{- if $.Values.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings: {{- toYaml $.Values.metrics.serviceMonitor.metricRelabelings | nindent 8 }}
      {{- end }}
      {{- if $.Values.metrics.serviceMonitor.relabelings }}
      relabelings: {{- toYaml $.Values.metrics.serviceMonitor.relabelings | nindent 8 }}
      {{- end }}
      {{- if $.Values.tls.enabled }}
      scheme: https
      {{- end }}
      {{- if $.Values.metrics.serviceMonitor.tlsConfig }}
      tlsConfig: {{- toYaml $.Values.metrics.serviceMonitor.tlsConfig | nindent 8 }}
      {{- end }}
    {{- end }}
  {{- if .Values.metrics.serviceMonitor.jobLabel }}
  jobLabel: {{ .Values.metrics.serviceMonitor.jobLabel }}
  {{- end }}
  namespaceSelector:
    matchNames:
      - {{ include "common.names.namespace" . | quote }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" (dict "customLabels" .Values.commonLabels "context" .) | nindent 6 }}
      app.kubernetes.io/component: minio
      app.kubernetes.io/part-of: minio
      {{- if .Values.metrics.serviceMonitor.selector }}
      {{- include "common.tplvalues.render" (dict "value" .Values.metrics.serviceMonitor.selector "context" .) | nindent 6 }}
      {{- end }}
{{- end }}
