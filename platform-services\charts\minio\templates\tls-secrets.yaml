{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}


{{- if and .Values.tls.enabled .Values.tls.autoGenerated.enabled (eq .Values.tls.autoGenerated.engine "helm") -}}
{{- $ca := genCA "minio-ca" 365 }}
{{- $releaseNamespace := include "common.names.namespace" . }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $caSecretName := include "minio.tls.ca.secretName" . }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $caSecretName }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ include "common.secrets.lookup" (dict "secret" $caSecretName "key" "tls.crt" "defaultValue" $ca.Cert "context" .) }}
  tls.key: {{ include "common.secrets.lookup" (dict "secret" $caSecretName "key" "tls.key" "defaultValue" $ca.Key "context" .) }}
---
{{- $serverFullname := include "common.names.fullname" . }}
{{- $serverHlServiceName := printf "%s-headless" (include "common.names.fullname" .) }}
{{- $serverAltNames := list (printf "*.%s.%s.svc.%s" $serverHlServiceName $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $serverHlServiceName $releaseNamespace $clusterDomain) $serverHlServiceName (printf "%s.%s.svc.%s" $serverFullname $releaseNamespace $clusterDomain) $serverFullname "127.0.0.1" "localhost" }}
{{- $serverCert := genSignedCert $serverFullname nil $serverAltNames 365 $ca }}
{{- $serverSecretName := include "minio.tls.server.secretName" . }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $serverSecretName }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/component: minio
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ include "common.secrets.lookup" (dict "secret" $serverSecretName "key" "tls.crt" "defaultValue" $serverCert.Cert "context" .) }}
  tls.key: {{ include "common.secrets.lookup" (dict "secret" $serverSecretName "key" "tls.key" "defaultValue" $serverCert.Key "context" .) }}
{{- else if and .Values.tls.enabled (not .Values.tls.autoGenerated.enabled) (empty .Values.tls.existingCASecret) (empty .Values.tls.server.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "minio.tls.ca.secretName" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ required "A valid .Values.tls.ca entry required!" .Values.tls.ca | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "minio.tls.server.secretName" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" . ) | nindent 4 }}
    app.kubernetes.io/component: minio
    app.kubernetes.io/part-of: minio
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" . ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ required "A valid .Values.tls.server.cert entry required!" .Values.tls.server.cert | b64enc | quote }}
  tls.key: {{ required "A valid .Values.tls.server.key entry required!" .Values.tls.server.key | b64enc | quote }}
{{- end }}
