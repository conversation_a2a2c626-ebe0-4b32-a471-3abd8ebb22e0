{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and (include "common.capabilities.apiVersions.has" ( dict "version" "autoscaling.k8s.io/v1/VerticalPodAutoscaler" "context" . )) .Values.autoscaling.vpa.enabled }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" (dict "customLabels" .Values.commonLabels "context" .) | nindent 4 }}
    app.kubernetes.io/component: minio
    app.kubernetes.io/part-of: minio
  {{- if or .Values.autoscaling.vpa.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" ( list .Values.autoscaling.vpa.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" (dict "value" $annotations "context" .) | nindent 4 }}
  {{- end }}
spec:
  resourcePolicy:
    containerPolicies:
    - containerName: console
      {{- with .Values.autoscaling.vpa.controlledResources }}
      controlledResources:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.autoscaling.vpa.maxAllowed }}
      maxAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.autoscaling.vpa.minAllowed }}
      minAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: {{ ternary (include "common.capabilities.statefulset.apiVersion" .) (include "common.capabilities.deployment.apiVersion" .) (eq .Values.mode "distributed") }}
    kind: {{ ternary "StatefulSet" "Deployment" (eq .Values.mode "distributed") }}
    name: {{ template "common.names.fullname" . }}
  {{- if .Values.autoscaling.vpa.updatePolicy }}
  updatePolicy:
    {{- with .Values.autoscaling.vpa.updatePolicy.updateMode }}
    updateMode: {{ . }}
    {{- end }}
  {{- end }}
{{- end }}
