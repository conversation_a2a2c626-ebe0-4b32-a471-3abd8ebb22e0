# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.defaultStorageClass Global default StorageClass for Persistent Volume(s)
##
global:
  imageRegistry: ""
  ## e.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  defaultStorageClass: ""
  ## Security parameters
  ##
  security:
    ## @param global.security.allowInsecureImages Allows skipping image verification
    allowInsecureImages: false
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto

## @section Common parameters

## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)
##
nameOverride: ""
## @param namespaceOverride String to fully override common.names.namespace
##
namespaceOverride: ""
## @param fullnameOverride String to fully override common.names.fullname template
##
fullnameOverride: ""
## @param commonLabels Labels to add to all deployed objects
##
commonLabels: {}
## @param commonAnnotations Annotations to add to all deployed objects
##
commonAnnotations: {}
## @param kubeVersion Force target Kubernetes version (using Helm capabilities if not set)
##
kubeVersion: ""
## @param apiVersions Override Kubernetes API versions reported by .Capabilities
##
apiVersions: []
## @param clusterDomain Default Kubernetes cluster domain
##
clusterDomain: cluster.local
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []

## @section MinIO&reg; parameters

## Bitnami MinIO&reg; image version
## ref: https://hub.docker.com/r/bitnami/minio/tags/
## @param image.registry [default: REGISTRY_NAME] MinIO&reg; image registry
## @param image.repository [default: REPOSITORY_NAME/minio] MinIO&reg; image repository
## @skip image.tag MinIO&reg; image tag (immutable tags are recommended)
## @param image.digest MinIO&reg; image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
## @param image.pullPolicy Image pull policy
## @param image.pullSecrets Specify docker-registry secret names as an array
## @param image.debug Specify if debug logs should be enabled
##
image:
  registry: docker.io
  repository: bitnami/minio
  tag: 2025.7.23-debian-12-r3
  digest: ""
  ## Specify a imagePullPolicy
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ## e.g:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []
  ## Set to true if you would like to see extra information on logs
  ##
  debug: false
## Bitnami MinIO&reg; Client image version
## ref: https://hub.docker.com/r/bitnami/minio-client/tags/
## @param clientImage.registry [default: REGISTRY_NAME] MinIO&reg; Client image registry
## @param clientImage.repository [default: REPOSITORY_NAME/minio-client] MinIO&reg; Client image repository
## @skip clientImage.tag MinIO&reg; Client image tag (immutable tags are recommended)
## @param clientImage.digest MinIO&reg; Client image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
##
clientImage:
  registry: docker.io
  repository: bitnami/minio-client
  tag: 2025.7.21-debian-12-r2
  digest: ""
## MinIO&reg; authentication parameters
##
auth:
  ## @param auth.rootUser MinIO&reg; root username
  ##
  rootUser: admin
  ## @param auth.rootPassword Password for MinIO&reg; root user
  ##
  rootPassword: ""
  ## @param auth.existingSecret Use existing secret for credentials details (`auth.rootUser` and `auth.rootPassword` will be ignored and picked up from this secret).
  ##
  existingSecret: ""
  ## @param auth.rootUserSecretKey Key where the MINIO_ROOT_USER username is being stored inside the existing secret `auth.existingSecret`
  ##
  rootUserSecretKey: ""
  ## @param auth.rootPasswordSecretKey Key where the MINIO_ROOT_USER password is being stored inside the existing secret `auth.existingSecret`
  ##
  rootPasswordSecretKey: ""
  ## @param auth.forcePassword Force users to specify required passwords
  ##
  forcePassword: false
  ## @param auth.usePasswordFiles Mount credentials as a files instead of using an environment variable
  ##
  usePasswordFiles: true
  ## @param auth.useSecret Uses a secret to mount the credential files.
  ##
  useSecret: true
  ## @param auth.forceNewKeys Force root credentials (user and password) to be reconfigured every time they change in the secrets
  ##
  forceNewKeys: false
## @param defaultBuckets Comma, semi-colon or space separated list of buckets to create at initialization (only in standalone mode)
## e.g:
## defaultBuckets: "my-bucket, my-second-bucket"
##
defaultBuckets: ""
## @param tls.enabled Enable TLS configuration for MinIO&reg;
## @param tls.autoGenerated.enabled Enable automatic generation of TLS certificates
## @param tls.autoGenerated.engine Mechanism to generate the certificates (allowed values: helm, cert-manager)
## @param tls.autoGenerated.certManager.existingIssuer The name of an existing Issuer to use for generating the certificates (only for `cert-manager` engine)
## @param tls.autoGenerated.certManager.existingIssuerKind Existing Issuer kind, defaults to Issuer (only for `cert-manager` engine)
## @param tls.autoGenerated.certManager.keyAlgorithm Key algorithm for the certificates (only for `cert-manager` engine)
## @param tls.autoGenerated.certManager.keySize Key size for the certificates (only for `cert-manager` engine)
## @param tls.autoGenerated.certManager.duration Duration for the certificates (only for `cert-manager` engine)
## @param tls.autoGenerated.certManager.renewBefore Renewal period for the certificates (only for `cert-manager` engine)
## @param tls.ca CA certificate for TLS. Ignored if `tls.existingCASecret` is set
## @param tls.existingCASecret The name of an existing Secret containing the CA certificate for TLS
## @param tls.server.cert TLS certificate for MinIO&reg; servers. Ignored if `tls.server.existingSecret` is set
## @param tls.server.key TLS key for MinIO&reg; servers. Ignored if `tls.server.existingSecret` is set
## @param tls.server.existingSecret The name of an existing Secret containing the TLS certificates for MinIO&reg; servers
##
tls:
  enabled: false
  autoGenerated:
    enabled: true
    engine: helm
    certManager:
      existingIssuer: ""
      existingIssuerKind: ""
      keySize: 2048
      keyAlgorithm: RSA
      duration: 2160h
      renewBefore: 360h
  ca: ""
  existingCASecret: ""
  server:
    cert: ""
    key: ""
    existingSecret: ""

## @param extraEnvVars Extra environment variables to be set on MinIO&reg; container
## e.g:
## extraEnvVars:
##   - name: FOO
##     value: "bar"
##
extraEnvVars: []
## @param extraEnvVarsCM ConfigMap with extra environment variables
##
extraEnvVarsCM: ""
## @param extraEnvVarsSecret Secret with extra environment variables
##
extraEnvVarsSecret: ""
## @param command Default container command (useful when using custom images). Use array form
##
command: []
## @param args Default container args (useful when using custom images). Use array form
##
args: []

## @section MinIO&reg; Deployment/StatefulSet parameters

## @param mode MinIO&reg; server mode (`standalone` or `distributed`)
## ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
##
mode: standalone
## @param schedulerName Specifies the schedulerName, if it's nil uses kube-scheduler
## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
schedulerName: ""
## @param terminationGracePeriodSeconds In seconds, time the given to the MinIO pod needs to terminate gracefully
## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods
##
terminationGracePeriodSeconds: ""
## @param updateStrategy.type MinIO&reg; deployment/statefulset update strategy type
## Can be set to RollingUpdate or Recreate (deployment) | RollingUpdate or OnDelete (statefulset)
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
##
updateStrategy:
  type: RollingUpdate
## MinIO&reg; StatefulSet parameters
## Only when mode is 'distributed'
##
statefulset:
  ## @param statefulset.podManagementPolicy StatefulSet controller supports relax its ordering guarantees while preserving its uniqueness and identity guarantees. There are two valid pod management policies: OrderedReady and Parallel
  ## ref: https://kubernetes.io/docs/tutorials/stateful-application/basic-stateful-set/#pod-management-policy
  ##
  podManagementPolicy: Parallel
  ## @param statefulset.replicaCount Number of pods per zone (only for MinIO&reg; distributed mode). Should be even and `>= 4`
  ##
  replicaCount: 4
  ## @param statefulset.zones Number of zones (only for MinIO&reg; distributed mode)
  ##
  zones: 1
  ## @param statefulset.drivesPerNode Number of drives attached to every node (only for MinIO&reg; distributed mode)
  ##
  drivesPerNode: 1
## @param automountServiceAccountToken Mount Service Account token in pod
##
automountServiceAccountToken: false
## @param hostAliases MinIO&reg; pod host aliases
## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
##
hostAliases: []
## @param containerPorts.api MinIO&reg; container port to open for MinIO&reg; API
##
containerPorts:
  api: 9000
## MinIO&reg; pod Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
## @param podSecurityContext.enabled Enable pod Security Context
## @param podSecurityContext.sysctls Set kernel settings using the sysctl interface
## @param podSecurityContext.supplementalGroups Set filesystem extra groups
## @param podSecurityContext.fsGroup Group ID for the container
## @param podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
## @param podSecurityContext.sysctls Set kernel settings using the sysctl interface
## @param podSecurityContext.supplementalGroups Set filesystem extra groups
## @param podSecurityContext.fsGroupChangePolicy When K8s should preform chown on attached volumes
##
podSecurityContext:
  enabled: true
  sysctls: []
  supplementalGroups: []
  fsGroup: 1001
  fsGroupChangePolicy: "OnRootMismatch"
## MinIO&reg; container Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
## @param containerSecurityContext.enabled Enabled containers' Security Context
## @param containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
## @param containerSecurityContext.runAsUser Set containers' Security Context runAsUser
## @param containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
## @param containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
## @param containerSecurityContext.privileged Set container's Security Context privileged
## @param containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
## @param containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
## @param containerSecurityContext.capabilities.drop List of capabilities to be dropped
## @param containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
##
containerSecurityContext:
  enabled: true
  seLinuxOptions: {}
  runAsUser: 1001
  runAsGroup: 1001
  runAsNonRoot: true
  privileged: false
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop: ["ALL"]
  seccompProfile:
    type: "RuntimeDefault"
## @param podLabels Extra labels for MinIO&reg; pods
## Ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
##
podLabels: {}
## @param podAnnotations Annotations for MinIO&reg; pods
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
##
podAnnotations: {}
## @param podAffinityPreset Pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAffinityPreset: ""
## @param podAntiAffinityPreset Pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAntiAffinityPreset: soft
## Node affinity preset
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
##
nodeAffinityPreset:
  ## @param nodeAffinityPreset.type Node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ##
  type: ""
  ## @param nodeAffinityPreset.key Node label key to match. Ignored if `affinity` is set.
  ## E.g.
  ## key: "kubernetes.io/e2e-az-name"
  ##
  key: ""
  ## @param nodeAffinityPreset.values Node label values to match. Ignored if `affinity` is set.
  ## E.g.
  ## values:
  ##   - e2e-az1
  ##   - e2e-az2
  ##
  values: []
## @param affinity Affinity for pod assignment. Evaluated as a template.
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
## Note: podAffinityPreset, podAntiAffinityPreset, and nodeAffinityPreset will be ignored when it's set
##
affinity: {}
## @param nodeSelector Node labels for pod assignment. Evaluated as a template.
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
##
nodeSelector: {}
## @param tolerations Tolerations for pod assignment. Evaluated as a template.
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
##
tolerations: []
## @param topologySpreadConstraints Topology Spread Constraints for MinIO&reg; pods assignment spread across your cluster among failure-domains
## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
##
topologySpreadConstraints: []
## @param priorityClassName MinIO&reg; pods' priorityClassName
##
priorityClassName: ""
## @param runtimeClassName Name of the runtime class to be used by MinIO&reg; pods'
## ref: https://kubernetes.io/docs/concepts/containers/runtime-class/
##
runtimeClassName: ""
## MinIO&reg; containers' resource requests and limits
## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
## We usually recommend not to specify default resources and to leave this as a conscious
## choice for the user. This also increases chances charts run on environments with little
## resources, such as Minikube. If you do want to specify resources, uncomment the following
## lines, adjust them as necessary, and remove the curly braces after 'resources:'.
## @param resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
##
resourcesPreset: "micro"
## @param resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
## Example:
## resources:
##   requests:
##     cpu: 2
##     memory: 512Mi
##   limits:
##     cpu: 3
##     memory: 1024Mi
##
resources: {}
## Configure extra options for liveness probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param livenessProbe.enabled Enable livenessProbe
## @param livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
## @param livenessProbe.periodSeconds Period seconds for livenessProbe
## @param livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
## @param livenessProbe.failureThreshold Failure threshold for livenessProbe
## @param livenessProbe.successThreshold Success threshold for livenessProbe
##
livenessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
## Configure extra options for readiness probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param readinessProbe.enabled Enable readinessProbe
## @param readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
## @param readinessProbe.periodSeconds Period seconds for readinessProbe
## @param readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
## @param readinessProbe.failureThreshold Failure threshold for readinessProbe
## @param readinessProbe.successThreshold Success threshold for readinessProbe
##
readinessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 1
  successThreshold: 1
  failureThreshold: 5
## Configure extra options for startupProbe probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param startupProbe.enabled Enable startupProbe
## @param startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
## @param startupProbe.periodSeconds Period seconds for startupProbe
## @param startupProbe.timeoutSeconds Timeout seconds for startupProbe
## @param startupProbe.failureThreshold Failure threshold for startupProbe
## @param startupProbe.successThreshold Success threshold for startupProbe
##
startupProbe:
  enabled: false
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 60
## @param customLivenessProbe Override default liveness probe
##
customLivenessProbe: {}
## @param customReadinessProbe Override default readiness probe
##
customReadinessProbe: {}
## @param customStartupProbe Override default startup probe
##
customStartupProbe: {}
## @param lifecycleHooks for the MinIO&reg container(s) to automate configuration before or after startup
##
lifecycleHooks: {}
## @param extraVolumes Optionally specify extra list of additional volumes for MinIO&reg; pods
##
extraVolumes: []
## @param extraVolumeMounts Optionally specify extra list of additional volumeMounts for MinIO&reg; container(s)
##
extraVolumeMounts: []
## @param initContainers Add additional init containers to the MinIO&reg; pods
## e.g:
## initContainers:
##   - name: your-image-name
##     image: your-image
##     imagePullPolicy: Always
##     ports:
##       - name: portname
##         containerPort: 1234
##
initContainers: []
## @param sidecars Add additional sidecar containers to the MinIO&reg; pods
## e.g:
## sidecars:
##   - name: your-image-name
##     image: your-image
##     imagePullPolicy: Always
##     ports:
##       - name: portname
##         containerPort: 1234
##
sidecars: []
## MinIO&reg; Pod Disruption Budget configuration in distributed mode.
## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
##
pdb:
  ## @param pdb.create Enable/disable a Pod Disruption Budget creation
  ##
  create: true
  ## @param pdb.minAvailable Minimum number/percentage of pods that must still be available after the eviction
  ##
  minAvailable: ""
  ## @param pdb.maxUnavailable Maximum number/percentage of pods that may be made unavailable after the eviction
  ##
  maxUnavailable: ""
## Autoscaling configuration
## ref: https://kubernetes.io/docs/concepts/workloads/autoscaling/
##
autoscaling:
  ## @param autoscaling.vpa.enabled Enable VPA for MinIO&reg; pods
  ## @param autoscaling.vpa.annotations Annotations for VPA resource
  ## @param autoscaling.vpa.controlledResources VPA List of resources that the vertical pod autoscaler can control. Defaults to cpu and memory
  ## @param autoscaling.vpa.maxAllowed VPA Max allowed resources for the pod
  ## @param autoscaling.vpa.minAllowed VPA Min allowed resources for the pod
  ##
  vpa:
    enabled: false
    annotations: {}
    controlledResources: []
    maxAllowed: {}
    minAllowed: {}
    ## @param autoscaling.vpa.updatePolicy.updateMode Autoscaling update policy
    ## Specifies whether recommended updates are applied when a Pod is started and whether recommended updates are applied during the life of a Pod
    ## Possible values are "Off", "Initial", "Recreate", and "Auto".
    ##
    updatePolicy:
      updateMode: Auto
## Default init Containers
##
defaultInitContainers:
  ## 'volume-permissions' init container
  ## Used to change the owner and group of the persistent volume(s) mountpoint(s) to 'runAsUser:fsGroup' on each node
  ##
  volumePermissions:
    ## @param defaultInitContainers.volumePermissions.enabled Enable init container that changes the owner and group of the persistent volume
    ##
    enabled: false
    ## @param defaultInitContainers.volumePermissions.image.registry [default: REGISTRY_NAME] "volume-permissions" init-containers' image registry
    ## @param defaultInitContainers.volumePermissions.image.repository [default: REPOSITORY_NAME/os-shell] "volume-permissions" init-containers' image repository
    ## @skip defaultInitContainers.volumePermissions.image.tag "volume-permissions" init-containers' image tag (immutable tags are recommended)
    ## @param defaultInitContainers.volumePermissions.image.digest "volume-permissions" init-containers' image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
    ## @param defaultInitContainers.volumePermissions.image.pullPolicy "volume-permissions" init-containers' image pull policy
    ## @param defaultInitContainers.volumePermissions.image.pullSecrets "volume-permissions" init-containers' image pull secrets
    ##
    image:
      registry: docker.io
      repository: bitnami/os-shell
      tag: 12-debian-12-r50
      digest: ""
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    ## Configure "volume-permissions" init-container Security Context
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.enabled Enabled "volume-permissions" init-containers' Security Context
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in "volume-permissions" init-containers
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.runAsUser Set runAsUser in "volume-permissions" init-containers' Security Context
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.privileged Set privileged in "volume-permissions" init-containers' Security Context
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.allowPrivilegeEscalation Set allowPrivilegeEscalation in "volume-permissions" init-containers' Security Context
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.capabilities.add List of capabilities to be added in "volume-permissions" init-containers
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.capabilities.drop List of capabilities to be dropped in "volume-permissions" init-containers
    ## @param defaultInitContainers.volumePermissions.containerSecurityContext.seccompProfile.type Set seccomp profile in "volume-permissions" init-containers
    ##
    containerSecurityContext:
      enabled: true
      seLinuxOptions: {}
      runAsUser: 0
      privileged: false
      allowPrivilegeEscalation: false
      capabilities:
        add: ["CHOWN"]
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
    ## MinIO&reg; "volume-permissions" init container resource requests and limits
    ## ref: http://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
    ## @param defaultInitContainers.volumePermissions.resourcesPreset Set MinIO&reg; "volume-permissions" init container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if defaultInitContainers.volumePermissions.resources is set (defaultInitContainers.volumePermissions.resources is recommended for production).
    ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
    ##
    resourcesPreset: "nano"
    ## @param defaultInitContainers.volumePermissions.resources Set MinIO&reg; "volume-permissions" init container requests and limits for different resources like CPU or memory (essential for production workloads)
    ## E.g:
    ## resources:
    ##   requests:
    ##     cpu: 2
    ##     memory: 512Mi
    ##   limits:
    ##     cpu: 3
    ##     memory: 1024Mi
    ##
    resources: {}

## @section MinIO&reg; Traffic exposure parameters

## MinIO&reg; Service properties
##
service:
  ## @param service.type MinIO&reg; service type
  ##
  type: ClusterIP
  ## @param service.ports.api MinIO&reg; API service port
  ##
  ports:
    api: 9000
  ## @param service.nodePorts.api Specify the MinIO&reg API nodePort value for the LoadBalancer and NodePort service types
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  ##
  nodePorts:
    api: ""
  ## @param service.clusterIP Service Cluster IP
  ## e.g.:
  ## clusterIP: None
  ##
  clusterIP: ""
  ## @param service.loadBalancerIP loadBalancerIP if service type is `LoadBalancer` (optional, cloud specific)
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
  ##
  loadBalancerIP: ""
  ## @param service.loadBalancerSourceRanges Addresses that are allowed when service is LoadBalancer
  ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
  ## e.g:
  ## loadBalancerSourceRanges:
  ##   - **********/24
  ##
  loadBalancerSourceRanges: []
  ## @param service.externalTrafficPolicy Enable client source IP preservation
  ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
  ##
  externalTrafficPolicy: Cluster
  ## @param service.extraPorts Extra ports to expose in the service (normally used with the `sidecar` value)
  ##
  extraPorts: []
  ## @param service.annotations Annotations for MinIO&reg; service
  ## This can be used to set the LoadBalancer service type to internal only.
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
  ##
  annotations: {}
  ## Headless service properties
  ##
  headless:
    ## @param service.headless.annotations Annotations for the headless service
    ##
    annotations: {}
## Configure the ingress resource that allows you to access the MinIO&reg; API
## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
##
ingress:
  ## @param ingress.enabled Enable ingress controller resource for MinIO API
  ##
  enabled: false
  ## @param ingress.apiVersion Force Ingress API version (automatically detected if not set)
  ##
  apiVersion: ""
  ## @param ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)
  ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster.
  ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/
  ##
  ingressClassName: ""
  ## @param ingress.hostname Default host for the ingress resource
  ##
  hostname: minio.local
  ## @param ingress.path The Path to MinIO&reg;. You may need to set this to '/*' in order to use this with ALB ingress controllers.
  ##
  path: /
  ## @param ingress.pathType Ingress path type
  ##
  pathType: ImplementationSpecific
  ## @param ingress.annotations Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.
  ## For a full list of possible ingress annotations, please see
  ## ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/nginx-configuration/annotations.md
  ## Use this parameter to set the required annotations for cert-manager, see
  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
  ##
  ## e.g:
  ## annotations:
  ##   kubernetes.io/ingress.class: nginx
  ##   cert-manager.io/cluster-issuer: cluster-issuer-name
  ##
  annotations: {}
  ## @param ingress.tls Enable TLS configuration for the hostname defined at `ingress.hostname` parameter
  ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf "%s-tls" .Values.ingress.hostname }}`
  ## You can:
  ##   - Use the `ingress.secrets` parameter to create this TLS secret
  ##   - Rely on cert-manager to create it by setting the corresponding annotations
  ##   - Rely on Helm to create self-signed certificates by setting `ingress.selfSigned=true`
  ##
  tls: false
  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
  ##
  selfSigned: false
  ## @param ingress.extraHosts The list of additional hostnames to be covered with this ingress record.
  ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
  ## e.g:
  ## extraHosts:
  ##   - name: minio.local
  ##     path: /
  ##
  extraHosts: []
  ## @param ingress.extraPaths Any additional paths that may need to be added to the ingress under the main host
  ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
  ## extraPaths:
  ## - path: /*
  ##   backend:
  ##     serviceName: ssl-redirect
  ##     servicePort: use-annotation
  ##
  extraPaths: []
  ## @param ingress.extraTls The tls configuration for additional hostnames to be covered with this ingress record.
  ## see: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  ## e.g:
  ## extraTls:
  ## - hosts:
  ##     - minio.local
  ##   secretName: minio.local-tls
  ##
  extraTls: []
  ## @param ingress.secrets If you're providing your own certificates, please use this to add the certificates as secrets
  ## key and certificate are expected in PEM format
  ## name should line up with a secretName set further up
  ##
  ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates
  ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days
  ## It is also possible to create and manage the certificates outside of this helm chart
  ## Please see README.md for more information
  ##
  ## Example
  ## secrets:
  ##   - name: minio.local-tls
  ##     key: ""
  ##     certificate: ""
  ##
  secrets: []
  ## @param ingress.extraRules Additional rules to be covered with this ingress record
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#ingress-rules
  ## e.g:
  ## extraRules:
  ## - host: example.local
  ##     http:
  ##       path: /
  ##       backend:
  ##         service:
  ##           name: example-svc
  ##           port:
  ##             name: http
  ##
  extraRules: []
## Network Policy configuration
## ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
##
networkPolicy:
  ## @param networkPolicy.enabled Enable creation of NetworkPolicy for MinIO&reg;
  ##
  enabled: true
  ## @param networkPolicy.allowExternal Don't require server label for connections
  ## The Policy model to apply. When set to false, only pods with the correct
  ## server label will have network access to the ports server is listening
  ## on. When true, server will accept connections from any source
  ## (with the correct destination port).
  ##
  allowExternal: true
  ## @param networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
  ##
  allowExternalEgress: true
  ## @param networkPolicy.addExternalClientAccess Allow access from pods with client label set to "true". Ignored if `networkPolicy.allowExternal` is true.
  ##
  addExternalClientAccess: true
  ## @param networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraIngress:
  ##   - ports:
  ##       - port: 1234
  ##     from:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraIngress: []
  ## @param networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraEgress:
  ##   - ports:
  ##       - port: 1234
  ##     to:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraEgress: []
  ## @param networkPolicy.ingressPodMatchLabels [object] Labels to match to allow traffic from other pods. Ignored if `networkPolicy.allowExternal` is true.
  ## e.g:
  ## ingressPodMatchLabels:
  ##   my-client: "true"
  #
  ingressPodMatchLabels: {}
  ## @param networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces. Ignored if `networkPolicy.allowExternal` is true.
  ## @param networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces. Ignored if `networkPolicy.allowExternal` is true.
  ##
  ingressNSMatchLabels: {}
  ingressNSPodMatchLabels: {}

## @section MinIO&reg; Persistence parameters

## Enable persistence using Persistent Volume Claims
## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
##
persistence:
  ## @param persistence.enabled Enable MinIO&reg; data persistence using PVC. If false, use emptyDir
  ##
  enabled: true
  ## @param persistence.storageClass PVC Storage Class for MinIO&reg; data volume
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: ""
  ## @param persistence.mountPath Data volume mount path
  ##
  mountPath: /bitnami/minio/data
  ## @param persistence.accessModes PVC Access Modes for MinIO&reg; data volume
  ##
  accessModes:
    - ReadWriteOnce
  ## @param persistence.size PVC Storage Request for MinIO&reg; data volume
  ##
  size: 8Gi
  ## @param persistence.annotations Annotations for the PVC
  ##
  annotations: {}
  ## @param persistence.existingClaim Name of an existing PVC to use (only in `standalone` mode)
  ##
  existingClaim: ""
  ## @param persistence.selector Selector to match an existing Persistent Volume for MinIO&reg; data PVC
  ## If set, the PVC can't have a PV dynamically provisioned for it
  ## E.g.
  ## selector:
  ##   matchLabels:
  ##     app: my-app
  ##
  selector: {}
  ## @param persistence.dataSource Custom PVC data source
  ##
  dataSource: {}

## @section RBAC parameters

## Specifies whether a ServiceAccount should be created
##
serviceAccount:
  ## @param serviceAccount.create Enable the creation of a ServiceAccount for MinIO&reg; pods
  ##
  create: true
  ## @param serviceAccount.name Name of the created ServiceAccount
  ## If not set and create is true, a name is generated using the common.names.fullname template
  ##
  name: ""
  ## @param serviceAccount.automountServiceAccountToken Enable/disable auto mounting of the service account token
  ##
  automountServiceAccountToken: false
  ## @param serviceAccount.annotations Custom annotations for MinIO&reg; ServiceAccount
  ##
  annotations: {}

## @section Metrics parameters

metrics:
  ## @param metrics.prometheusAuthType Authentication mode for Prometheus (`jwt` or `public`)
  ## To allow public access without authentication for prometheus metrics set environment as follows.
  ##
  prometheusAuthType: public
  ## @param metrics.enabled Enable the export of Prometheus metrics
  ##
  enabled: false
  ## Prometheus Operator ServiceMonitor configuration
  ##
  serviceMonitor:
    ## @param metrics.serviceMonitor.enabled If the operator is installed in your cluster, set to true to create a Service Monitor Entry
    ##
    enabled: false
    ## @param metrics.serviceMonitor.namespace Namespace which Prometheus is running in
    ##
    namespace: ""
    ## @param metrics.serviceMonitor.labels Extra labels for the ServiceMonitor
    ##
    labels: {}
    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in Prometheus
    ##
    jobLabel: ""
    ## @param metrics.serviceMonitor.paths HTTP paths to scrape for metrics
    ##
    paths:
      - /minio/v2/metrics/cluster
      - /minio/v2/metrics/node
    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped
    ##
    interval: 30s
    ## @param metrics.serviceMonitor.scrapeTimeout Specify the timeout after which the scrape is ended
    ## e.g:
    ## scrapeTimeout: 30s
    scrapeTimeout: ""
    ## @param metrics.serviceMonitor.metricRelabelings MetricRelabelConfigs to apply to samples before ingestion
    ##
    metricRelabelings: []
    ## @param metrics.serviceMonitor.relabelings Metrics relabelings to add to the scrape endpoint, applied before scraping
    ##
    relabelings: []
    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.serviceMonitor.selector Prometheus instance selector labels
    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration
    ##
    selector: {}
    ## @param metrics.serviceMonitor.apiVersion ApiVersion for the serviceMonitor Resource (defaults to "monitoring.coreos.com/v1")
    apiVersion: ""
    ## @param metrics.serviceMonitor.tlsConfig Additional TLS configuration for metrics endpoint with "https" scheme
    ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#monitoring.coreos.com/v1.TLSConfig
    tlsConfig: {}
  ## Prometheus Operator PrometheusRule configuration
  ##
  prometheusRule:
    ## @param metrics.prometheusRule.enabled Create a Prometheus Operator PrometheusRule (also requires `metrics.enabled` to be `true` and `metrics.prometheusRule.rules`)
    ##
    enabled: false
    ## @param metrics.prometheusRule.namespace Namespace for the PrometheusRule Resource (defaults to the Release Namespace)
    ##
    namespace: ""
    ## @param metrics.prometheusRule.additionalLabels Additional labels that can be used so PrometheusRule will be discovered by Prometheus
    ##
    additionalLabels: {}
    ## @param metrics.prometheusRule.rules Prometheus Rule definitions
    # - alert: minio cluster nodes offline
    #   annotations:
    #     summary: "minio cluster nodes offline"
    #     description: "minio cluster nodes offline, pod {{`{{`}} $labels.pod {{`}}`}} service {{`{{`}} $labels.job {{`}}`}} offline"
    #   for: 10m
    #   expr: minio_cluster_nodes_offline_total > 0
    #   labels:
    #     severity: critical
    #     group: PaaS
    ##
    rules: []

## @section MinIO&reg; Console parameters

console:
  ## @param console.enabled Enable MinIO&reg; Console
  ##
  enabled: true
  ## Bitnami MinIO&reg; Console image
  ## ref: https://hub.docker.com/r/bitnami/minio-object-browser/tags/
  ## @param console.image.registry [default: REGISTRY_NAME] MinIO&reg; Console image registry
  ## @param console.image.repository [default: REPOSITORY_NAME/minio-object-browser] MinIO&reg; Console image repository
  ## @skip console.image.tag MinIO&reg; Console image tag (immutable tags are recommended)
  ## @param console.image.digest MinIO&reg; Console image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag image tag (immutable tags are recommended)
  ## @param console.image.pullPolicy MinIO&reg; Console image pull policy
  ## @param console.image.pullSecrets MinIO&reg; Console image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/minio-object-browser
    tag: 2.0.2-debian-12-r3
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param console.replicaCount Number of MinIO&reg; Console replicas to deploy
  ##
  replicaCount: 1
  ## @param console.containerPorts.http MinIO&reg; Console HTTP container port
  ##
  containerPorts:
    http: 9090
  ## @param console.extraContainerPorts Optionally specify extra list of additional ports for MinIO&reg; Console containers
  ## e.g:
  ## extraContainerPorts:
  ##   - name: myservice
  ##     containerPort: 9090
  ##
  extraContainerPorts: []
  ## Configure extra options for MinIO&reg; Console containers' liveness and readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
  ## @param console.livenessProbe.enabled Enable livenessProbe on MinIO&reg; Console containers
  ## @param console.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param console.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param console.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param console.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param console.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param console.readinessProbe.enabled Enable readinessProbe on MinIO&reg; Console containers
  ## @param console.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param console.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param console.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param console.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param console.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param console.startupProbe.enabled Enable startupProbe on MinIO&reg; Console containers
  ## @param console.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param console.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param console.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param console.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param console.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 0
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 60
  ## @param console.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param console.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param console.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## MinIO&reg; Console resource requests and limits
  ## ref: http://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param console.resourcesPreset Set MinIO&reg; Console container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if console.resources is set (console.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param console.resources Set MinIO&reg; Console container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param console.podSecurityContext.enabled Enable MinIO&reg; Console pods' Security Context
  ## @param console.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy for MinIO&reg; Console pods
  ## @param console.podSecurityContext.sysctls Set kernel settings using the sysctl interface for MinIO&reg; Console pods
  ## @param console.podSecurityContext.supplementalGroups Set filesystem extra groups for MinIO&reg; Console pods
  ## @param console.podSecurityContext.fsGroup Set fsGroup in MinIO&reg; Console pods' Security Context
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param console.containerSecurityContext.enabled Enabled MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in MinIO&reg; Console container
  ## @param console.containerSecurityContext.runAsUser Set runAsUser in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.runAsGroup Set runAsGroup in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.runAsNonRoot Set runAsNonRoot in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.readOnlyRootFilesystem Set readOnlyRootFilesystem in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.privileged Set privileged in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.allowPrivilegeEscalation Set allowPrivilegeEscalation in MinIO&reg; Console container' Security Context
  ## @param console.containerSecurityContext.capabilities.drop List of capabilities to be dropped in MinIO&reg; Console container
  ## @param console.containerSecurityContext.seccompProfile.type Set seccomp profile in MinIO&reg; Console container
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: true
    privileged: false
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param console.command Override default MinIO&reg; Console container command (useful when using custom images)
  ##
  command: []
  ## @param console.args Override default MinIO&reg; Console container args (useful when using custom images)
  ##
  args: []
  ## @param console.automountServiceAccountToken Mount Service Account token in MinIO&reg; Console pods
  ##
  automountServiceAccountToken: false
  ## @param console.hostAliases MinIO&reg; Console pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param console.deploymentAnnotations Annotations for MinIO&reg; Console deployment
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  deploymentAnnotations: {}
  ## @param console.podLabels Extra labels for MinIO&reg; Console pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param console.podAnnotations Annotations for MinIO&reg; Console pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param console.podAffinityPreset Pod affinity preset. Ignored if `console.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param console.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `console.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node console.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param console.nodeAffinityPreset.type Node affinity preset type. Ignored if `console.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param console.nodeAffinityPreset.key Node label key to match. Ignored if `console.affinity` is set
    ##
    key: ""
    ## @param console.nodeAffinityPreset.values Node label values to match. Ignored if `console.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param console.affinity Affinity for MinIO&reg; Console pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `console.podAffinityPreset`, `console.podAntiAffinityPreset`, and `console.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param console.nodeSelector Node labels for MinIO&reg; Console pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param console.tolerations Tolerations for MinIO&reg; Console pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param console.updateStrategy.type MinIO&reg; Console deployment strategy type
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy
  ##
  updateStrategy:
    ## Can be set to RollingUpdate or Recreate
    ##
    type: RollingUpdate
  ## @param console.priorityClassName MinIO&reg; Console pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param console.topologySpreadConstraints Topology Spread Constraints for MinIO&reg; Console pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param console.schedulerName Name of the k8s scheduler (other than default) for MinIO&reg; Console pods
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param console.terminationGracePeriodSeconds Seconds MinIO&reg; Console pods need to terminate gracefully
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods
  ##
  terminationGracePeriodSeconds: ""
  ## @param console.lifecycleHooks for MinIO&reg; Console containers to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param console.extraEnvVars Array with extra environment variables to add to MinIO&reg; Console containers
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param console.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for MinIO&reg; Console containers
  ##
  extraEnvVarsCM: ""
  ## @param console.extraEnvVarsSecret Name of existing Secret containing extra env vars for MinIO&reg; Console containers
  ##
  extraEnvVarsSecret: ""
  ## @param console.extraVolumes Optionally specify extra list of additional volumes for the MinIO&reg; Console pods
  ##
  extraVolumes: []
  ## @param console.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the MinIO&reg; Console containers
  ##
  extraVolumeMounts: []
  ## @param console.sidecars Add additional sidecar containers to the MinIO&reg; Console pods
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param console.initContainers Add additional init containers to the MinIO&reg; Console pods
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## Pod Disruption Budget configuration
  ## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb
  ## @param console.pdb.create Enable/disable a Pod Disruption Budget creation
  ## @param console.pdb.minAvailable Minimum number/percentage of pods that should remain scheduled
  ## @param console.pdb.maxUnavailable Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `console.pdb.minAvailable` and `console.pdb.maxUnavailable` are empty.
  ##
  pdb:
    create: true
    minAvailable: ""
    maxUnavailable: ""
  ## Autoscaling configuration
  ## ref: https://kubernetes.io/docs/concepts/workloads/autoscaling/
  ##
  autoscaling:
    ## @param console.autoscaling.vpa.enabled Enable VPA for MinIO&reg; Console pods
    ## @param console.autoscaling.vpa.annotations Annotations for VPA resource
    ## @param console.autoscaling.vpa.controlledResources VPA List of resources that the vertical pod autoscaler can control. Defaults to cpu and memory
    ## @param console.autoscaling.vpa.maxAllowed VPA Max allowed resources for the pod
    ## @param console.autoscaling.vpa.minAllowed VPA Min allowed resources for the pod
    ##
    vpa:
      enabled: false
      annotations: {}
      controlledResources: []
      maxAllowed: {}
      minAllowed: {}
      ## @param console.autoscaling.vpa.updatePolicy.updateMode Autoscaling update policy
      ## Specifies whether recommended updates are applied when a Pod is started and whether recommended updates are applied during the life of a Pod
      ## Possible values are "Off", "Initial", "Recreate", and "Auto".
      ##
      updatePolicy:
        updateMode: Auto
    ## @param console.autoscaling.hpa.enabled Enable HPA for MinIO&reg; Console pods
    ## @param console.autoscaling.hpa.minReplicas Minimum number of replicas
    ## @param console.autoscaling.hpa.maxReplicas Maximum number of replicas
    ## @param console.autoscaling.hpa.targetCPU Target CPU utilization percentage
    ## @param console.autoscaling.hpa.targetMemory Target Memory utilization percentage
    ##
    hpa:
      enabled: false
      minReplicas: ""
      maxReplicas: ""
      targetCPU: ""
      targetMemory: ""
  ## MinIO&reg; Console Service properties
  ##
  service:
    ## @param console.service.type MinIO&reg; Console service type
    ##
    type: ClusterIP
    ## @param console.service.ports.http MinIO&reg; Console HTTP service port
    ##
    ports:
      http: 9090
    ## @param console.service.nodePorts.http Specify the MinIO&reg; Console HTTP nodePort value
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
    ##
    nodePorts:
      http: ""
    ## @param console.service.clusterIP Service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param console.service.loadBalancerIP loadBalancerIP if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param console.service.loadBalancerSourceRanges Addresses that are allowed when service is LoadBalancer
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param console.service.externalTrafficPolicy Enable client source IP preservation
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param console.service.extraPorts Extra ports to expose in the service (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param console.service.annotations Annotations for MinIO&reg; Console service
    ## This can be used to set the LoadBalancer service type to internal only.
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    annotations: {}
  ## Configure the ingress resource that allows you to access the MinIO&reg; Console
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
  ##
  ingress:
    ## @param console.ingress.enabled Enable ingress controller resource for MinIO&reg; Console
    ##
    enabled: false
    ## @param console.ingress.apiVersion Force Ingress API version (automatically detected if not set)
    ##
    apiVersion: ""
    ## @param console.ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)
    ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster.
    ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/
    ##
    ingressClassName: ""
    ## @param console.ingress.hostname Default host for the ingress resource
    ##
    hostname: minio.local
    ## @param console.ingress.path The Path to MinIO&reg; Console. You may need to set this to '/*' in order to use this with ALB ingress controllers.
    ##
    path: /
    ## @param console.ingress.pathType Ingress path type
    ##
    pathType: ImplementationSpecific
    ## @param console.ingress.annotations Additional annotations for the Ingress resource. To enable certificate auto-generation, place here your cert-manager annotations.
    ## For a full list of possible ingress annotations, please see
    ## ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/nginx-configuration/annotations.md
    ## Use this parameter to set the required annotations for cert-manager, see
    ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
    ##
    ## e.g:
    ## annotations:
    ##   kubernetes.io/ingress.class: nginx
    ##   cert-manager.io/cluster-issuer: cluster-issuer-name
    ##
    annotations: {}
    ## @param console.ingress.tls Enable TLS configuration for the hostname defined at `ingress.hostname` parameter
    ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf "%s-tls" .Values.console.ingress.hostname }}`
    ## You can:
    ##   - Use the `ingress.secrets` parameter to create this TLS secret
    ##   - Rely on cert-manager to create it by setting the corresponding annotations
    ##   - Rely on Helm to create self-signed certificates by setting `ingress.selfSigned=true`
    ##
    tls: false
    ## @param console.ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
    ##
    selfSigned: false
    ## @param console.ingress.extraHosts The list of additional hostnames to be covered with this ingress record.
    ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
    ## e.g:
    ## extraHosts:
    ##   - name: minio.local
    ##     path: /
    ##
    extraHosts: []
    ## @param console.ingress.extraPaths Any additional paths that may need to be added to the ingress under the main host
    ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
    ## @param console.ingress.extraTls The tls configuration for additional hostnames to be covered with this ingress record.
    ## see: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
    ## e.g:
    ## extraTls:
    ## - hosts:
    ##     - minio.local
    ##   secretName: minio.local-tls
    ##
    extraTls: []
    ## @param console.ingress.secrets If you're providing your own certificates, please use this to add the certificates as secrets
    ## key and certificate are expected in PEM format
    ## name should line up with a secretName set further up
    ##
    ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates
    ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days
    ## It is also possible to create and manage the certificates outside of this helm chart
    ## Please see README.md for more information
    ##
    ## Example
    ## secrets:
    ##   - name: minio.local-tls
    ##     key: ""
    ##     certificate: ""
    ##
    secrets: []
    ## @param console.ingress.extraRules Additional rules to be covered with this ingress record
    ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#ingress-rules
    ## e.g:
    ## extraRules:
    ## - host: example.local
    ##     http:
    ##       path: /
    ##       backend:
    ##         service:
    ##           name: example-svc
    ##           port:
    ##             name: http
    ##
    extraRules: []
  ## Network Policy configuration
  ## ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param console.networkPolicy.enabled Enable creation of NetworkPolicy for MinIO&reg; Console
    ##
    enabled: true
    ## @param console.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param console.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param console.networkPolicy.addExternalClientAccess Allow access from pods with client label set to "true". Ignored if `console.networkPolicy.allowExternal` is true.
    ##
    addExternalClientAccess: true
    ## @param console.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraIngress: []
    ## @param console.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param console.networkPolicy.ingressPodMatchLabels [object] Labels to match to allow traffic from other pods. Ignored if `console.networkPolicy.allowExternal` is true.
    ## e.g:
    ## ingressPodMatchLabels:
    ##   my-client: "true"
    #
    ingressPodMatchLabels: {}
    ## @param console.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces. Ignored if `console.networkPolicy.allowExternal` is true.
    ## @param console.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces. Ignored if `console.networkPolicy.allowExternal` is true.
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}

## @section MinIO&reg; provisioning parameters

provisioning:
  ## @param provisioning.enabled Enable MinIO&reg; provisioning Job
  ##
  enabled: false
  ## @param provisioning.sleepTime Sleep time before checking Minio availability
  ##
  sleepTime: 5
  ## @param provisioning.schedulerName Name of the k8s scheduler (other than default) for MinIO&reg; provisioning
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param provisioning.nodeSelector Node labels for pod assignment. Evaluated as a template.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/assign-pods-nodes/
  ##
  nodeSelector: {}
  ## @param provisioning.podLabels Extra labels for provisioning pods
  ## Ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param provisioning.podAnnotations Provisioning Pod annotations.
  ##
  podAnnotations: {}
  ## @param provisioning.command Default provisioning container command (useful when using custom images). Use array form
  ##
  command: []
  ## @param provisioning.args Default provisioning container args (useful when using custom images). Use array form
  ##
  args: []
  ## @param provisioning.extraCommands Optionally specify extra list of additional commands for MinIO&reg; provisioning pod
  ##
  extraCommands: []
  ## @param provisioning.extraVolumes Optionally specify extra list of additional volumes for MinIO&reg; provisioning pod
  ##
  extraVolumes: []
  ## @param provisioning.extraVolumeMounts Optionally specify extra list of additional volumeMounts for MinIO&reg; provisioning container
  ##
  extraVolumeMounts: []
  ## We usually recommend not to specify default resources and to leave this as a conscious
  ## choice for the user. This also increases chances charts run on environments with little
  ## resources, such as Minikube. If you do want to specify resources, uncomment the following
  ## lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  ## @param provisioning.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if provisioning.resources is set (provisioning.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param provisioning.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## @param provisioning.policies MinIO&reg; policies provisioning
  ## https://docs.min.io/docs/minio-admin-complete-guide.html#policy
  ## e.g.
  ## policies:
  ##   - name: custom-bucket-specific-policy
  ##     statements:
  ##       - resources:
  ##           - "arn:aws:s3:::my-bucket"
  ##         actions:
  ##           - "s3:GetBucketLocation"
  ##           - "s3:ListBucket"
  ##           - "s3:ListBucketMultipartUploads"
  ##       - resources:
  ##           - "arn:aws:s3:::my-bucket/*"
  ##         # Allowed values: "Allow" | "Deny"
  ##         # Defaults to "Deny" if not specified
  ##         effect: "Allow"
  ##         actions:
  ##           - "s3:AbortMultipartUpload"
  ##           - "s3:DeleteObject"
  ##           - "s3:GetObject"
  ##           - "s3:ListMultipartUploadParts"
  ##           - "s3:PutObject"
  ##         condition:
  ##           StringLike:
  ##               "s3:prefix":
  ##                 - "${aws:username}/*"
  policies: []
  ## @param provisioning.users MinIO&reg; users provisioning. Can be used in addition to provisioning.usersExistingSecrets.
  ## https://docs.min.io/docs/minio-admin-complete-guide.html#user
  ## e.g.
  ## users:
  ##   - username: test-username
  ##     password: test-password
  ##     disabled: false
  ##     policies:
  ##       - readwrite
  ##       - consoleAdmin
  ##       - diagnostics
  ##     # When set to true, it will replace all policies with the specified.
  ##     # When false, the policies will be added to the existing.
  ##     setPolicies: false
  users: []
  ## @param provisioning.usersExistingSecrets Array if existing secrets containing MinIO&reg; users to be provisioned. Can be used in addition to provisioning.users.
  ## https://docs.min.io/docs/minio-admin-complete-guide.html#user
  ##
  ## Instead of configuring users inside values.yaml, referring to existing Kubernetes secrets containing user
  ## configurations is possible.
  ## e.g.
  ## usersExistingSecrets:
  ##   - centralized-minio-users
  ##
  ## All provided Kubernetes secrets require a specific data structure. The same data from the provisioning.users example above
  ##  can be defined via secrets with the following data structure. The secret keys have no meaning to the provisioning job except that
  ## they are used as filenames.
  ##   ## apiVersion: v1
  ##   ## kind: Secret
  ##   ## metadata:
  ##   ##   name: centralized-minio-users
  ##   ## type: Opaque
  ##   ## stringData:
  ##   ##   username1: |
  ##   ##     username=test-username
  ##   ##     password=test-password
  ##   ##     disabled=false
  ##   ##     policies=readwrite,consoleAdmin,diagnostics
  ##   ##     setPolicies=false
  usersExistingSecrets: []
  ## @param provisioning.groups MinIO&reg; groups provisioning
  ## https://docs.min.io/docs/minio-admin-complete-guide.html#group
  ## e.g.
  ## groups
  ##   - name: test-group
  ##     disabled: false
  ##     members:
  ##       - test-username
  ##     policies:
  ##       - readwrite
  ##     # When set to true, it will replace all policies with the specified.
  ##     # When false, the policies will be added to the existing.
  ##     setPolicies: false
  groups: []
  ## @param provisioning.buckets MinIO&reg; buckets, versioning, lifecycle, quota and tags provisioning
  ## Buckets https://docs.min.io/docs/minio-client-complete-guide.html#mb
  ## Lifecycle https://docs.min.io/docs/minio-client-complete-guide.html#ilm
  ## Quotas https://docs.min.io/docs/minio-admin-complete-guide.html#bucket
  ## Tags https://docs.min.io/docs/minio-client-complete-guide.html#tag
  ## Versioning https://docs.min.io/docs/minio-client-complete-guide.html#version
  ## e.g.
  ## buckets:
  ##   - name: test-bucket
  ##     region: us-east-1
  ##     # Only when mode is 'distributed'
  ##     # Allowed values: "Versioned" | "Suspended" | "Unchanged"
  ##     # Defaults to "Suspended" if not specified.
  ##     # For compatibility, accepts boolean values as well, where true maps
  ##     # to "Versioned" and false to "Suspended".
  ##     # ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
  ##     versioning: Suspended
  ##     # Versioning is automatically enabled if withLock is true
  ##     # ref: https://docs.min.io/docs/minio-bucket-versioning-guide.html
  ##     withLock: true
  ##     # Only when mode is 'distributed'
  ##     # ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
  ##     lifecycle:
  ##       - id: TestPrefix7dRetention
  ##         prefix: test-prefix
  ##         disabled: false
  ##         expiry:
  ##           days: 7
  ##           # Days !OR! date
  ##           # date: "2021-11-11T00:00:00Z"
  ##           nonconcurrentDays: 3
  ##     # Only when mode is 'distributed'
  ##     # ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
  ##     quota:
  ##       # set (hard still works as an alias but is deprecated) or clear(+ omit size)
  ##       type: set
  ##       size: 10GiB
  ##     tags:
  ##       key1: value1
  buckets: []
  ## @param provisioning.config MinIO&reg; config provisioning
  ## https://docs.min.io/docs/minio-server-configuration-guide.html
  ## e.g.
  ## config:
  ##   - name: region
  ##     options:
  ##       name: us-east-1
  config: []
  ## MinIO&reg; pod Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param provisioning.podSecurityContext.enabled Enable pod Security Context
  ## @param provisioning.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param provisioning.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param provisioning.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param provisioning.podSecurityContext.fsGroup Group ID for the container
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## MinIO&reg; container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param provisioning.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param provisioning.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param provisioning.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param provisioning.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param provisioning.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param provisioning.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param provisioning.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param provisioning.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param provisioning.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param provisioning.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## Automatic Cleanup for Finished Jobs
  ## @param provisioning.cleanupAfterFinished.enabled Enables Cleanup for Finished Jobs
  ## @param provisioning.cleanupAfterFinished.seconds Sets the value of ttlSecondsAfterFinished
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/ttlafterfinished/
  ##
  cleanupAfterFinished:
    enabled: false
    seconds: 600
  ## Network Policy configuration
  ## ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param provisioning.networkPolicy.enabled Enable creation of NetworkPolicy resources
    ##
    enabled: true
    ## @param provisioning.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param provisioning.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraIngress: []
    ## @param provisioning.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
