apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "nexus-repository-manager.fullname" . }}
  labels:
    {{- include "nexus-repository-manager.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "nexus-repository-manager.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "nexus-repository-manager.selectorLabels" . | nindent 8 }}
    spec:
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      initContainers:
        {{- range .Values.initContainers }}
        - name: {{ .name }}
          image: {{ .image }}
          command:
            {{- toYaml .command | nindent 12 }}
          securityContext:
            {{- toYaml .securityContext | nindent 12 }}
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
        {{- end }}
      containers:
        - name: nexus
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          env:
            {{- toYaml .Values.env | nindent 12 }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: nexus-data
              mountPath: /nexus-data
            {{- if .Values.properties.override }}
            - name: nexus-properties
              mountPath: /opt/sonatype/nexus/etc/nexus.properties
              subPath: nexus.properties
            {{- end }}
      volumes:
        - name: nexus-data
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "nexus-repository-manager.fullname" . }}-data
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- if .Values.properties.override }}
        - name: nexus-properties
          configMap:
            name: {{ include "nexus-repository-manager.fullname" . }}-properties
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
