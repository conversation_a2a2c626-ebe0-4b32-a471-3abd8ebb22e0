apiVersion: v1
kind: Service
metadata:
  name: {{ include "nexus-repository-manager.fullname" . }}
  labels:
    {{- include "nexus-repository-manager.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "nexus-repository-manager.selectorLabels" . | nindent 4 }}
