# Nexus Repository Manager Helm Chart Values
# Sử dụng external PostgreSQL và cấu hình subpath routing

# Root level configuration for template compatibility
replicaCount: 1

image:
  repository: sonatype/nexus3
  tag: 3.68.1
  pullPolicy: IfNotPresent

# Environment variables
env:
  - name: NEXUS_CONTEXT
    value: ""
  - name: INSTALL4J_ADD_VM_PARAMS
    value: "-Xms2g -Xmx2g -XX:MaxDirectMemorySize=3g -Djava.util.prefs.userRoot=/nexus-data/javaprefs"
  - name: NEXUS_SECURITY_RANDOMPASSWORD
    value: "false"
  - name: NEXUS_DATASTORE_NEXUS_JDBCURL
    value: "***************************************************************************"
  - name: NEXUS_DATASTORE_NEXUS_USERNAME
    valueFrom:
      secretKeyRef:
        name: nexus-database-secret
        key: username
  - name: NEXUS_DATASTORE_NEXUS_PASSWORD
    valueFrom:
      secretKeyRef:
        name: nexus-database-secret
        key: password
  - name: NEXUS_DATASTORE_NEXUS_ADVANCED
    value: "maximumPoolSize=20"

# Service configuration (for template compatibility)
service:
  type: ClusterIP
  port: 8081
  targetPort: 8081

# Resources
resources:
  requests:
    cpu: 500m
    memory: 2Gi
  limits:
    cpu: 2000m
    memory: 4Gi

# Probes
livenessProbe:
  tcpSocket:
    port: 8081
  initialDelaySeconds: 240
  periodSeconds: 60
  failureThreshold: 6

readinessProbe:
  tcpSocket:
    port: 8081
  initialDelaySeconds: 240
  periodSeconds: 60
  failureThreshold: 6

# Persistence
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 50Gi

# Security context - Updated for Nexus 3.68.1 compatibility
securityContext:
  runAsUser: 200
  runAsGroup: 200
  fsGroup: 200

# Init containers
initContainers:
  - name: clean-h2-database
    image: busybox:1.35
    command: ['sh', '-c', 'rm -rf /nexus-data/db/nexus.* /nexus-data/db/OSystem /nexus-data/db/component /nexus-data/db/config /nexus-data/db/security /nexus-data/db/analytics || true']
    securityContext:
      runAsUser: 0
      runAsGroup: 0
    volumeMounts:
      - name: nexus-data
        mountPath: /nexus-data
  - name: fix-permissions
    image: busybox:1.35
    command: ['sh', '-c', 'chown -R 200:200 /nexus-data && chmod -R 755 /nexus-data && find /nexus-data -type f -exec chmod 644 {} \;']
    securityContext:
      runAsUser: 0
      runAsGroup: 0
    volumeMounts:
      - name: nexus-data
        mountPath: /nexus-data

# Properties configuration
properties:
  override: true
  data:
    nexus.scripts.allowCreation: "true"
    nexus.security.randompassword: "false"
    nexus.cleanup.retainDays: "30"
    # Reverse proxy configuration for subpath routing
    nexus.onboarding.enabled: "false"

# Node selector và tolerations
nodeSelector: {}
tolerations: []
affinity: {}

nexus-repository-manager:
  # Nexus image configuration
  image:
    repository: sonatype/nexus3
    tag: 3.61.0
    pullPolicy: IfNotPresent

  # Deployment configuration
  deployment:
    annotations: {}
    # Init containers to fix permissions and clean H2 database
    initContainers:
      - name: clean-h2-database
        image: busybox:1.35
        command: ['sh', '-c', 'rm -rf /nexus-data/db/nexus.* /nexus-data/db/OSystem /nexus-data/db/component /nexus-data/db/config /nexus-data/db/security /nexus-data/db/analytics || true']
        securityContext:
          runAsUser: 0
          runAsGroup: 0
        volumeMounts:
          - name: nexus-repository-manager-data
            mountPath: /nexus-data
      - name: fix-permissions
        image: busybox:1.35
        command: ['sh', '-c', 'chown -R 200:200 /nexus-data && chmod -R 755 /nexus-data && find /nexus-data -type f -exec chmod 644 {} \;']
        securityContext:
          runAsUser: 0
          runAsGroup: 0
        volumeMounts:
          - name: nexus-repository-manager-data
            mountPath: /nexus-data

  nexus:
    # Override default environment variables from upstream chart
    env:
    - name: NEXUS_CONTEXT
      value: ""
    - name: INSTALL4J_ADD_VM_PARAMS
      value: "-Xms2g -Xmx2g -XX:MaxDirectMemorySize=3g -Djava.util.prefs.userRoot=/nexus-data/javaprefs"
    # Reverse proxy configuration
    - name: NEXUS_SECURITY_RANDOMPASSWORD
      value: "false"
    # PostgreSQL database configuration via environment variables
    - name: NEXUS_DATASTORE_NEXUS_JDBCURL
      value: "***************************************************************************"
    - name: NEXUS_DATASTORE_NEXUS_USERNAME
      valueFrom:
        secretKeyRef:
          name: nexus-database-secret
          key: username
    - name: NEXUS_DATASTORE_NEXUS_PASSWORD
      valueFrom:
        secretKeyRef:
          name: nexus-database-secret
          key: password
    # Advanced PostgreSQL configuration
    - name: NEXUS_DATASTORE_NEXUS_ADVANCED
      value: "maximumPoolSize=20"

    # Resource limits và requests
    resources:
      requests:
        cpu: 500m
        memory: 2Gi
      limits:
        cpu: 2000m
        memory: 4Gi

    # Liveness và readiness probes
    livenessProbe:
      tcpSocket:
        port: 8081
      initialDelaySeconds: 240
      periodSeconds: 60
      failureThreshold: 6

    readinessProbe:
      tcpSocket:
        port: 8081
      initialDelaySeconds: 240
      periodSeconds: 60
      failureThreshold: 6

    # Service configuration
    service:
      type: ClusterIP
      port: 8081
      targetPort: 8081

  # Service configuration (for template compatibility)
  service:
    type: ClusterIP
    port: 8081
    targetPort: 8081

    # Persistence configuration
    persistence:
      enabled: true
      storageClass: ""
      accessMode: ReadWriteOnce
      size: 50Gi

    # Security context - Updated for Nexus 3.68.1 compatibility
    securityContext:
      runAsUser: 200
      runAsGroup: 200
      fsGroup: 200

    # External PostgreSQL configuration
    postgresql:
      enabled: false

    # Nexus configuration properties
    properties:
      override: true
      data:
        nexus.scripts.allowCreation: "true"
        nexus.security.randompassword: "false"
        nexus.cleanup.retainDays: "30"
        # Reverse proxy configuration for subpath routing
        nexus.onboarding.enabled: "false"

    # Secret configuration cho admin password
    secret:
      enabled: false

    # Ingress disabled - sử dụng Gateway API
    ingress:
      enabled: false

    # ServiceMonitor cho Prometheus (nếu cần)
    serviceMonitor:
      enabled: false

    # Node selector và tolerations
    nodeSelector: {}
    tolerations: []
    affinity: {}

    # Additional labels
    labels:
      app.kubernetes.io/name: nexus-repository-manager
      app.kubernetes.io/component: repository-manager
      app.kubernetes.io/part-of: platform-services
