{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "postgresql.v1.primary.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: metrics
  {{- if or .Values.commonAnnotations .Values.metrics.service.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  sessionAffinity: {{ .Values.metrics.service.sessionAffinity }}
  {{- if .Values.metrics.service.clusterIP }}
  clusterIP: {{ .Values.metrics.service.clusterIP }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ .Values.metrics.service.ports.metrics }}
      targetPort: http-metrics
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: primary
{{- end }}
