{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.primary.pdb.create }}
apiVersion: {{ include "common.capabilities.policy.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "postgresql.v1.primary.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: primary
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.primary.pdb.minAvailable }}
  minAvailable: {{ .Values.primary.pdb.minAvailable }}
  {{- end  }}
  {{- if or .Values.primary.pdb.maxUnavailable ( not .Values.primary.pdb.minAvailable ) }}
  maxUnavailable: {{ .Values.primary.pdb.maxUnavailable | default 1 }}
  {{- end  }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: primary
{{- end }}
