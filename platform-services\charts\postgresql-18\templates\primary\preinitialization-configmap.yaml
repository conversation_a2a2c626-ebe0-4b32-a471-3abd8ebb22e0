{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.primary.preInitDb.scripts (not .Values.primary.preInitDb.scriptsConfigMap) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-preinit-scripts" (include "postgresql.v1.primary.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data: {{- include "common.tplvalues.render" (dict "value" .Values.primary.preInitDb.scripts "context" .) | nindent 2 }}
{{- end }}
