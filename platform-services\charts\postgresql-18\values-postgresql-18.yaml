# PostgreSQL 18 Values - Song song với PostgreSQL 16
# <PERSON><PERSON><PERSON> hình để tránh conflict với PostgreSQL 16 hiện tại

## Global parameters
global:
  imageRegistry: ""
  imagePullSecrets: []
  defaultStorageClass: ""
  postgresql:
    auth:
      postgresPassword: "postgres123"
      username: ""
      password: ""
      database: "postgres"
      existingSecret: ""
      secretKeys:
        adminPasswordKey: "postgres-password"
        userPasswordKey: "password"
        replicationPasswordKey: "replication-password"
    service:
      ports:
        postgresql: "5433"  # Port khác với PostgreSQL 16 (5432)

## PostgreSQL 18 Image configuration
image:
  registry: docker.io
  repository: bitnami/postgresql
  tag: "18.0.0-debian-12-r0"  # PostgreSQL 18 image
  digest: ""
  pullPolicy: IfNotPresent
  pullSecrets: []
  debug: false

## PostgreSQL Authentication
auth:
  enablePostgresUser: true
  postgresPassword: "postgres123"
  username: ""
  password: ""
  database: "postgres"
  replicationUsername: replicator
  replicationPassword: ""
  existingSecret: ""
  secretKeys:
    adminPasswordKey: postgres-password
    userPasswordKey: password
    replicationPasswordKey: replication-password
  usePasswordFiles: false

## PostgreSQL 18 Architecture
architecture: standalone

## PostgreSQL Primary configuration
primary:
  ## Name of the primary database
  name: primary
  
  ## PostgreSQL 18 specific configuration
  configuration: |
    # PostgreSQL 18 specific settings
    shared_preload_libraries = 'pg_stat_statements'
    
    # Performance tuning
    shared_buffers = 256MB
    effective_cache_size = 1GB
    maintenance_work_mem = 64MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # PostgreSQL 18 new features
    log_connections = on
    log_disconnections = on
    log_lock_waits = on

  ## PostgreSQL Primary client authentication configuration
  pgHbaConfiguration: |
    local all all trust
    host all all 127.0.0.1/32 md5
    host all all ::1/128 md5
    host all all 0.0.0.0/0 md5

  ## Node affinity - chạy trên node khác với PostgreSQL 16
  nodeAffinityPreset:
    type: "hard"
    key: "kubernetes.io/hostname"
    values:
      - "warehouse03"  # Node khác với PostgreSQL 16 (warehouse02)

  ## Pod affinity để tránh cùng node với PostgreSQL 16
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app.kubernetes.io/name: postgresql
            topologyKey: kubernetes.io/hostname

  ## Service configuration
  service:
    type: ClusterIP
    ports:
      postgresql: 5433  # Port khác với PostgreSQL 16
    nodePorts:
      postgresql: ""
    clusterIP: ""
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    externalIPs: []
    annotations: {}
    sessionAffinity: None
    sessionAffinityConfig: {}

  ## Persistence configuration
  persistence:
    enabled: true
    mountPath: /bitnami/postgresql
    subPath: ""
    storageClass: "local-path"
    accessModes:
      - ReadWriteOnce
    size: 30Gi  # Size lớn hơn cho PostgreSQL 18
    annotations: {}
    labels: {}
    selector: {}
    dataSource: {}
    existingClaim: ""

  ## Resource configuration
  resources:
    limits:
      memory: "2Gi"
      cpu: "1"
    requests:
      memory: "1Gi"
      cpu: "500m"

  ## Liveness and readiness probes
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 6
    successThreshold: 1

  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 6
    successThreshold: 1

  ## Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001
    seccompProfile:
      type: RuntimeDefault

  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: RuntimeDefault

## Metrics configuration
metrics:
  enabled: true
  image:
    registry: docker.io
    repository: bitnami/postgres-exporter
    tag: 0.15.0-debian-12-r43
    digest: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  
  resources:
    limits:
      memory: "256Mi"
      cpu: "250m"
    requests:
      memory: "128Mi"
      cpu: "100m"

  service:
    type: ClusterIP
    ports:
      http: 9187
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "9187"

## Network Policy
networkPolicy:
  enabled: true
  allowExternal: true
  explicitNamespacesSelector: {}

## Service Account
serviceAccount:
  create: true
  name: ""
  automountServiceAccountToken: false
  annotations: {}

## Pod Disruption Budget
pdb:
  create: true
  minAvailable: ""
  maxUnavailable: 1

## Common labels
commonLabels:
  app.kubernetes.io/part-of: "postgresql-migration"
  migration.version: "18"

## Common annotations
commonAnnotations:
  postgresql.version: "18.0.0"
  deployment.purpose: "migration-testing"
