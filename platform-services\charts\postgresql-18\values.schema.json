{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"architecture": {"type": "string", "title": "PostgreSQL architecture", "form": true, "description": "Allowed values: `standalone` or `replication`"}, "auth": {"type": "object", "title": "Authentication configuration", "form": true, "properties": {"enablePostgresUser": {"type": "boolean", "title": "Enable \"postgres\" admin user", "description": "Assign a password to the \"postgres\" admin user. Otherwise, remote access will be blocked for this user", "form": true}, "postgresPassword": {"type": "string", "title": "Password for the \"postgres\" admin user", "description": "Defaults to a random 10-character alphanumeric string if not set", "form": true}, "database": {"type": "string", "title": "PostgreSQL custom database", "description": "Name of the custom database to be created during the 1st initialization of PostgreSQL", "form": true}, "username": {"type": "string", "title": "PostgreSQL custom user", "description": "Name of the custom user to be created during the 1st initialization of PostgreSQL. This user only has permissions on the PostgreSQL custom database", "form": true}, "password": {"type": "string", "title": "Password for the custom user to create", "description": "Defaults to a random 10-character alphanumeric string if not set", "form": true}, "replicationUsername": {"type": "string", "title": "PostgreSQL replication user", "description": "Name of user used to manage replication.", "form": true, "hidden": {"value": "standalone", "path": "architecture"}}, "replicationPassword": {"type": "string", "title": "Password for PostgreSQL replication user", "description": "Defaults to a random 10-character alphanumeric string if not set", "form": true, "hidden": {"value": "standalone", "path": "architecture"}}}}, "persistence": {"type": "object", "properties": {"size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi"}}}, "resources": {"type": "object", "title": "Required Resources", "description": "Configure resource requests", "form": true, "properties": {"requests": {"type": "object", "properties": {"memory": {"type": "string", "form": true, "render": "slider", "title": "Memory Request", "sliderMin": 10, "sliderMax": 2048, "sliderUnit": "<PERSON>"}, "cpu": {"type": "string", "form": true, "render": "slider", "title": "CPU Request", "sliderMin": 10, "sliderMax": 2000, "sliderUnit": "m"}}}}}, "replication": {"type": "object", "form": true, "title": "Replication Details", "properties": {"enabled": {"type": "boolean", "title": "Enable Replication", "form": true}, "readReplicas": {"type": "integer", "title": "read Replicas", "form": true, "hidden": {"value": "standalone", "path": "architecture"}}}}, "volumePermissions": {"type": "object", "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable Init Containers", "description": "Change the owner of the persist volume mountpoint to RunAsUser:fsGroup"}}}, "metrics": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "Configure metrics exporter", "form": true}}}}}