{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.serviceBindings.enabled }}
{{- $host := include "common.names.fullname" . }}
{{- if not .Values.sentinel.enabled }}
{{- $host = printf "%s-master" (include "common.names.fullname" .) }}
{{- end }}
{{- $port := print .Values.master.service.ports.redis }}
{{- if .Values.sentinel.enabled }}
{{- $port = print .Values.sentinel.service.ports.redis }}
{{- end }}
{{- $password := include "redis.password" . }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/redis
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "redis" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  password: {{ print $password | b64enc | quote }}
  {{- if $password }}
  uri: {{ printf "redis://:%s@%s:%s" $password $host $port | b64enc | quote }}
  {{- else }}
  uri: {{ printf "redis://%s:%s" $host $port | b64enc | quote }}
  {{- end }}
{{- end }}
