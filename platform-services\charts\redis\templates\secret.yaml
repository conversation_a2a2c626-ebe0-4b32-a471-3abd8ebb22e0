{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.auth.enabled (not .Values.auth.existingSecret) (or .Values.auth.usePasswordFileFromSecret (not .Values.auth.usePasswordFiles)) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.secretAnnotations .Values.commonAnnotations }}
  annotations:
      {{- if .Values.secretAnnotations }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.secretAnnotations "context" $ ) | nindent 4 }}
      {{- end }}
      {{- if .Values.commonAnnotations }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
      {{- end }}
  {{- end }}
type: Opaque
data:
  redis-password: {{ include "redis.password" . | b64enc | quote }}
{{- end -}}
