{{- if .Values.otelCollector.ingress.enabled -}}
{{- $fullName := include "otelCollector.fullname" . -}}
{{- $ingressApiIsStable := eq (include "ingress.isStable" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "ingress.supportsPathType" .) "true" -}}
apiVersion: {{ include "ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "otelCollector.labels" . | nindent 4 }}
  {{- with .Values.otelCollector.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.otelCollector.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.otelCollector.ingress.className }}
  {{- end }}
  {{- if .Values.otelCollector.ingress.tls }}
  tls:
    {{- range .Values.otelCollector.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      {{- with .secretName }}
      secretName: {{ . }}
      {{- end }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.otelCollector.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ .port }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ .port }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
