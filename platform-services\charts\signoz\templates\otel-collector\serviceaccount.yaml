{{- if .Values.otelCollector.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "otelCollector.serviceAccountName" . }}
  labels:
    {{- include "otelCollector.labels" . | nindent 4 }}
  {{- with .Values.otelCollector.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- include "signoz.imagePullSecrets" . }}
{{- end -}}
