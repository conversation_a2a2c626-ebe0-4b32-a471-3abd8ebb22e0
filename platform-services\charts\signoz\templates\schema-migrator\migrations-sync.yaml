{{- if and .Values.schemaMigrator.enabled }}
{{- $schemaMigratorSuffix := "" }}
{{- if .Release.IsInstall }}
{{- $schemaMigratorSuffix = "-init" }}
{{- end }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "schemaMigrator.fullname" . }}-sync{{ $schemaMigratorSuffix }}
  labels:
    {{- include "schemaMigrator.selectorLabels" . | nindent 4 }}
  annotations:
    {{- with .Values.schemaMigrator.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- if .Values.schemaMigrator.upgradeHelmHooks }}
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
    {{- if .Release.IsUpgrade }}
    helm.sh/hook: pre-upgrade
    helm.sh/hook-delete-policy: before-hook-creation
    {{- end }}
    {{- end }}
spec:
  template:
    metadata:
      {{- with .Values.schemaMigrator.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "schemaMigrator.selectorLabels" . | nindent 8 }}
    spec:
      initContainers:
        {{- if .Values.schemaMigrator.initContainers.init.enabled }}
        - name: schema-migrator-sync-init
          image: {{ include "schemaMigrator.initContainers.init.image" . }}
          imagePullPolicy: {{ .Values.schemaMigrator.initContainers.init.image.pullPolicy }}
          env:
            {{- include "snippet.clickhouse-credentials" . | nindent 12 }}
          {{- with .Values.schemaMigrator.initContainers.init.command }}
          command:
            - sh
            - -c
            - until wget --user "$(CLICKHOUSE_USER):$(CLICKHOUSE_PASSWORD)" --spider -q {{ include "clickhouse.httpUrl" $ }}{{ .endpoint }}; do echo -e "{{ .waitMessage }}"; sleep {{ .delay }}; done; echo -e "{{ .doneMessage }}";
          {{- end }}
          resources:
            {{- toYaml .Values.schemaMigrator.initContainers.init.resources | nindent 12 }}
        {{- end }}
        # ClickHouse ready check
        {{- if and .Values.clickhouse.enabled .Values.schemaMigrator.initContainers.chReady.enabled }}
        - name: schema-migrator-sync-ch-ready
          image: {{ include "schemaMigrator.initContainers.chReady.image" . }}
          imagePullPolicy: {{ .Values.schemaMigrator.initContainers.chReady.image.pullPolicy }}
          env:
            {{- include "snippet.clickhouse-credentials" . | nindent 12 }}
            - name: CLICKHOUSE_VERSION
              value: {{ trimSuffix "-alpine" .Values.clickhouse.image.tag }}
            - name: CLICKHOUSE_SHARDS
              value: {{ default 1 .Values.clickhouse.layout.shardsCount | quote }}
            - name: CLICKHOUSE_REPLICAS
              value: {{ default 1 .Values.clickhouse.layout.replicasCount | quote }}
          {{- with .Values.schemaMigrator.initContainers.chReady.command }}
          command:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.schemaMigrator.initContainers.chReady.resources | nindent 12 }}
        {{- end }}
      containers:
        - name: schema-migrator
          image: {{ include "schemaMigrator.image" . }}
          imagePullPolicy: {{ .Values.schemaMigrator.image.pullPolicy }}
          env:
            {{- include "snippet.clickhouse-credentials" . | nindent 12 }}
          args:
            - sync
            - "--cluster-name"
            - "$(CLICKHOUSE_CLUSTER)"
            - "--dsn"
            - "tcp://$(CLICKHOUSE_USER):$(CLICKHOUSE_PASSWORD)@{{ include "schemamigrator.url" . }}"
            {{- if .Values.schemaMigrator.enableReplication }}
            - "--replication"
            {{- end }}
            {{- range .Values.schemaMigrator.args }}
            - {{ . | quote }}
            {{- end }}
      restartPolicy: OnFailure
      {{- if .Values.schemaMigrator.affinity }}
      affinity: {{ toYaml .Values.schemaMigrator.affinity | nindent 8 }}
      {{- end }}
      {{- if .Values.schemaMigrator.tolerations }}
      tolerations: {{ toYaml .Values.schemaMigrator.tolerations | nindent 8 }}
      {{- end }}
      {{- if .Values.schemaMigrator.nodeSelector }}
      nodeSelector: {{ toYaml .Values.schemaMigrator.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.schemaMigrator.topologySpreadConstraints }}
      topologySpreadConstraints: {{ toYaml .Values.schemaMigrator.topologySpreadConstraints | nindent 8 }}
      {{- end }}
{{- end }}
