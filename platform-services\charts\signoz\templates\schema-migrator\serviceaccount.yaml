{{- if .Values.schemaMigrator.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "schemaMigrator.serviceAccountName" . }}-async
  labels:
    {{- include "schemaMigrator.labels" . | nindent 4 }}
  {{- with .Values.schemaMigrator.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- include "signoz.imagePullSecrets" . }}
{{- end -}}
