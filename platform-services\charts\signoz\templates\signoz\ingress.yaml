{{- if .Values.signoz.ingress.enabled -}}
{{- $fullName := include "signoz.fullname" . -}}
{{- $ingressApiIsStable := eq (include "ingress.isStable" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "ingress.supportsPathType" .) "true" -}}
apiVersion: {{ include "ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "signoz.labels" . | nindent 4 }}
  {{- with .Values.signoz.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.signoz.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.signoz.ingress.className }}
  {{- end }}
  {{- if .Values.signoz.ingress.tls }}
  tls:
    {{- range .Values.signoz.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      {{- with .secretName }}
      secretName: {{ . }}
      {{- end }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.signoz.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ .port }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ .port }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
