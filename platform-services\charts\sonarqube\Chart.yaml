apiVersion: v2
name: sonarqube
description: SonarQube is a self-managed, automatic code review tool that systematically helps you deliver clean code. As a core element of our Sonar solution, SonarQube integrates into your existing workflow and detects issues in your code to help you perform continuous code inspections of your projects. The tool analyses 30+ different programming languages and integrates into your CI pipeline and DevOps platform to ensure that your code meets high-quality standards.
type: application
version: 2025.5.0
appVersion: 2025.4.0
keywords:
  - coverage
  - security
  - code
  - quality
home: https://www.sonarqube.org/
icon: https://raw.githubusercontent.com/SonarSource/sonarqube-static-resources/master/helm/SonarQubeServerLogo.png
sources:
  - https://github.com/SonarSource/helm-chart-sonarqube/tree/master/charts/sonarqube
  - https://github.com/SonarSource/docker-sonarqube
  - https://github.com/SonarSource/sonarqube
kubeVersion: ">= 1.24.0-0"
maintainers:
  - name: carminevas<PERSON>lo
    email: <EMAIL>
  - name: jCOTINEAU
    email: jeremy.co<PERSON><PERSON>@sonarsource.com
  - name: davividal
    email: davi.k<PERSON><PERSON><PERSON>-<EMAIL>
annotations:
  artifacthub.io/links: |
    - name: support
      url: https://community.sonarsource.com/
    - name: Chart Source
      url: https://github.com/SonarSource/helm-chart-sonarqube/tree/master/charts/sonarqube
  artifacthub.io/changes: |
    - kind: changed
      description: "Update Chart's version to 2025.5.0"
    - kind: changed
      description: "Upgrade SonarQube Community Build to 25.9.0.112764"
    - kind: changed
      description: "Update the image and readinessProbe used by postgresql after they migrated to a legacy repository"
    - kind: changed
      description: "Support Kubernetes v1.34.0"
  artifacthub.io/containsSecurityUpdates: "false"
  artifacthub.io/images: |
    - name: sonarqube-community
      image: sonarqube:25.9.0.112764-community
    - name: sonarqube-developer
      image: sonarqube:2025.4.0-developer
    - name: sonarqube-enterprise
      image: sonarqube:2025.4.0-enterprise
  charts.openshift.io/name: sonarqube
dependencies:
  - name: postgresql
    version: 10.15.0
    repository: https://raw.githubusercontent.com/bitnami/charts/archive-full-index/bitnami
    condition: postgresql.enabled
  - name: ingress-nginx
    version: 4.12.3
    repository: https://kubernetes.github.io/ingress-nginx
    condition: nginx.enabled,ingress-nginx.enabled
