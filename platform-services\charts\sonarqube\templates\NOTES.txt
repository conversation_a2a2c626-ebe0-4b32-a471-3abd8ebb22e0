1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range .Values.ingress.hosts }}
  http://{{ .name }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ template "sonarqube.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get svc -w {{ template "sonarqube.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ template "sonarqube.fullname" . }} -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  echo http://$SERVICE_IP:{{ .Values.service.externalPort }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app={{ template "sonarqube.name" . }},release={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl port-forward $POD_NAME 8080:{{ .Values.service.externalPort }} -n {{ .Release.Namespace }}
{{- end }}
{{- if eq .Values.edition "community" }}
DEPRECATION NOTICE: The SonarQube Community Edition has been replaced by the SonarQube Community Build.
                    Please use "community.enabled=true" to use the latest SonarQube Community Build. 
                    The "community" value is deprecated and won't be supported for "edition" anymore.
{{- end }}
WARNING: 
         Please note that the SonarQube image runs with a non-root user (uid=1000) belonging to the root group (guid=0). In this way, the chart can support arbitrary user ids as recommended in OpenShift.
         Please visit https://docs.openshift.com/container-platform/4.14/openshift_images/create-images.html#use-uid_create-images for more information.
{{ if .Values.postgresql.enabled }}
WARNING: The embedded PostgreSQL is intended for evaluation only, it is DEPRECATED, and it will be REMOVED in a future release.
         Please visit https://artifacthub.io/packages/helm/sonarqube/sonarqube#production-use-case for more information.
{{- end }}
{{ if or (.Values.nginx).enabled (index .Values "ingress-nginx" "enabled") }}
WARNING: The ingress nginx controller dependency has been upgraded to a major version (4.x), please carefully read the changelogs at https://github.com/kubernetes/ingress-nginx/releases and refer to the documentation at https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/annotations/.
         Please also visit https://artifacthub.io/packages/helm/sonarqube/sonarqube#production-use-case for more information.
{{- end }}
{{ if hasKey .Values.deploymentStrategy "type" }}
WARNING: Setting the deployment strategy type is deprecated and will be removed in a future release. It will be hard-coded to Recreate.
{{- end }}
{{ if eq .Values.deploymentType "StatefulSet" }}
WARNING: The deploymentType value is deprecated and won't be supported anymore. SonarQube will be deployed as a Deployment by default.
{{- end }}
