{{ $accountDeprecation := fromYaml (include "accountDeprecation" . ) }}
{{ $_ := set .Values "account" $accountDeprecation }}
{{- if .Values.account }}
{{- if or .Values.account.adminPassword .Values.account.newPassword .Values.account.adminPasswordSecretName .Values.account.passwordSecretName }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "sonarqube.fullname" . }}-change-admin-password-hook
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install, post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded
    {{- range $key, $value := .Values.adminJobAnnotations | default .Values.account.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  template:
    metadata:
      name: {{ template "sonarqube.fullname" . }}-change-admin-password-hook
      labels: {{- include "sonarqube.labels" . | nindent 8 }}
      annotations:
        # Disable Istio sidecar injection for this hook pod
        "sidecar.istio.io/inject": "false"
    spec:
      restartPolicy: OnFailure
      {{- if or .Values.image.pullSecrets .Values.image.pullSecret }}
      imagePullSecrets:
        {{- if .Values.image.pullSecret }}
        - name: {{ .Values.image.pullSecret }}
        {{- end }}
        {{- with .Values.image.pullSecrets }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      serviceAccountName: {{ template "sonarqube.serviceAccountName" . }}
      {{- with .Values.tolerations }}
      tolerations: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity: {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ template "sonarqube.fullname" . }}-change-default-admin-password
        image: {{ .Values.curlContainerImage | default ( .Values.account.image | default  (include "sonarqube.image" .) ) }}
         {{- with (default (fromYaml (include "sonarqube.initContainerSecurityContext" .)) .Values.account.securityContext) }}
        securityContext: {{- toYaml . | nindent 10 }}
        {{- end }}
        command: ["sh", "-c", 'until curl -sSi --connect-timeout 100 {{ template "sonarqube.fullname" . }}:{{ default 9000 .Values.service.internalPort }}{{ .Values.account.sonarWebContext | default (include "sonarqube.webcontext" .) }}api/system/status | grep -w UP; do sleep 10; done; curl -sSi --connect-timeout 100 -u admin:"$CURRENT_ADMIN_PASSWORD" -X POST "{{ template "sonarqube.fullname" . }}:{{ default 9000 .Values.service.internalPort }}{{ .Values.account.sonarWebContext | default (include "sonarqube.webcontext" .) }}api/users/change_password?login=admin" --data-urlencode "previousPassword=$CURRENT_ADMIN_PASSWORD" --data-urlencode "password=$ADMIN_PASSWORD"']
        env:
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              {{- if or .Values.account.adminPassword .Values.account.newPassword }}
              name: {{ template "sonarqube.fullname" . }}-admin-password
              {{- else }}
              name: {{ .Values.account.adminPasswordSecretName | default .Values.account.passwordSecretName }}
              {{- end }}
              key: password
        - name: CURRENT_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              {{- if or .Values.account.adminPassword .Values.account.newPassword }}
              name: {{ template "sonarqube.fullname" . }}-admin-password
              {{- else }}
              name: {{ .Values.account.adminPasswordSecretName | default .Values.account.passwordSecretName }}
              {{- end }}
              key: currentPassword
        {{- with (default .Values.resources .Values.account.resources) }}
        resources: {{- toYaml . | nindent 10 }}
        {{- end }}
{{- end }}
{{- end }}
