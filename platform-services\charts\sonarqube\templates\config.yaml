apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "sonarqube.fullname" . }}-config
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
data:
  sonar.properties: |
  {{- range $key, $val := .Values.sonarProperties }}
    {{ $key }}={{ $val }}
  {{- end }}
  {{- if not .Values.elasticsearch.bootstrapChecks }}
    sonar.es.bootstrap.checks.disable=true
  {{- end }}
  {{- if .Values.sonarSecretKey }}
    sonar.secretKeyPath={{ .Values.sonarqubeFolder }}/secret/sonar-secret.txt
  {{- end }}
