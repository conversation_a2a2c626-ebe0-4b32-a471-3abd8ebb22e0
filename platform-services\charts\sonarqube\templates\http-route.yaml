{{- if .Values.httproute.enabled -}}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ template "sonarqube.fullname" . }}-http-route
  labels:
    {{- include "sonarqube.labels" . | nindent 4 }}
    {{- with .Values.httproute.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  parentRefs:
  - name: {{ .Values.httproute.gateway }}
    {{- if .Values.httproute.gatewayNamespace }}
    namespace: {{ .Values.httproute.gatewayNamespace }}
    {{- end }}
  hostnames:
    {{- with .Values.httproute.hostnames }}
    {{ toYaml . }}
    {{- end }}
  rules:
  {{- if .Values.httproute.rules -}}
    {{- with .Values.httproute.rules -}}
    {{ toYaml . | nindent 4 }}
    {{- end }}
  {{- else }}
  - matches:
    - path:
        type: PathPrefix
        value: {{ include "sonarqube.webcontext" . }}
    backendRefs:
    - name: {{ include "sonarqube.fullname" . }}
      port: {{ .Values.service.externalPort }}
  {{- end }}
{{- end }}
