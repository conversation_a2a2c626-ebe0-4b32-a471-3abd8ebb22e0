{{- if .Values.ingress.enabled -}}
{{- $serviceName := include "sonarqube.fullname" . -}}
{{- $servicePort := .Values.service.externalPort -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "sonarqube.fullname" . }}
  labels:
    {{- include "sonarqube.labels" . | nindent 4 }}
    {{- with .Values.ingress.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .Values.ingress.annotations (.Values.nginx).enabled (index .Values "ingress-nginx" "enabled") }}
  annotations:
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- if and (or (.Values.nginx).enabled (index .Values "ingress-nginx" "enabled")) (not (hasKey (.Values.ingress.annotations) "nginx.ingress.kubernetes.io/proxy-body-size")) }}
    nginx.ingress.kubernetes.io/proxy-body-size: "64m"
    {{- end -}}
  {{- end }}
spec:
  {{- if .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- else if or (.Values.nginx).enabled (index .Values "ingress-nginx" "enabled") }}
  ingressClassName: "nginx"
  {{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
  - host: {{ printf "%s" .name }}
    http:
      paths:
      - backend:
          service:
            name: {{ default $serviceName .serviceName }}
            port:
              number: {{ default $servicePort .servicePort }}
        path: {{ .path | default (include "sonarqube.webcontext" $) }}
        pathType: {{ default "ImplementationSpecific" .pathType }}
  {{- end }}
  {{- with .Values.ingress.tls }}
  tls: {{- toYaml . | nindent 4 }}
  {{- end -}}
{{- end }}
