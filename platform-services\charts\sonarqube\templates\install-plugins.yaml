apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "sonarqube.fullname" . }}-install-plugins
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
data:
  install_plugins.sh: |-
    {{- if .Values.plugins.install }}
      rm -f {{ .Values.sonarqubeFolder }}/extensions/plugins/*
      cd {{ .Values.sonarqubeFolder }}/extensions/plugins
      {{- range $index, $val := .Values.plugins.install }}
      curl {{ if $.Values.plugins.noCheckCertificate }}--insecure{{ end }} {{ if $.Values.plugins.netrcCreds }}--netrc-file /root/.netrc{{ end }} -fsSLO {{ $val | quote }}
      {{- end }}
    {{- end }}
