{{- if .Values.networkPolicy.enabled }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "sonarqube.fullname" . }}-network-policy
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      app: {{ template "sonarqube.name" . }}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              app: {{ template "sonarqube.name" . }}
              release: {{ .Release.Name }}
      ports:
        - port: {{ .Values.service.internalPort }}
    {{ if .Values.prometheusExporter.enabled }}
    - from:
        - namespaceSelector:
            matchLabels:
              networking/namespace: {{ .Values.networkPolicy.prometheusNamespace }}
      ports:
        - port: {{ .Values.prometheusExporter.ceBeanPort }}
          protocol: TCP
        - port: {{ .Values.prometheusExporter.webBeanPort }}
          protocol: TCP
    {{ end }}
  egress:
    - to:
        - namespaceSelector:
            matchLabels:
              networking/namespace: kube-system
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - port: 53
          protocol: UDP
    {{- if .Values.postgresql.enabled }}
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: postgresql
      ports:
        - port: 5432
          protocol: TCP
    {{- end }}
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
{{- end -}}

{{ if and .Values.postgresql.enabled .Values.networkPolicy.enabled }}
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: {{ template "sonarqube.fullname" . }}-database
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: postgresql
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              app: {{ template "sonarqube.name" . }}
      ports:
        - port: 5432
  egress:
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - port: 53
          protocol: UDP
{{- end }}

{{- if and .Values.networkPolicy.enabled (or .Values.networkPolicy.additionalNetworkPolicies .Values.networkPolicy.additionalNetworkPolicys) }}
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: {{ template "sonarqube.fullname" . }}-additional-network-policy
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
spec:
{{- if.Values.networkPolicy.additionalNetworkPolicys -}}
{{- with .Values.networkPolicy.additionalNetworkPolicys -}}
{{ toYaml . | nindent 2 }}
{{- end -}}
{{- else -}}
{{- with .Values.networkPolicy.additionalNetworkPolicies -}}
{{ toYaml . | nindent 2 }}
{{- end -}}
{{- end -}}
{{- end -}}
