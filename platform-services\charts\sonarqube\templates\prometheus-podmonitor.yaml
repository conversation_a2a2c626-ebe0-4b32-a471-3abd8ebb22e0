{{- if .Values.prometheusMonitoring.podMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: {{ template "sonarqube.name" . }}
  {{- if .Values.prometheusMonitoring.podMonitor.namespace }}
  namespace: {{ .Values.prometheusMonitoring.podMonitor.namespace | quote }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    app: {{ template "sonarqube.name" . }}
    {{- with .Values.prometheusMonitoring.podMonitor.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
   {{- if .Values.prometheusMonitoring.podMonitor.jobLabel }}
  jobLabel: {{ .Values.prometheusMonitoring.podMonitor.jobLabel | quote }}
  {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  selector:
    matchLabels:
      app: {{ template "sonarqube.name" . }}
  podMetricsEndpoints:
  - port: http
    path: {{ include "sonarqube.webcontext" . }}api/monitoring/metrics
    scheme: http
    {{- if .Values.prometheusMonitoring.podMonitor.interval }}
    interval: {{ .Values.prometheusMonitoring.podMonitor.interval }}
    {{- end }}
    {{- if .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    {{- end }}
    bearerTokenSecret:
    {{- if and .Values.monitoringPasscodeSecretName .Values.monitoringPasscodeSecretKey }}
      name: {{ .Values.monitoringPasscodeSecretName }}
      key: {{ .Values.monitoringPasscodeSecretKey }}
    {{- else }}
      name: {{ template "sonarqube.fullname" . }}-monitoring-passcode
      key: SONAR_WEB_SYSTEMPASSCODE
    {{- end }}
  {{- if .Values.prometheusExporter.enabled }}
  {{- if .Values.prometheusExporter.ceBeanPort }}
  - port: monitoring-ce
    path: /
    scheme: http
    {{- if .Values.prometheusMonitoring.podMonitor.interval }}
    interval: {{ .Values.prometheusMonitoring.podMonitor.interval }}
    {{- end }}
    {{- if .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    {{- end }}
  {{- end }}
  {{- if .Values.prometheusExporter.webBeanPort }}
  - port: monitoring-web
    path: /
    scheme: http
    {{- if .Values.prometheusMonitoring.podMonitor.interval }}
    interval: {{ .Values.prometheusMonitoring.podMonitor.interval }}
    {{- end }}
    {{- if .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.prometheusMonitoring.podMonitor.scrapeTimeout }}
    {{- end }}
  {{- end }}
  {{- end }}
{{- end }}
