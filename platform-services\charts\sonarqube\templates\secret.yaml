{{ $accountDeprecation := fromYaml (include "accountDeprecation" . ) }}
{{ $_ := set .Values "account" $accountDeprecation }}
---
{{- if not (or .Values.postgresql.enabled .Values.postgresql.existingSecret .Values.jdbcOverwrite.jdbcSecretName) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "sonarqube.fullname" . }}
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
type: Opaque
data:
  {{ template "jdbc.secretPasswordKey" . }}: {{ template "jdbc.internalSecretPasswd" . }}
{{- end }}
---
{{- if and .Values.monitoringPasscode (not .Values.monitoringPasscodeSecretName) (not .Values.monitoringPasscodeSecretKey) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "sonarqube.fullname" . }}-monitoring-passcode
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
type: Opaque
data:
  SONAR_WEB_SYSTEMPASSCODE: {{ .Values.monitoringPasscode | b64enc | quote }}
{{- end }}
---
{{- if .Values.account }}
{{- if or .Values.account.adminPassword .Values.account.newPassword }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "sonarqube.fullname" . }}-admin-password
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
type: Opaque
stringData:
  password: {{ .Values.account.adminPassword | default .Values.account.newPassword | quote }}
  currentPassword: {{ .Values.account.currentAdminPassword | default .Values.account.currentPassword | quote }}
{{- end }}
{{- end }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "sonarqube.fullname" . }}-http-proxies
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
type: Opaque
stringData:
  PLUGINS-HTTP-PROXY: {{ default .Values.httpProxy .Values.plugins.httpProxy | quote }}
  PLUGINS-HTTPS-PROXY: {{ default .Values.httpsProxy .Values.plugins.httpsProxy | quote }}
  PLUGINS-NO-PROXY: {{ default .Values.noProxy .Values.plugins.noProxy | quote }}
  PROMETHEUS-EXPORTER-HTTP-PROXY: {{ default .Values.httpProxy .Values.prometheusExporter.httpProxy | quote }}
  PROMETHEUS-EXPORTER-HTTPS-PROXY: {{ default .Values.httpsProxy .Values.prometheusExporter.httpsProxy | quote }}
  PROMETHEUS-EXPORTER-NO-PROXY: {{ default .Values.noProxy .Values.prometheusExporter.noProxy | quote }}