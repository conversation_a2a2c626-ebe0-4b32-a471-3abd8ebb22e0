apiVersion: v1
kind: Service
metadata:
  name: {{ template "sonarqube.fullname" . }}
  labels:
    {{- include "sonarqube.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.service.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- if .Values.service.annotations }}
  annotations:
    {{- range $key, $value := .Values.service.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.externalPort }}
      targetPort: http
      protocol: TCP
      name: http
      {{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    app: {{ template "sonarqube.name" . }}
    release: {{ .Release.Name }}
  {{- if eq .Values.service.type "LoadBalancer" }}
  {{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range .Values.service.loadBalancerSourceRanges }}
  - {{ . }}
  {{- end }}
  {{- end -}}
  {{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- end }}
