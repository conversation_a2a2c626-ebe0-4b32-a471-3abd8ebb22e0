{{- if eq .Values.deploymentType "StatefulSet" -}}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "sonarqube.fullname" . }}
  labels: {{- include "sonarqube.workloadLabels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  serviceName: {{ template "sonarqube.fullname" . }}
  selector:
    matchLabels: {{- include "sonarqube.selectorLabels" . | nindent 6 }}
  template: {{- include "sonarqube.pod" . | nindent 4 }}
{{- end }}
