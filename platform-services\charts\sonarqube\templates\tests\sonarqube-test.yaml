{{- if .Values.tests.enabled -}}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ .Release.Name }}-ui-test"
  annotations:
    "helm.sh/hook": test-success
    # Disable Istio sidecar injection for this test pod
    "sidecar.istio.io/inject": "false"
  labels: {{- include "sonarqube.labels" . | nindent 4 }}
spec:
  automountServiceAccountToken: false
  {{- if or .Values.image.pullSecrets .Values.image.pullSecret }}
  imagePullSecrets:
    {{- if .Values.image.pullSecret }}
    - name: {{ .Values.image.pullSecret }}
    {{- end }}
    {{- with .Values.image.pullSecrets }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  containers:
    - name: {{ .Release.Name }}-ui-test
      image: {{ .Values.tests.image | default (include "sonarqube.image" .) | quote }}
      imagePullPolicy: {{ .Values.image.pullPolicy }}
      command: ['wget']
      args: [
        '--retry-connrefused',
        '--waitretry=1',
        '--timeout=5',
        '-t',
        '12',
        '-qO-',
        '{{ template "sonarqube.fullname" . }}:{{ .Values.service.internalPort }}/api/system/status'
        ]
      {{- with .Values.tests.resources }}
      resources: {{- toYaml . | nindent 8 }}
      {{- end }}
  restartPolicy: Never
{{- end -}}
