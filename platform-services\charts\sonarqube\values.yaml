# Default values for SonarQube.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# (DEPRECATED) If the deployment Type is set to Deployment SonarQube is deployed as a replica set.
# StatefulSet will be removed in a future release in favor of Deployment. Default to StatefulSet for backwards compatibility.
deploymentType: "StatefulSet"

# There should not be more than 1 SonarQube instance connected to the same database. Please set this value to 1 or 0 (in case you need to scale down programmatically).
replicaCount: 1

# How many revisions to retain (Deployment ReplicaSets or StatefulSets)
revisionHistoryLimit: 10

# (DEPRECATED) This will use the default deployment strategy unless it is overridden
deploymentStrategy:
  type: Recreate
# Uncomment this to scheduler pods on priority
# priorityClassName: "high-priority"

## Use an alternate scheduler, e.g. "stork".
## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
# schedulerName:

## OpenShift specific configuration
OpenShift:
  enabled: false
  # (Deprecated) this parameter should not be needed anymore, we support Openshift SCCv2 by default when Openshift.enabled=true
  createSCC: false
  route:
    enabled: false
    host: "sonarqube.your-org.com"
    path: "/"
    # Add tls section to secure traffic.
    tls:
      termination: edge
      # certificate:
      # key:
      # caCertificate:
      # insecureEdgeTerminationPolicy: Redirect
    wildcardPolicy: None
    annotations: {}
    # See Openshift/OKD route annotation
    # https://docs.openshift.com/container-platform/4.10/networking/routes/route-configuration.html#nw-route-specific-annotations_route-configuration
    # haproxy.router.openshift.io/timeout: 1m
    # Additional labels for Route manifest file
    # labels:
    #  external: 'true'

# Configure the edition of SonarQube Server to deploy: developer or enterprise
# edition: ""

# Set the chart to use the latest released SonarQube Community Build
community:
  enabled: true
  buildNumber: "25.9.0.112764"

image:
  repository: sonarqube
  tag: "25.9.0.112764-community"
  pullPolicy: IfNotPresent
  # If using a private repository, the imagePullSecrets to use
  # pullSecrets:
  #   - name: my-repo-secret

# Set security context for sonarqube pod.
# The current section contains the default values set in a generic Kubernetes cluster. If you are using OpenShift, you should not set any specific fsGroup.
securityContext:
  fsGroup: 0

# Set security context for sonarqube container.
# The current section contains the default values set in a generic Kubernetes cluster. If you are using OpenShift, you should not set any specific UID or GID to be used for the execution.
containerSecurityContext:
# Sonarqube dockerfile creates sonarqube user as UID and GID 0
# Those default are used to match pod security standard restricted as least privileged approach
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 0
  seccompProfile:
    type: RuntimeDefault
  capabilities:
    drop: ["ALL"]
#   readOnlyRootFilesystem: true

# Settings to configure elasticsearch host requirements
elasticsearch:
  # (DEPRECATED) Use initSysctl.enabled instead
  configureNode: false
  bootstrapChecks: true

service:
  type: ClusterIP
  externalPort: 9000
  internalPort: 9000
  labels:
  annotations: {}
  # May be used in example for internal load balancing in GCP:
  # cloud.google.com/load-balancer-type: Internal
  # loadBalancerSourceRanges:
  #   - 0.0.0.0/0
  # loadBalancerIP: *******

# Those proxy settings will be propagated to the install-plugin and prometheus-exporter init containers
# if httpProxySecret is set the other one will be ignored
# the secret should contain exactly the keys http_proxy, https_proxy and no_proxy
httpProxySecret: ""
httpProxy: ""
httpsProxy: ""
noProxy: ""

# Optionally create Network Policies
networkPolicy:
  enabled: false

  # If you plan on using the jmx exporter, you need to define where the traffic is coming from
  prometheusNamespace: "monitoring"

  # If you are using a external database and enable network Policies to be created
  # you will need to explicitly allow egress traffic to your database
  # (DEPRECATED) please use additionalNetworkPolicies instead
  # additionalNetworkPolicys:
  # expects https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.21/#networkpolicyspec-v1-networking-k8s-io
  # additionalNetworkPolicies:

# will be used as default for ingress path and probes path, will be injected in .Values.env as SONAR_WEB_CONTEXT
# if .Values.env.SONAR_WEB_CONTEXT is set, this value will be ignored
sonarWebContext: ""

# (DEPRECATED) please use ingress-nginx instead
# nginx:
#   enabled: false

# Install the nginx ingress helm chart
ingress-nginx:
  enabled: false

  # You can add here any values from the official nginx ingress chart
  # controller:
  #   replicaCount: 3

httproute:
  enabled: false
  # gateway: my-gateway
  # gatewayNamespace: my-gateway-namespace # optional
  # labels:
  #   somelabel: somevalue
  # hostnames:
  #   - sonarqube.your-org.com
  # The rules are optional, by default we will create one with the SonarWebContext prefix and the SonarQube service values
  # rules:
  # - matches:
  #   - path:
  #       type: PathPrefix
  #       value: /bar
  #   backendRefs:
  #   - name: my-service1
  #     port: 8080

ingress:
  enabled: false
  # Used to create an Ingress record.
  hosts:
    - name: sonarqube.your-org.com
      # Different clouds or configurations might need /* as the default path
      # path: /
      # For additional control over serviceName and servicePort
      # serviceName: someService
      # servicePort: somePort
      # the pathType can be one of the following values: Exact|Prefix|ImplementationSpecific(default)
      # pathType: ImplementationSpecific
  annotations: {}
  # kubernetes.io/tls-acme: "true"

  # Set the ingressClassName on the ingress record
  # ingressClassName: nginx

# Additional labels for Ingress manifest file
  # labels:
  #  traffic-type: external
  #  traffic-type: internal
  tls: []
  # Secrets must be manually created in the namespace. To generate a self-signed certificate (and private key) and then create the secret in the cluster please refer to official documentation available at https://kubernetes.github.io/ingress-nginx/user-guide/tls/#tls-secrets
  # - secretName: chart-example-tls
  #   hosts:
  #     - chart-example.local

# Affinity for pod assignment
# Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
affinity: {}

# Tolerations for pod assignment
# Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
# taint a node with the following command to mark it as not schedulable for new pods
# kubectl taint nodes <node> sonarqube=true:NoSchedule
# The following statement will tolerate this taint and as such reverse a node for sonarqube
tolerations: []
#  - key: "sonarqube"
#    operator: "Equal"
#    value: "true"
#    effect: "NoSchedule"

# Node labels for pod assignment
# Ref: https://kubernetes.io/docs/user-guide/node-selection/
# add a label to a node with the following command
# kubectl label node <node> sonarqube=true
nodeSelector: {}
#  sonarqube: "true"

# hostAliases allows the modification of the hosts file inside a container
hostAliases: []
# - ip: "************"
#   hostnames:
#   - "example.com"
#   - "www.example.com"

readinessProbe:
  exec:
    command:
    - sh
    - -c
    - |
      #!/bin/bash
      # A Sonarqube container is considered ready if the status is UP, DB_MIGRATION_NEEDED or DB_MIGRATION_RUNNING
      # status about migration are added to prevent the node to be kill while SonarQube is upgrading the database.
      if wget --no-proxy -qO- http://localhost:{{ .Values.service.internalPort }}{{ .Values.readinessProbe.sonarWebContext | default (include "sonarqube.webcontext" .) }}api/system/status | grep -q -e '"status":"UP"' -e '"status":"DB_MIGRATION_NEEDED"' -e '"status":"DB_MIGRATION_RUNNING"'; then
        exit 0
      fi
      exit 1
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 6
  # Note that timeoutSeconds was not respected before Kubernetes 1.20 for exec probes
  timeoutSeconds: 1
  # If an ingress *path* other than the root (/) is defined, it should be reflected here
  # A trailing "/" must be included
  # deprecated please use sonarWebContext at the value top level
  # sonarWebContext: /

livenessProbe:
  exec:
    command:
    - sh
    - -c
    - |
      wget --no-proxy --quiet -O /dev/null --timeout={{ .Values.livenessProbe.timeoutSeconds }} --header="X-Sonar-Passcode: $SONAR_WEB_SYSTEMPASSCODE" "http://localhost:{{ .Values.service.internalPort }}{{ .Values.livenessProbe.sonarWebContext | default (include "sonarqube.webcontext" .) }}api/system/liveness"
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 6
  # Note that timeoutSeconds was not respected before Kubernetes 1.20 for exec probes
  timeoutSeconds: 1
  # If an ingress *path* other than the root (/) is defined, it should be reflected here
  # A trailing "/" must be included
  # deprecated please use sonarWebContext at the value top level
  # sonarWebContext: /

startupProbe:
  initialDelaySeconds: 30
  periodSeconds: 10
  failureThreshold: 24
  # Note that timeoutSeconds was not respected before Kubernetes 1.20 for exec probes
  timeoutSeconds: 1
  # If an ingress *path* other than the root (/) is defined, it should be reflected here
  # A trailing "/" must be included
  # deprecated please use sonarWebContext at the value top level
  # sonarWebContext: /

initContainers:
  # all initContainers use SonarQube image by default, but you can override it by setting the image field (ex image: ubuntu:24.04)
  # image:
  # Set the security context for the init containers
  # The current section contains the default values set in a generic Kubernetes cluster. If you are using OpenShift, you should not set any specific UID or GID to be used for the execution.
  # We allow the init containers to have a separate security context declaration because
  # the initContainer may not require the same as SonarQube.
  # Those default are used to match pod security standard restricted as least privileged approach
  securityContext:
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 0
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
    readOnlyRootFilesystem: true
  # We allow the init containers to have a separate resources declaration because
  # the initContainer does not take as much resources.
  resources: {}

# Extra init containers to e.g. download required artifacts
extraInitContainers: {}

## Array of extra containers to run alongside the SonarQube container
##
## Example:
## - name: myapp-container
##   image: busybox
##   command: ['sh', '-c', 'echo Hello && sleep 3600']
##
extraContainers: []
extraVolumes: []
extraVolumeMounts: []

## Provide a secret containing one or more certificate files in the keys that will be added to cacerts
## The cacerts file will be set via SONARQUBE_WEB_JVM_OPTS and SONAR_CE_JAVAOPTS
##
caCerts:
  enabled: false
  # image:
  # secret: your-secret-name

  # Optionally, you can store your certificate in a ConfigMap and use it as:
  # configMap:
  #   name: my-custom-cacerts-certificate
  #   key: key
  #   path: my-certificate.crt

initSysctl:
  enabled: true
  vmMaxMapCount: 524288
  fsFileMax: 131072
  nofile: 131072
  nproc: 8192
  # all initContainers use SonarQube image by default, but you can override it by setting the image field (ex image: ubuntu:24.04)
  # image:
  securityContext:
    # Compatible with podSecurity standard privileged
    privileged: true
    # if run without root permissions, error "sysctl: permission denied on key xxx, ignoring"
    runAsUser: 0
    readOnlyRootFilesystem: true
  # resources: {}

# This should not be required anymore, used to chown/chmod folder created by faulty CSI driver that are not applying properly POSIX fsgroup.
initFs:
  enabled: true
  # all initContainers use SonarQube image by default, but you can override it by setting the image field (ex image: ubuntu:24.04)
  # image:
  # Compatible with podSecurity standard baseline.
  securityContext:
    privileged: false
    runAsNonRoot: false
    runAsUser: 0
    runAsGroup: 0
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
      add: ["CHOWN"]
    readOnlyRootFilesystem: true

prometheusExporter:
  enabled: false
  # jmx_prometheus_javaagent version to download from Maven Central
  version: "0.17.2"
  # Alternative full download URL for the jmx_prometheus_javaagent.jar (overrides prometheusExporter.version)
  # downloadURL: ""
  # if you need to ignore TLS certificates for whatever reason enable the following flag
  noCheckCertificate: false

  # Ports for the jmx prometheus agent to export metrics at
  webBeanPort: 8000
  ceBeanPort: 8001

  config:
    rules:
      - pattern: ".*"
  # Overrides config for the CE process Prometheus exporter (by default, the same rules are used for both the Web and CE processes).
  # ceConfig:
  #   rules:
  #     - pattern: ".*"
  # image: curlimages/curl:8.2.1
  # For use behind a corporate proxy when downloading prometheus
  # httpProxy: ""
  # httpsProxy: ""
  # noProxy: ""
  # Reuse default initcontainers.securityContext that match restricted pod security standard
  # securityContext: {}

prometheusMonitoring:
  # Generate a Prometheus Pod Monitor (https://github.com/coreos/prometheus-operator)
  #
  podMonitor:
    # Create PodMonitor Resource for Prometheus scraping
    enabled: false
    # (DEPRECATED) Specify a custom namespace where the PodMonitor will be created.
    # This value should not be set, as the PodMonitor's namespace has to match the Release Namespace.
    # namespace: "default"
    # Specify the interval how often metrics should be scraped
    interval: 30s
    # Specify the timeout after a scrape is ended
    # scrapeTimeout: ""
    # Name of the label on target services that prometheus uses as job name
    # jobLabel: ""
    # Additional labels to add to the PodMonitor
    # labels: {}

# List of plugins to install.
# For example:
# plugins:
#  install:
#    - "https://github.com/AmadeusITGroup/sonar-stash/releases/download/1.3.0/sonar-stash-plugin-1.3.0.jar"
#    - "https://github.com/SonarSource/sonar-ldap/releases/download/2.2-RC3/sonar-ldap-plugin-2.2.0.601.jar"
#
plugins:
  install:
    - "https://github.com/mc1arke/sonarqube-community-branch-plugin/releases/download/25.9.0/sonarqube-community-branch-plugin-25.9.0.jar"

  # For use behind a corporate proxy when downloading plugins
  # httpProxy: ""
  # httpsProxy: ""
  # noProxy: ""

  # image: curlimages/curl:8.2.1
  # resources: {}

  # .netrc secret file with a key "netrc" to use basic auth while downloading plugins
  # netrcCreds: ""

  # Set to true to not validate the server's certificate to download plugin
  noCheckCertificate: false
  # Reuse default initcontainers.securityContext that match restricted pod security standard
  # securityContext: {}

## (DEPRECATED) The following value sets SONAR_WEB_JAVAOPTS (e.g., jvmOpts: "-Djava.net.preferIPv4Stack=true"). However, this is deprecated, please set SONAR_WEB_JAVAOPTS or sonar.web.javaOpts directly instead.
jvmOpts: ""

## (DEPRECATED) The following value sets SONAR_CE_JAVAOPTS. However, this is deprecated, please set SONAR_CE_JAVAOPTS or sonar.ce.javaOpts directly instead.
jvmCeOpts: ""

## a monitoring passcode needs to be defined in order to get reasonable probe results
# not setting the monitoring passcode will result in a deployment that will never be ready
# monitoringPasscode: "define_it"
# Alternatively, you can define the passcode loading it from an existing secret specifying the right key
# monitoringPasscodeSecretName: "pass-secret-name"
# monitoringPasscodeSecretKey: "pass-key"

## Environment variables to attach to the pods
##
env:
  # Java agent configuration for Community Branch Plugin
  - name: SONAR_WEB_JAVAADDITIONALOPTS
    value: "-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-25.9.0.jar=web"
  - name: SONAR_CE_JAVAADDITIONALOPTS
    value: "-javaagent:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-25.9.0.jar=ce"
#   # If you use a different ingress path from /, you have to add it here as the value of SONAR_WEB_CONTEXT
#   - name: SONAR_WEB_CONTEXT
#     value: /sonarqube
#   - name: VARIABLE
#     value: my-value

# Set annotations for pods
annotations: {}

## We usually don't make specific resource recommendations, as they are heavily dependant on
## the usage of SonarQube and the surrounding infrastructure.
## Those default are based on the default Web -Xmx1G -Xms128m and CE -Xmx2G -Xms128m and Search -Xmx2G -Xms2G settings of SQ sub processes
## Adjust these values to your needs, you can find more details on the main README of the chart.
resources:
  limits:
    cpu: 800m
    memory: 6144M
    ephemeral-storage: 512000M
  requests:
    cpu: 400m
    memory: 2048M
    ephemeral-storage: 1536M

persistence:
  enabled: false
  ## Set annotations on pvc
  annotations: {}

  ## Specify an existing volume claim instead of creating a new one.
  ## When using this option all following options like storageClass, accessMode and size are ignored.
  # existingClaim:

  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass:
  accessMode: ReadWriteOnce
  size: 5Gi
  uid: 1000
  guid: 0

  ## DEPRECATED please use root level extraVolumes value
  ## Specify extra volumes. Refer to ".spec.volumes" specification : https://kubernetes.io/fr/docs/concepts/storage/volumes/
  volumes: []
  ## DEPRECATED please use root level extraVolumeMounts value
  ## Specify extra mounts. Refer to ".spec.containers.volumeMounts" specification : https://kubernetes.io/fr/docs/concepts/storage/volumes/
  mounts: []

  # In case you want to specify hostPath for to storage into the host.
  # hostPath:
  #   # Path where the data will be stored.
  #   path: /data/sonarqube/
  #   # Type of volume. Must be one of: DirectoryOrCreate, Directory, FileOrCreate, File, Socket, CharDevice, BlockDevice.
  #   type: DirectoryOrCreate

# In case you want to specify different resources for emptyDir than {}
emptyDir: {}
  # Example of resouces that might be used:
  # medium: Memory
  # sizeLimit: 16Mi

# A custom sonar.properties file can be provided via dictionary.
# For example:
# sonarProperties:
#   sonar.log.level: DEBUG
#   sonar.security.realm: LDAP
#   ldap.url: ldaps://organization.com

# Additional sonar properties to load from a secret with a key "secret.properties" (must be a string)
# sonarSecretProperties:

# Kubernetes secret that contains the encryption key for the SonarQube instance.
# The secret must contain the key 'sonar-secret.txt'.
# The 'sonar.secretKeyPath' property will be set automatically.
# sonarSecretKey: "settings-encryption-secret"

## Override JDBC values
## for external Databases
jdbcOverwrite:
  # (DEPRECATED) Please use jdbcOverwrite.enabled instead
  # enable: false
  # If enable the JDBC Overwrite, make sure to set `postgresql.enabled=false`
  enabled: false
  # The JDBC url of the external DB
  jdbcUrl: "****************************************"
  # The DB user that should be used for the JDBC connection
  jdbcUsername: "sonarUser"
  # Use this if you don't mind the DB password getting stored in plain text within the values file
  # (DEPRECATED) Please use `jdbcOverwrite.jdbcSecretName` along with `jdbcOverwrite.jdbcSecretPasswordKey` instead
  jdbcPassword: "sonarPass"
  ## Alternatively, use a pre-existing k8s secret containing the DB password
  # jdbcSecretName: "sonarqube-jdbc"
  ## and the secretValueKey of the password found within that secret
  # jdbcSecretPasswordKey: "jdbc-password"
  # To install the oracle JDBC driver, set the following URL (in this example, we set the URL for the Oracle 11 driver. Please update it to your target driver URL.).
  # If downloading the driver requires authentication, please set the .netrc secret file with a key "netrc" to use basic auth.
  # oracleJdbcDriver:
  #   url: "https://download.oracle.com/otn-pub/otn_software/jdbc/2113/ojdbc11.jar"
  #   netrcCreds: ""

## (DEPRECATED) Configuration values for postgresql dependency
## ref: https://github.com/bitnami/charts/blob/master/bitnami/postgresql/README.md
postgresql:
  # Enable to deploy the bitnami PostgreSQL chart
  enabled: true
  ## postgresql Chart global settings
  # global:
  #   imageRegistry: ''
  #   imagePullSecrets: ''
  ## bitnami/postgres image tag
  image:
    repository: "bitnamilegacy/postgresql"
    tag: 11.14.0
  # Set the readiness probe to avoid that the initialization scripts' are not completed before marking the pod as ready.
  readinessProbe:
    enabled: false
  customReadinessProbe:
    exec:
      command:
        - /bin/sh
        - -c
        - -e
        - |
          {{- if (include "postgresql.database" .) }}
          exec pg_isready -U {{ include "postgresql.username" . | quote }} -d "dbname={{ include "postgresql.database" . }} {{- if .Values.tls.enabled }} sslcert={{ include "postgresql.tlsCert" . }} sslkey={{ include "postgresql.tlsCertKey" . }}{{- end }}" -h 127.0.0.1 -p {{ .Values.containerPorts.postgresql }}
          {{- else }}
          exec pg_isready -U {{ include "postgresql.username" . | quote }} {{- if .Values.tls.enabled }} -d "sslcert={{ include "postgresql.tlsCert" . }} sslkey={{ include "postgresql.tlsCertKey" . }}"{{- end }} -h 127.0.0.1 -p {{ .Values.containerPorts.postgresql }}
          {{- end }}
          {{- if contains "bitnamilegacy/" .Values.image.repository }}
          [ -f /opt/bitnami/postgresql/tmp/.initialized ] || [ -f /bitnami/postgresql/.initialized ]
          {{- end }}
    failureThreshold: 6
    initialDelaySeconds: 5
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 5
  # existingSecret Name of existing secret to use for PostgreSQL passwords
  # The secret has to contain the keys postgresql-password which is the password for postgresqlUsername when it is
  # different of postgres, postgresql-postgres-password which will override postgresqlPassword,
  # postgresql-replication-password which will override replication.password and postgresql-ldap-password which will be
  # used to authenticate on LDAP. The value is evaluated as a template.
  # existingSecret: ""
  #
  # The bitnami chart enforces the key to be "postgresql-password". This value is only here for historic purposes
  # existingSecretPasswordKey: "postgresql-password"
  postgresqlUsername: "sonarUser"
  postgresqlPassword: "sonarPass"
  postgresqlDatabase: "sonarDB"
  # Specify the TCP port that PostgreSQL should use
  service:
    port: 5432
  resources:
    limits:
      cpu: 2
      memory: 2Gi
    requests:
      cpu: 100m
      memory: 200Mi
  persistence:
    enabled: true
    accessMode: ReadWriteOnce
    size: 20Gi
    storageClass:
  securityContext:
    enabled: true
    # fsGroup specification below are not applied if enabled=false. enabled=false is the required setting for OpenShift "restricted SCC" to work successfully.
    # postgresql dockerfile sets user as 1001
    fsGroup: 1001
  containerSecurityContext:
    enabled: true
    # runAsUser specification below are not applied if enabled=false. enabled=false is the required setting for OpenShift "restricted SCC" to work successfully.
    # postgresql dockerfile sets user as 1001, the rest aim at making it compatible with restricted pod security standard.
    runAsUser: 1001
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  volumePermissions:
    enabled: false
    # if using restricted SCC set runAsUser: "auto" and if running under anyuid/nonroot SCC - runAsUser needs to match runAsUser above
    securityContext:
      runAsUser: 0
  shmVolume:
    chmod:
      enabled: false
  serviceAccount:
    ## If enabled = true, and name is not set, postgreSQL will create a serviceAccount
    enabled: false
    # name:

# Additional labels to add to the pods:
# podLabels:
#   key: value
podLabels: {}
# (DEPRECATED) this field will be removed, as it needs to match the SonarQube image folder structure, considering we have one chart version per docker image version this field is not needed anymore.
sonarqubeFolder: /opt/sonarqube

tests:
  image: ""
  enabled: true
  resources:
    requests:
      cpu: 500m
      memory: 200M
      ephemeral-storage: 100M
    limits:
      cpu: 500m
      memory: 200M
      ephemeral-storage: 1000M

# For OpenShift set create=true to ensure service account is created.
serviceAccount:
  create: false
  # name:
  automountToken: false
  ## Annotations for the Service Account
  annotations: {}

# extraConfig is used to load Environment Variables from Secrets and ConfigMaps
# which may have been written by other tools, such as external orchestrators.
#
# These Secrets/ConfigMaps are expected to contain Key/Value pairs, such as:
#
# apiVersion: v1
# kind: ConfigMap
# metadata:
#   name: external-sonarqube-opts
# data:
#   SONARQUBE_JDBC_USERNAME: foo
#   SONARQUBE_JDBC_URL: *******************************************
#
# These vars can then be injected into the environment by uncommenting the following:
#
# extraConfig:
#   configmaps:
#     - external-sonarqube-opts

extraConfig:
  secrets: []
  configmaps: []

# setAdminPassword:
# The values can be set to define the current and the (new) custom admin passwords at the startup (the username will remain "admin")
#  newPassword: AdminAdmin_12$
#  currentPassword: admin
# The above values can be also provided by a secret that contains "password" and "currentPassword" as keys. You can generate such a secret in your cluster
# using "kubectl create secret generic admin-password-secret-name --from-literal=password=admin --from-literal=currentPassword=admin"
#  passwordSecretName: ""
# Reuse default initcontainers.securityContext that match restricted pod security standard
#  securityContext: {}
#  resources:
#    limits:
#      cpu: 100m
#      memory: 128Mi
#    requests:
#      cpu: 100m
#      memory: 128Mi
#  image:
#  annotations: {}

# (DEPRECATED) please use setAdminPassword instead
# account:
# The values can be set to define the current and the (new) custom admin passwords at the startup (the username will remain "admin")
#   adminPassword: AdminAdmin_12$
#   currentAdminPassword: admin
# The above values can be also provided by a secret that contains "password" and "currentPassword" as keys. You can generate such a secret in your cluster
# using "kubectl create secret generic admin-password-secret-name --from-literal=password=admin --from-literal=currentPassword=admin"
#   adminPasswordSecretName: ""
# Reuse default initcontainers.securityContext that match restricted pod security standard
#   securityContext: {}
#   resources:
#     limits:
#       cpu: 100m
#       memory: 128Mi
#     requests:
#       cpu: 100m
#       memory: 128Mi
# (DEPRECATED) please use setAdminPassword.image instead
# curlContainerImage: curlimages/curl:8.2.1
# (DEPRECATED) please use setAdminPassword.annotations instead
# adminJobAnnotations: {}
# deprecated please use sonarWebContext at the value top level
#   sonarWebContext: /

# (DEPRECATED) This value is not used in the templates.
terminationGracePeriodSeconds: 60
