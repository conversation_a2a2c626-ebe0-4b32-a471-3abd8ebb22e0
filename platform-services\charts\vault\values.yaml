# Values for HashiCorp Vault Chart
# Configuration will be passed to the vault subchart

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets:
    - ospgroup-dockerhub-secret

# HashiCorp Vault configuration
vault:
  # Global settings
  global:
    enabled: true
    tlsDisable: true

  # Injector settings (disabled for standalone deployment)
  injector:
    enabled: false

  # Server configuration
  server:
    # Image configuration
    image:
      repository: "hashicorp/vault"
      tag: "1.15.2"
      pullPolicy: IfNotPresent

    # Resources
    resources:
      requests:
        memory: 256Mi
        cpu: 250m
      limits:
        memory: 512Mi
        cpu: 500m

    # Readiness and liveness probes
    readinessProbe:
      enabled: true
      path: "/v1/sys/health?standbyok=true&sealedcode=204&uninitcode=204"
      failureThreshold: 5
      initialDelaySeconds: 10
      periodSeconds: 10
      successThreshold: 1
      timeoutSeconds: 5
    livenessProbe:
      enabled: true
      path: "/v1/sys/health?standbyok=true&sealedcode=204&uninitcode=204"
      failureThreshold: 5
      initialDelaySeconds: 120
      periodSeconds: 10
      successThreshold: 1
      timeoutSeconds: 5

    # Service configuration
    service:
      enabled: true
      type: ClusterIP
      port: 8200
      targetPort: 8200

    # Data storage
    dataStorage:
      enabled: true
      size: 10Gi
      storageClass: "local-path"
      accessMode: ReadWriteOnce

    # Audit storage
    auditStorage:
      enabled: true
      size: 5Gi
      storageClass: "local-path"
      accessMode: ReadWriteOnce

    # Standalone mode configuration
    standalone:
      enabled: true
      config: |
        ui = true
        
        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
        }
        
        storage "file" {
          path = "/vault/data"
        }
        
        # API address for UI
        api_addr = "http://127.0.0.1:8200"
        cluster_addr = "http://127.0.0.1:8201"
        
        # Disable mlock for containers
        disable_mlock = true

    # High Availability mode (disabled for standalone)
    ha:
      enabled: false

    # Node affinity
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
          - matchExpressions:
            - key: kubernetes.io/hostname
              operator: In
              values:
              - warehouse02

    # Update strategy
    updateStrategy:
      type: RollingUpdate
      rollingUpdate:
        maxUnavailable: 1

    # Pod disruption budget
    podDisruptionBudget:
      maxUnavailable: 1

  # UI configuration
  ui:
    enabled: true
    serviceType: "ClusterIP"
    serviceNodePort: null
    externalPort: 8200

  # CSI Provider (disabled)
  csi:
    enabled: false

  # Server-side TLS configuration
  serverTelemetry:
    serviceMonitor:
      enabled: false
