apiVersion: v1
kind: Secret
metadata:
  name: harbor-registry-secret
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
type: kubernetes.io/dockerconfigjson
data:
  # Base64 encoded JSON config for Harbor registry
  # Format: {"auths":{"dockerhub.ospgroup.vn":{"username":"<username>","password":"<password>","auth":"<base64(username:password)>"}}}
  # Replace with actual credentials when deploying
  .dockerconfigjson: ewogICJhdXRocyI6IHsKICAgICJkb2NrZXJodWIub3NwZ3JvdXAudm4iOiB7CiAgICAgICJ1c2VybmFtZSI6ICJPU1BfUkVHSVNUUllfVVNFUk5BTUUiLAogICAgICAicGFzc3dvcmQiOiAiT1NQX1JFR0lTVFJZX1BBU1NXT1JEIiwKICAgICAgImF1dGgiOiAiVDFOUVgxSkZSMGxUVkZKWlgxVlRSVkpPUVUxRk9rOVRVRjlTUlVkSlUxUlNXVjlRUVZOVFYwOVNSQT09IgogICAgfQogIH0KfQo=
---
# Alternative: Create secret using environment variables from Vault/External Secrets
# This is a placeholder - in production, use External Secrets Operator or similar
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-backend
  namespace: platform-services
spec:
  provider:
    vault:
      server: "http://vault.vault.svc.cluster.local:8200"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "external-secrets"
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: harbor-registry-secret-from-vault
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  refreshInterval: 15s
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: harbor-registry-secret-vault
    creationPolicy: Owner
    template:
      type: kubernetes.io/dockerconfigjson
      data:
        .dockerconfigjson: |
          {
            "auths": {
              "dockerhub.ospgroup.vn": {
                "username": "{{ .registry_username }}",
                "password": "{{ .registry_password }}",
                "auth": "{{ printf "%s:%s" .registry_username .registry_password | b64enc }}"
              }
            }
          }
  data:
  - secretKey: registry_username
    remoteRef:
      key: platform/registry/harbor
      property: username
  - secretKey: registry_password
    remoteRef:
      key: platform/registry/harbor
      property: password