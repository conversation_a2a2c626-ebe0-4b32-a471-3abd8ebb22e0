apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafdrop
  namespace: platform-services
  labels:
    app: kafdrop
    component: kafka-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafdrop
  template:
    metadata:
      labels:
        app: kafdrop
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000


      containers:
      - name: kafdrop
        image: obsidiandynamics/kafdrop:3.31.0
        ports:
        - name: http
          containerPort: 9000
          protocol: TCP
        env:
        - name: KAFKA_BROKERCONNECT
          value: "kafka:9092"
        - name: KAFKA_PROPERTIES_FILE
          value: "/etc/kafka/kafka.properties"
        - name: JVM_OPTS
          value: "-Xms64M -Xmx256M"
        - name: SERVER_SERVLET_CONTEXTPATH
          value: "/"
        - name: CMD_ARGS
          value: "--message.format=AVRO --message.keyFormat=DEFAULT --prototopic.enable=true"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 9000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: kafka-properties
          mountPath: /etc/kafka
      volumes:
      - name: tmp
        emptyDir: {}
      - name: kafka-properties
        configMap:
          name: kafka-properties
      restartPolicy: Always
