apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: kafdrop-httproute
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
  labels:
    app: kafdrop
    component: kafka-ui
spec:
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      kind: Gateway
      sectionName: web
  hostnames:
    - kafka-ui.local
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: kafdrop
          port: 9000
          weight: 100