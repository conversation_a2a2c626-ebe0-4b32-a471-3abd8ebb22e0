apiVersion: batch/v1
kind: CronJob
metadata:
  name: kafdrop-password-sync
  namespace: platform-services
spec:
  schedule: "*/10 * * * *"  # Every 10 minutes
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: kafdrop-password-sync
          containers:
          - name: password-sync
            image: bitnami/kubectl:latest
            command: ["/bin/bash"]
            args:
              - -c
              - |
                echo "🔄 Checking if Kafdrop password needs update..."
                
                # Get current password from Kafka secret
                CURRENT_PASSWORD=$(kubectl get secret kafka-user-passwords -o jsonpath='{.data.system-user-password}' | base64 -d)
                
                # Get password from ConfigMap
                CONFIGMAP_PASSWORD=$(kubectl get configmap kafka-properties -o jsonpath='{.data.kafka\.properties}' | grep -o 'password="[^"]*"' | cut -d'"' -f2)
                
                if [ "$CURRENT_PASSWORD" != "$CONFIGMAP_PASSWORD" ]; then
                    echo "🔄 Password mismatch detected. Updating ConfigMap..."
                    
                    kubectl patch configmap kafka-properties --patch "
                data:
                  kafka.properties: |
                    security.protocol=SASL_PLAINTEXT
                    sasl.mechanism=PLAIN
                    sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username=\"user1\" password=\"$CURRENT_PASSWORD\";
                "
                    
                    echo "🔄 Restarting Kafdrop deployment..."
                    kubectl rollout restart deployment/kafdrop
                    
                    echo "✅ Kafdrop password updated successfully!"
                else
                    echo "✅ Password is up to date. No action needed."
                fi
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kafdrop-password-sync
  namespace: platform-services
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kafdrop-password-sync
  namespace: platform-services
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "patch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kafdrop-password-sync
  namespace: platform-services
subjects:
- kind: ServiceAccount
  name: kafdrop-password-sync
  namespace: platform-services
roleRef:
  kind: Role
  name: kafdrop-password-sync
  apiGroup: rbac.authorization.k8s.io