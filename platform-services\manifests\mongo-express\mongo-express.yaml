apiVersion: v1
kind: Service
metadata:
  name: mongo-express
  namespace: platform-services
  labels:
    app: mongo-express
spec:
  ports:
  - port: 8081
    targetPort: 8081
  selector:
    app: mongo-express
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongo-express
  namespace: platform-services
  labels:
    app: mongo-express
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongo-express
  template:
    metadata:
      labels:
        app: mongo-express
    spec:
      containers:
      - name: mongo-express
        image: mongo-express:1.0.0-20
        ports:
        - containerPort: 8081
        env:
        - name: ME_CONFIG_MONGODB_SERVER
          value: mongodb
        - name: ME_CONFIG_MONGODB_ADMINUSERNAME
          value: root
        - name: ME_CONFIG_MONGODB_ADMINPASSWORD
          value: "admin"
        - name: ME_CONFIG_BASICAUTH_USERNAME
          value: "admin"
        - name: ME_CONFIG_BASICAUTH_PASSWORD
          value: "admin"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "250m"
