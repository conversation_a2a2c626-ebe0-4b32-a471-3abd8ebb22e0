apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgadmin
  namespace: platform-services
  labels:
    app: pgadmin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pgadmin
  template:
    metadata:
      labels:
        app: pgadmin
    spec:
      containers:
      - name: pgadmin
        image: dpage/pgadmin4:latest
        env:
        - name: PGA<PERSON><PERSON>_DEFAULT_EMAIL
          value: "<EMAIL>"
        - name: PGA<PERSON><PERSON>_DEFAULT_PASSWORD
          value: "admin123"
        - name: PGADMIN_LISTEN_PORT
          value: "80"
        - name: PGADMIN_CONFIG_SERVER_MODE
          value: "False"
        - name: GUNICORN_ACCESS_LOGFILE
          value: "-"
        - name: PGADMIN_CONFIG_ENHANCED_COOKIE_PROTECTION
          value: "False"
        - name: PGADMIN_SERVER_MODE
          value: "False"
        - name: PGADMIN_CONFIG_ALLOW_SPECIAL_EMAIL_DOMAINS
          value: "['osp.vn']"
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
        # Temporarily disabled probes for debugging
        # livenessProbe:
        #   httpGet:
        #     path: /misc/ping
        #     port: 80
        #   initialDelaySeconds: 60
        #   periodSeconds: 30
        # readinessProbe:
        #   httpGet:
        #     path: /misc/ping
        #     port: 80
        #   initialDelaySeconds: 30
        #   periodSeconds: 10