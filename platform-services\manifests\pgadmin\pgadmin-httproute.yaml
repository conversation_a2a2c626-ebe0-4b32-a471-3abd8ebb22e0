apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: pgadmin-http
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: web
  
  hostnames:
  - pgadmin.local
  
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
        
    backendRefs:
    - name: pgadmin
      port: 80
      weight: 100