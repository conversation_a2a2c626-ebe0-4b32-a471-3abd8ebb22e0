apiVersion: v1
kind: Secret
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
type: Opaque
data:
  postgres-password: cG9zdGdyZXMxMjM=  # postgres123

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
spec:
  type: ClusterIP
  ports:
    - name: tcp-postgresql
      port: 5434  # Port khác với 5432 (PG 16.4) và 5433 (PG 17)
      targetPort: postgresql
  selector:
    app.kubernetes.io/name: postgresql-18

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18-headless
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: tcp-postgresql
      port: 5434
      targetPort: postgresql
  selector:
    app.kubernetes.io/name: postgresql-18

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
spec:
  serviceName: postgresql-18-headless
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name: postgresql-18
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgresql-18
        app.kubernetes.io/part-of: postgresql-migration
        migration.version: "17-to-18"
    spec:
      securityContext:
        fsGroup: 999
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: postgresql
          image: postgres:18  # Official PostgreSQL 18 image - mới nhất
          imagePullPolicy: IfNotPresent
          securityContext:
            runAsUser: 999
            runAsGroup: 999
            runAsNonRoot: true
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop: ["ALL"]
            seccompProfile:
              type: RuntimeDefault
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-18
                  key: postgres-password
            - name: POSTGRES_USER
              value: "postgres"
            - name: POSTGRES_DB
              value: "postgres"
            - name: PGDATA
              value: "/var/lib/postgresql/data/pgdata"
            # PostgreSQL 18 specific configurations
            - name: POSTGRES_INITDB_ARGS
              value: "--data-checksums --encoding=UTF8 --locale=en_US.UTF-8"
          ports:
            - name: postgresql
              containerPort: 5432
              protocol: TCP
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "postgres" -h 127.0.0.1 -p 5432
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - -e
                - |
                  exec pg_isready -U "postgres" -h 127.0.0.1 -p 5432
                  [ -f /var/lib/postgresql/data/pgdata/PG_VERSION ] || echo "DB not initialized"
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          resources:
            limits:
              memory: "2Gi"  # Tăng memory cho PostgreSQL 18 AIO features
              cpu: "1.5"     # Tăng CPU cho parallel processing
            requests:
              memory: "1Gi"
              cpu: "750m"
          volumeMounts:
            - name: data
              mountPath: /var/lib/postgresql/data
            - name: dshm
              mountPath: /dev/shm
            - name: postgresql-config
              mountPath: /etc/postgresql/postgresql.conf
              subPath: postgresql.conf
              readOnly: true
      volumes:
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 2Gi  # Tăng shared memory cho PostgreSQL 18
        - name: postgresql-config
          configMap:
            name: postgresql-18-config
      # Pod anti-affinity để tránh cùng node với PostgreSQL 16.4 và 17
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/name: postgresql
                topologyKey: kubernetes.io/hostname
            - weight: 90
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/name: postgresql-17
                topologyKey: kubernetes.io/hostname
  volumeClaimTemplates:
    - metadata:
        name: data
        labels:
          app.kubernetes.io/name: postgresql-18
          app.kubernetes.io/part-of: postgresql-migration
          migration.version: "17-to-18"
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 30Gi  # Tăng storage cho PostgreSQL 18

---
# Service for external access với NodePort
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18-external
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
spec:
  type: NodePort
  ports:
    - name: tcp-postgresql
      port: 5434
      targetPort: postgresql
      nodePort: 32002  # Port khác với 32000 (PG 16.4) và 32001 (PG 17)
  selector:
    app.kubernetes.io/name: postgresql-18

---
# ConfigMap với PostgreSQL 18 optimizations
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-18-config
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "17-to-18"
data:
  postgresql.conf: |
    # PostgreSQL 18 Configuration - Optimized

    # Connection Settings
    listen_addresses = '*'
    port = 5432
    max_connections = 200

    # Memory Settings (optimized for PostgreSQL 18)
    shared_buffers = 512MB
    work_mem = 8MB
    maintenance_work_mem = 128MB
    effective_cache_size = 1536MB

    # PostgreSQL 18 New Features
    # Enable AIO (Asynchronous I/O) - Major feature in PostgreSQL 18
    # io_uring = on  # Uncomment if running on Linux with io_uring support

    # Query Optimization (PostgreSQL 18 improvements)
    enable_seqscan = on
    enable_indexscan = on
    enable_bitmapscan = on
    enable_hashjoin = on
    enable_mergejoin = on
    enable_nestloop = on
    enable_partitionwise_join = on
    enable_partitionwise_aggregate = on

    # Parallel Processing (enhanced in PostgreSQL 18)
    max_parallel_workers = 4
    max_parallel_workers_per_gather = 2
    max_parallel_maintenance_workers = 2

    # WAL Settings
    wal_level = replica
    max_wal_size = 2GB
    min_wal_size = 80MB
    checkpoint_completion_target = 0.9

    # Logging
    log_destination = 'stderr'
    logging_collector = off
    log_min_messages = warning
    log_error_verbosity = default
    log_statement = 'none'
    log_duration = off
    log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

    # Statistics
    track_activities = on
    track_counts = on
    track_io_timing = on
    track_functions = all

    # Autovacuum (improved in PostgreSQL 18)
    autovacuum = on
    autovacuum_max_workers = 3
    autovacuum_naptime = 1min

    # Data Checksums (enabled by default in PostgreSQL 18)
    data_checksums = on

    # Security
    password_encryption = 'scram-sha-256'
    ssl = off

    # Extensions
    shared_preload_libraries = 'pg_stat_statements'

    # PostgreSQL 18 specific optimizations
    # These settings leverage new features in PostgreSQL 18
    enable_hashagg = on
    enable_material = on
    enable_sort = on

    # NUMA-aware settings (if running on NUMA systems)
    # numa_policy = 'preferred'