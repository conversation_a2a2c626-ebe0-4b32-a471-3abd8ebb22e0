apiVersion: v1
kind: Secret
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "16-to-18"
type: Opaque
data:
  postgres-password: cG9zdGdyZXMxMjM=  # postgres123

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "16-to-18"
spec:
  type: ClusterIP
  ports:
    - name: tcp-postgresql
      port: 5433
      targetPort: postgresql
  selector:
    app.kubernetes.io/name: postgresql-18

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18-headless
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "16-to-18"
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: tcp-postgresql
      port: 5433
      targetPort: postgresql
  selector:
    app.kubernetes.io/name: postgresql-18

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-18
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "16-to-18"
spec:
  serviceName: postgresql-18-headless
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name: postgresql-18
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgresql-18
        app.kubernetes.io/part-of: postgresql-migration
        migration.version: "16-to-18"
    spec:
      securityContext:
        fsGroup: 999
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: postgresql
          image: postgres:18.0  # Official PostgreSQL 18 image
          imagePullPolicy: IfNotPresent
          securityContext:
            runAsUser: 999
            runAsGroup: 999
            runAsNonRoot: true
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop: ["ALL"]
            seccompProfile:
              type: RuntimeDefault
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-18
                  key: postgres-password
            - name: POSTGRES_USER
              value: "postgres"
            - name: POSTGRES_DB
              value: "postgres"
            - name: PGDATA
              value: "/var/lib/postgresql/data/pgdata"
          ports:
            - name: postgresql
              containerPort: 5432
              protocol: TCP
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "postgres" -h 127.0.0.1 -p 5432
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - -e
                - |
                  exec pg_isready -U "postgres" -h 127.0.0.1 -p 5432
                  [ -f /var/lib/postgresql/data/pgdata/.initialized ] || echo "DB not initialized"
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          resources:
            limits:
              memory: "1Gi"
              cpu: "1"
            requests:
              memory: "512Mi"
              cpu: "500m"
          volumeMounts:
            - name: data
              mountPath: /var/lib/postgresql/data
            - name: dshm
              mountPath: /dev/shm
      volumes:
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 1Gi
      # Pod anti-affinity để tránh cùng node với PostgreSQL 16.4
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/name: postgresql
                topologyKey: kubernetes.io/hostname
  volumeClaimTemplates:
    - metadata:
        name: data
        labels:
          app.kubernetes.io/name: postgresql-18
          app.kubernetes.io/part-of: postgresql-migration
          migration.version: "16-to-18"
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 20Gi

---
# Service for port-forward và external access
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18-external
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "16-to-18"
spec:
  type: NodePort
  ports:
    - name: tcp-postgresql
      port: 5433
      targetPort: postgresql
      nodePort: 32001  # Port khác với 32000 của PostgreSQL 16.4
  selector:
    app.kubernetes.io/name: postgresql-18