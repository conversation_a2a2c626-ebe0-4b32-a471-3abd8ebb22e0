apiVersion: helm.cattle.io/v1
kind: HelmChart
metadata:
  name: postgresql-18
  namespace: platform-services
spec:
  chart: postgresql
  repo: https://charts.bitnami.com/bitnami
  version: "16.0.0"  # Latest chart version hỗ trợ PostgreSQL 18
  targetNamespace: platform-services
  valuesContent: |-
    # Đặt tên khác để tránh conflict với PostgreSQL 16.4
    nameOverride: "postgresql-18"
    fullnameOverride: "postgresql-18"

    # PostgreSQL 18 image
    image:
      registry: docker.io
      repository: bitnami/postgresql
      tag: "18.0.0-debian-12-r0"
      digest: ""
      pullPolicy: IfNotPresent

    # Authentication
    auth:
      enablePostgresUser: true
      postgresPassword: "postgres123"
      username: ""
      password: ""
      database: ""
      existingSecret: ""
      secretKeys:
        adminPasswordKey: postgres-password
        userPasswordKey: password
        replicationPasswordKey: replication-password

    # Primary instance configuration
    primary:
      # Persistence
      persistence:
        enabled: true
        existingClaim: ""
        mountPath: /bitnami/postgresql
        subPath: ""
        storageClass: ""
        accessModes:
          - ReadWriteOnce
        size: 20Gi  # Tăng size cho safety
        annotations: {}
        labels: {}
        selector: {}
        dataSource: {}
        existingClaimResourcePolicy: "keep"

      # Service configuration - port khác để tránh conflict
      service:
        type: ClusterIP
        ports:
          postgresql: 5433  # Port khác với 5432 của PostgreSQL 16.4
        nodePorts:
          postgresql: ""
        clusterIP: ""
        externalTrafficPolicy: Cluster
        extraPorts: []
        internalTrafficPolicy: Cluster
        sessionAffinity: None
        sessionAffinityConfig: {}
        loadBalancerIP: ""
        loadBalancerSourceRanges: []
        externalIPs: []
        annotations: {}
        labels: {}
        headless:
          annotations: {}
          labels: {}

      # Resources
      resources:
        limits:
          memory: "1Gi"
          cpu: "1"
        requests:
          memory: "512Mi"
          cpu: "500m"

      # Pod Security Context
      podSecurityContext:
        enabled: true
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault

      # Container Security Context
      containerSecurityContext:
        enabled: true
        runAsUser: 1001
        runAsGroup: 1001
        runAsNonRoot: true
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
        capabilities:
          drop: ["ALL"]
        seLinuxOptions: {}
        seccompProfile:
          type: RuntimeDefault

      # Pod configuration
      podLabels: {}
      podAnnotations: {}
      priorityClassName: ""
      runtimeClassName: ""
      schedulerName: ""
      terminationGracePeriodSeconds: ""
      topologySpreadConstraints: []
      podAffinityPreset: ""
      podAntiAffinityPreset: soft
      nodeAffinityPreset:
        type: ""
        key: ""
        values: []
      affinity: {}
      nodeSelector: {}
      tolerations: []

      # Configuration
      configuration: ""
      pgHbaConfiguration: ""
      existingConfigmap: ""
      extended_conf: ""
      existingExtendedConfigmap: ""
      initdb:
        args: ""
        postgresqlWalDir: ""
        scripts: {}
        scriptsConfigMap: ""
        scriptsSecret: ""
        user: ""
        password: ""

      # Startup, liveness và readiness probes
      startupProbe:
        enabled: false
        initialDelaySeconds: 30
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 10
      livenessProbe:
        enabled: true
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 6
      readinessProbe:
        enabled: true
        initialDelaySeconds: 5
        periodSeconds: 10
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 6

    # Backup configuration (tắt để tránh conflict)
    backup:
      enabled: false

    # Metrics configuration
    metrics:
      enabled: true
      image:
        registry: docker.io
        repository: bitnami/postgres-exporter
        tag: 0.15.0-debian-12-r43
        digest: ""
        pullPolicy: IfNotPresent

      # Metrics service - port khác
      service:
        ports:
          metrics: 9188  # Port khác với 9187 của PostgreSQL 16.4
        clusterIP: ""
        sessionAffinity: None
        annotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "9188"
        labels: {}

      serviceMonitor:
        enabled: true
        namespace: ""
        interval: ""
        scrapeTimeout: ""
        labels: {}
        selector: {}
        relabelings: []
        metricRelabelings: []
        honorLabels: false
        jobLabel: ""

      prometheusRule:
        enabled: false
        namespace: ""
        labels: {}
        rules: []

      resources:
        limits:
          memory: "256Mi"
          cpu: "250m"
        requests:
          memory: "128Mi"
          cpu: "100m"

    # Architecture
    architecture: standalone

    # Common labels
    commonLabels:
      app.kubernetes.io/part-of: "postgresql-migration"
      migration.version: "16-to-18"

    # Diagnostic mode
    diagnosticMode:
      enabled: false

    # Service account
    serviceAccount:
      create: true
      name: ""
      automountServiceAccountToken: false
      annotations: {}

    # RBAC
    rbac:
      create: true
      rules: []

    # Pod Security Policy
    psp:
      create: false

    # Network Policy
    networkPolicy:
      enabled: false
      allowExternal: true
      allowExternalEgress: true
      extraIngress: []
      extraEgress: []
      ingressNSMatchLabels: {}
      ingressNSPodMatchLabels: {}

    # Volume Permissions
    volumePermissions:
      enabled: false
      image:
        registry: docker.io
        repository: bitnami/bitnami-shell
        tag: 12-debian-12-r33
        digest: ""
        pullPolicy: IfNotPresent
      resources:
        limits: {}
        requests: {}
      containerSecurityContext:
        runAsUser: 0
        seLinuxOptions: {}
        seccompProfile:
          type: RuntimeDefault

    # Shmem
    shmVolume:
      enabled: true
      sizeLimit: ""

    # TLS
    tls:
      enabled: false