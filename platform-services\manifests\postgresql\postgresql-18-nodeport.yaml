---
# NodePort Service for PostgreSQL 18 External Access
apiVersion: v1
kind: Service
metadata:
  name: postgresql-18-external
  namespace: platform-services
  labels:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/part-of: postgresql-migration
    migration.version: "18"
  annotations:
    postgresql.version: "18.0.0"
    deployment.purpose: "migration-testing"
spec:
  type: NodePort
  ports:
    - name: tcp-postgresql
      port: 5433
      targetPort: postgresql
      nodePort: 32001  # Port khác với PostgreSQL 16 (32000)
      protocol: TCP
  selector:
    app.kubernetes.io/name: postgresql-18
    app.kubernetes.io/component: primary
