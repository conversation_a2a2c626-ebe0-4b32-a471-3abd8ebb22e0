apiVersion: v1
kind: Service
metadata:
  name: postgresql-nodeport
  namespace: platform-services
  labels:
    app: postgresql
    component: nodeport
spec:
  type: NodePort
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    nodePort: 30432  # Expose on all nodes at port 30432
    protocol: TCP
  selector:
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/instance: postgresql