apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: deepwiki-frontend-api-rewrite
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  replacePathRegex:
    # Rewrite frontend API calls to correct backend endpoints
    # /api/lang/config -> /lang/config
    # /api/auth/status -> /auth/status
    # /api/models/config -> /models/config
    # /api/chat/stream -> /chat/completions/stream
    regex: ^/api/(lang/config|auth/status|models/config)$
    replacement: /$1
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: deepwiki-chat-stream-rewrite
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  replacePathRegex:
    # Rewrite /api/chat/stream to /chat/completions/stream
    regex: ^/api/chat/stream$
    replacement: /chat/completions/stream
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: deepwiki-wiki-projects-rewrite
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  replacePathRegex:
    # Rewrite /api/wiki/projects to /api/processed_projects
    regex: ^/api/wiki/projects$
    replacement: /api/processed_projects
