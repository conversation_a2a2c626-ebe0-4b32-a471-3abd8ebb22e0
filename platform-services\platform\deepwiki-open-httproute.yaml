apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: deepwiki-open-subdomain-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: websecure
  hostnames:
  - wiki.ospgroup.io.vn
  rules:
  # Frontend API calls that need rewriting
  - matches:
    - path:
        type: Exact
        value: /api/lang/config
    - path:
        type: Exact
        value: /api/auth/status
    - path:
        type: Exact
        value: /api/models/config
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: deepwiki-frontend-api-rewrite
    backendRefs:
    - name: deepwiki-open-backend
      port: 8001
      weight: 100

  # Wiki projects endpoint rewrite
  - matches:
    - path:
        type: Exact
        value: /api/wiki/projects
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: deepwiki-wiki-projects-rewrite
    backendRefs:
    - name: deepwiki-open-backend
      port: 8001
      weight: 100

  # Chat stream endpoint rewrite
  - matches:
    - path:
        type: Exact
        value: /api/chat/stream
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: deepwiki-chat-stream-rewrite
    backendRefs:
    - name: deepwiki-open-backend
      port: 8001
      weight: 100

  # All other API routes go to backend - simplified routing
  - matches:
    - path:
        type: PathPrefix
        value: /api
    - path:
        type: PathPrefix
        value: /models
    - path:
        type: PathPrefix
        value: /auth
    - path:
        type: PathPrefix
        value: /export
    - path:
        type: PathPrefix
        value: /local_repo
    - path:
        type: PathPrefix
        value: /chat
    - path:
        type: PathPrefix
        value: /lang
    - path:
        type: PathPrefix
        value: /health
    - path:
        type: PathPrefix
        value: /docs
    - path:
        type: PathPrefix
        value: /wiki_cache
    - path:
        type: PathPrefix
        value: /wiki
    backendRefs:
    - name: deepwiki-open-backend
      port: 8001
      weight: 100
  # Main frontend application - serve at root path for everything else
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: deepwiki-open-frontend
      port: 3000
      weight: 100