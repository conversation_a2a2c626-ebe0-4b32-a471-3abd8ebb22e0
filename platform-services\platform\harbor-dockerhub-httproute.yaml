apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: harbor-dockerhub-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
    traefik.ingress.kubernetes.io/router.middlewares: platform-services-harbor-timeout@kubernetescrd
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: websecure
  hostnames:
  - dockerhub.ospgroup.vn
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /v2/
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
  - matches:
    - path:
        type: PathPrefix
        value: /service/token
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
  - matches:
    - path:
        type: PathPrefix
        value: /chartrepo/
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
  - matches:
    - path:
        type: PathPrefix
        value: /api/
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
  - matches:
    - path:
        type: PathPrefix
        value: /c/
    backendRefs:
    - name: harbor
      port: 80
      weight: 100
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: harbor
      port: 80
      weight: 100