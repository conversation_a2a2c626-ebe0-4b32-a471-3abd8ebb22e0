apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: harbor-route
  namespace: platform-services
  annotations:
    traefik.ingress.kubernetes.io/router.middlewares: platform-services-harbor-headers@kubernetescrd
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
  hostnames:
  - harbor.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: harbor
      port: 80
      weight: 100