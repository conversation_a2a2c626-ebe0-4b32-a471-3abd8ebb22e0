apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: harbor-headers
  namespace: platform-services
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
      X-Forwarded-Host: "dockerhub.ospgroup.vn"
      X-Forwarded-Port: "443"
      X-Real-IP: ""
      X-Forwarded-For: ""
    customResponseHeaders:
      X-Frame-Options: "SAMEORIGIN"
    hostsProxyHeaders:
      - "X-Forwarded-Host"