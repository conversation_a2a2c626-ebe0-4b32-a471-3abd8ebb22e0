apiVersion: batch/v1
kind: Job
metadata:
  name: keycloak-db-init
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  template:
    spec:
      containers:
      - name: init-db
        image: bitnami/postgresql:16
        env:
        - name: PGPASSWORD
          value: "postgres123"
        command: ["/bin/bash", "-c"]
        args:
          - |
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "SELECT 1 FROM pg_database WHERE datname = 'keycloak';" | grep -q 1 || psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "CREATE DATABASE keycloak;"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "SELECT 1 FROM pg_roles WHERE rolname = 'keycloak';" | grep -q 1 || psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "CREATE USER keycloak WITH ENCRYPTED PASSWORD 'keycloak-db-password123';"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE keycloak TO keycloak;"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d keycloak -c "GRANT ALL ON SCHEMA public TO keycloak;"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d keycloak -c "GRANT CREATE ON SCHEMA public TO keycloak;"
      restartPolicy: OnFailure
