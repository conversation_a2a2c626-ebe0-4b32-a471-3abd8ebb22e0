apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: keycloak-sso-httproute
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    kind: Gateway
  hostnames:
  - sso.ospgroup.io.vn
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /auth
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: keycloak
      port: 80
      weight: 100