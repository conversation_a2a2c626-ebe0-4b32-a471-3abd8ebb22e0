apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: labelstudio-common-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: websecure
  hostnames:
  - common.ospgroup.vn
  rules:
  # Static files - no prefix stripping
  - matches:
    - path:
        type: PathPrefix
        value: /label-studio/static
    - path:
        type: PathPrefix
        value: /label-studio/react-app
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: labelstudio-static-rewrite
    backendRefs:
    - name: labelstudio
      port: 80
      weight: 100
  # Main application - with prefix stripping
  - matches:
    - path:
        type: PathPrefix
        value: /label-studio
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: labelstudio-stripprefix
    backendRefs:
    - name: labelstudio
      port: 80
      weight: 100
