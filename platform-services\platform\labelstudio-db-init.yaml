apiVersion: batch/v1
kind: Job
metadata:
  name: labelstudio-db-init-v2
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  template:
    metadata:
      name: labelstudio-db-init-v2
    spec:
      restartPolicy: OnFailure
      containers:
      - name: postgres-client
        image: postgres:16-alpine
        command:
        - /bin/sh
        - -c
        - |
          echo "Đang kết nối đến PostgreSQL để khởi tạo database cho Label Studio..."
          
          # Kiểm tra kết nối PostgreSQL
          until pg_isready -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER; do
            echo "Đang chờ PostgreSQL sẵn sàng..."
            sleep 2
          done
          
          echo "PostgreSQL đã sẵn sàng. <PERSON><PERSON> thực thi script khởi tạo..."
          
          # Thự<PERSON> thi script SQL
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -f /scripts/init-labelstudio-db.sql
          
          if [ $? -eq 0 ]; then
            echo "Khởi tạo database Label Studio thành công!"
          else
            echo "Lỗi khi khởi tạo database Label Studio!"
            exit 1
          fi
        env:
        - name: POSTGRES_HOST
          value: "postgresql.platform-services.svc.cluster.local"
        - name: POSTGRES_PORT
          value: "5432"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql
              key: postgres-password
        volumeMounts:
        - name: init-script
          mountPath: /scripts
          readOnly: true
      volumes:
      - name: init-script
        configMap:
          name: labelstudio-db-init-script
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: labelstudio-db-init-script
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  init-labelstudio-db.sql: |
    -- Script khởi tạo database và user cho Label Studio
    -- Tạo user labelstudio với mật khẩu mạnh
    CREATE USER labelstudio WITH PASSWORD 'LS_2025_SecurePass_9x7!';

    -- Tạo database labelstudio
    CREATE DATABASE labelstudio OWNER labelstudio;

    -- Cấp quyền cho user labelstudio
    GRANT ALL PRIVILEGES ON DATABASE labelstudio TO labelstudio;

    -- Kết nối đến database labelstudio để cấp quyền schema
    \c labelstudio;

    -- Cấp quyền trên schema public
    GRANT ALL ON SCHEMA public TO labelstudio;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO labelstudio;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO labelstudio;

    -- Cấp quyền mặc định cho các table và sequence sẽ được tạo trong tương lai
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO labelstudio;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO labelstudio;

    -- Hiển thị thông tin xác nhận
    SELECT 'Label Studio database and user created successfully!' as status;
