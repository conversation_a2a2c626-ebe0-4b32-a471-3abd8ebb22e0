apiVersion: apps/v1
kind: Deployment
metadata:
  name: labelstudio
  namespace: platform-services
  labels:
    app: labelstudio
    app.kubernetes.io/name: labelstudio
    app.kubernetes.io/instance: labelstudio
spec:
  replicas: 1
  selector:
    matchLabels:
      app: labelstudio
  template:
    metadata:
      labels:
        app: labelstudio
        app.kubernetes.io/name: labelstudio
        app.kubernetes.io/instance: labelstudio
    spec:
      securityContext:
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: labelstudio
        image: heartexlabs/label-studio:1.19.0
        ports:
        - containerPort: 8080
          name: http
        env:
        # Database configuration
        - name: POSTGRE_NAME
          value: "labelstudio"
        - name: POSTGRE_USER
          value: "labelstudio"
        - name: POSTGRE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: labelstudio-db-secret
              key: password
        - name: POSTGRE_HOST
          value: "postgresql.platform-services.svc.cluster.local"
        - name: POSTGRE_PORT
          value: "5432"
        
        # Redis configuration
        - name: REDIS_LOCATION
          value: "redis://:<EMAIL>:6379/2"
        
        # Label Studio configuration
        - name: LABEL_STUDIO_HOST
          value: "https://common.ospgroup.vn/label-studio"
        - name: LABEL_STUDIO_BASE_DATA_DIR
          value: "/label-studio/data"

        # Django subpath configuration
        - name: USE_X_FORWARDED_HOST
          value: "true"
        - name: USE_X_FORWARDED_PORT
          value: "true"
        - name: SECURE_PROXY_SSL_HEADER
          value: "HTTP_X_FORWARDED_PROTO,https"
        - name: STATIC_URL
          value: "/label-studio/static/"
        - name: MEDIA_URL
          value: "/label-studio/media/"

        # CSRF and trusted origins configuration
        - name: CSRF_TRUSTED_ORIGINS
          value: "https://common.ospgroup.vn"
        - name: ALLOWED_HOSTS
          value: "*"
        - name: CSRF_COOKIE_SECURE
          value: "1"
        - name: CSRF_COOKIE_HTTPONLY
          value: "1"
        - name: CSRF_COOKIE_SAMESITE
          value: "Lax"

        # Security
        - name: DJANGO_DB
          value: "default"
        - name: LABEL_STUDIO_DISABLE_SIGNUP_WITHOUT_LINK
          value: "false"
        
        volumeMounts:
        - name: data
          mountPath: /label-studio/data
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        securityContext:
          runAsUser: 1001
          runAsNonRoot: true
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: data
        emptyDir: {}
      
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: labelstudio
  namespace: platform-services
  labels:
    app: labelstudio
    app.kubernetes.io/name: labelstudio
    app.kubernetes.io/instance: labelstudio
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: labelstudio
