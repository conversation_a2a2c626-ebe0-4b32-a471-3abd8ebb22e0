apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: minio-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    kind: Gateway
  hostnames:
  - minio.ospgroup.io.vn
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: minio-console
      port: 9090
      weight: 100