apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: nexus-repository-manager-common-route
  namespace: platform-services
  labels:
    app.kubernetes.io/name: nexus-repository-manager
    app.kubernetes.io/component: gateway-httproute
    app.kubernetes.io/part-of: platform-services
spec:
  parentRefs:
    - name: traefik-gateway
      namespace: bootstrap
      sectionName: websecure
  hostnames:
    - package.ospgroup.io.vn
  rules:
    - backendRefs:
        - name: nexus-repository-manager
          port: 8081
          weight: 100
