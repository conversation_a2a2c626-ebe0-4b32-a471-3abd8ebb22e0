apiVersion: v1
kind: Secret
metadata:
  name: nexus-database-secret
  namespace: platform-services
  labels:
    app.kubernetes.io/name: nexus-repository-manager
    app.kubernetes.io/component: database-secret
    app.kubernetes.io/part-of: platform-services
type: Opaque
data:
  # Database password: NxRm2025!@#SecurePass_9x7
  password: TnhSbTIwMjUhQCNTZWN1cmVQYXNzXzl4Nw==
  # Username: nexus
  username: bmV4dXM=
