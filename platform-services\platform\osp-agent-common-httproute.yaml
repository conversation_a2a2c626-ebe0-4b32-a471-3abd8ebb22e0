apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: osp-agent-common-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: websecure
  hostnames:
  - common.ospgroup.io.vn
  rules:
  # Static files - no prefix stripping for assets
  - matches:
    - path:
        type: PathPrefix
        value: /osp-agent/static
    backendRefs:
    - name: osp-agent
      port: 80
      weight: 100
  # Main application - with prefix stripping
  - matches:
    - path:
        type: PathPrefix
        value: /osp-agent
    filters:
    - type: ExtensionRef
      extensionRef:
        group: traefik.io
        kind: Middleware
        name: osp-agent-stripprefix
    backendRefs:
    - name: osp-agent
      port: 80
      weight: 100
