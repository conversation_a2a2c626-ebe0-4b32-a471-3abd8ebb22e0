apiVersion: apps/v1
kind: Deployment
metadata:
  name: osp-agent
  namespace: platform-services
  labels:
    app: osp-agent
    app.kubernetes.io/name: osp-agent
    app.kubernetes.io/instance: osp-agent
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: osp-agent
  template:
    metadata:
      labels:
        app: osp-agent
        app.kubernetes.io/name: osp-agent
        app.kubernetes.io/instance: osp-agent
    spec:
      securityContext:
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: osp-agent
        image: dockerhub.ospgroup.vn/osp-public/osp-agent:libsecret-bitable-fix-20250928-200542
        ports:
        - containerPort: 8000
          name: http
        env:
        # Core Lark Configuration
        - name: LARK_APP_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-app-id
        - name: LARK_APP_SECRET
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-app-secret
        - name: BOT_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: bot-id
        - name: VERIFICATION_TOKEN
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: verification-token
        - name: ENCRYPT_KEY
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: encrypt-key
        
        # AI Models Configuration
        - name: LLM_PROVIDER
          value: "openrouter"
        - name: LLM_MODEL_NAME
          value: "anthropic/claude-3.5-sonnet"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: openai-api-key
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: google-api-key
        - name: GEMINI_API_KEY
          value: "AIzaSyDeVkTfpcSLyM4ftOh987xf8iOAat9-QMk"
        - name: OPENROUTER_API_KEY
          value: "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"

        # Lark Bitable Configuration
        - name: APP_CONFIG_TOKEN
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-token
        - name: APP_CONFIG_ADMINISTRATION_TOKEN
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-administration-token
        - name: APP_CONFIG_TABLE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-table-project-id
        - name: APP_CONFIG_TABLE_USER_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-table-user-id
        - name: APP_CONFIG_TABLE_CONFIG_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-table-config-id
        - name: APP_CONFIG_TABLE_CANDIDATE_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: app-config-table-candidate-id

        # Lark Configuration IDs
        - name: LARK_ADMIN_USER_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-admin-user-id
        - name: LARK_GROUP_RECEIVE_EVENT_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-group-receive-event-id
        - name: LARK_CF_GIT_ISSUE
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-cf-git-issue
        - name: LARK_CF_VERSION
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-cf-version
        - name: LARK_CF_PRIORITY
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-cf-priority
        - name: LARK_PRIORITY_MEDIUM
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-priority-medium
        - name: LARK_CF_ROLE
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-cf-role
        - name: LARK_CF_TASK_TYPE
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-cf-task-type
        - name: LARK_TYPE_TASK
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-type-task
        - name: LARK_TYPE_BUG
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-type-bug
        - name: LARK_FIELD_ATTACHMENT_ID
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-field-attachment-id

        # Lark Approval Codes
        - name: LARK_APPROVAL_CODE_PROPOSAL
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-approval-code-proposal
        - name: LARK_APPROVAL_CODE_CORRECTION
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-approval-code-correction
        - name: LARK_APPROVAL_CODE_LEAVE
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: lark-approval-code-leave

        # Email Configuration
        - name: SMTP_SERVER
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: smtp-server
        - name: SMTP_PORT
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: smtp-port
        - name: EMAIL_SENDER
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: email-sender

        # GitHub Integration
        - name: GITHUB_OWNER
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: github-owner
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: github-token
        - name: REPO_URL
          value: "https://github.com/ospgroupvn/k8s-deployment"
        - name: ACCESS_TOKEN
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: github-token
        
        # Application Configuration for subpath deployment
        - name: ROOT_PATH
          value: "/osp-agent"
        - name: BASE_HREF
          value: "/osp-agent"
        - name: DEPLOYMENT_DOMAIN
          value: "https://common.ospgroup.io.vn"
        - name: APP_BASE_URL
          value: "https://common.ospgroup.io.vn/osp-agent"
        - name: LARK_REDIRECT_URI
          value: "https://common.ospgroup.io.vn/osp-agent/oauth/callback"
        
        # Lark Configuration
        # CRITICAL: Include messaging permissions to fix error 99991679 - "Unauthorized"
        - name: LARK_SCOPES
          value: "offline_access im:message:send_as_bot im:message im:message:readonly bitable:app bitable:app:readonly task:task:read task:task:write task:tasklist:read task:tasklist:write task:section:read task:section:write task:custom_field:read task:custom_field:write task:comment:read task:comment:write"
        - name: CALLBACK_HOST
          value: "https://common.ospgroup.io.vn/osp-agent"
        - name: CALLBACK_PATH
          value: "oauth/callback"
        
        # Redis Configuration
        - name: REDIS_URL
          value: "redis://:<EMAIL>:6379/3"
        
        # Logging and Environment
        - name: LOG_LEVEL
          value: "INFO"
        - name: PYTHONPATH
          value: "/app"
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: PYTHONDONTWRITEBYTECODE
          value: "1"
        
        # Security Configuration
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: osp-agent-secrets
              key: secret-key
        - name: JWT_ALGORITHM
          value: "HS256"
        - name: ACCESS_TOKEN_EXPIRE_MINUTES
          value: "1440"
        
        # Optional Features
        - name: ENABLE_SCHEDULER
          value: "true"
        - name: ENABLE_WEBHOOKS
          value: "true"
        - name: ENABLE_METRICS
          value: "true"
        
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: docker-sock
          mountPath: /var/run/docker.sock
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        
        securityContext:
          runAsUser: 1001
          runAsNonRoot: true
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: logs
        emptyDir: {}
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
          type: Socket
      
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: osp-agent
  namespace: platform-services
  labels:
    app: osp-agent
    app.kubernetes.io/name: osp-agent
    app.kubernetes.io/instance: osp-agent
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: osp-agent
