apiVersion: v1
kind: Secret
metadata:
  name: osp-agent-secrets
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "0"
type: Opaque
data:
  # Core Lark Configuration (base64 encoded)
  # LARK_APP_ID=********************
  lark-app-id: ****************************
  # LARK_APP_SECRET=fyGybbGrE4xPZvC7Pfwa9czb2KxJGlrF
  lark-app-secret: ZnlHeWJiR3JFNHhQWnZDN1Bmd2E5Y3piMkt4SkdsckY=
  # BOT_ID=ou_1c2df658e3ba71a69ccda876db064777
  bot-id: b3VfMWMyZGY2NThlM2JhNzFhNjljY2RhODc2ZGIwNjQ3Nzc=
  # VERIFICATION_TOKEN=6fXONxZym7tS6QFo9hMJadjEWuGhE4zs
  verification-token: NmZYT054WnltN3RTNlFGbzloTUphZGpFV3VHaEU0enM=
  # ENCRYPT_KEY=SKo9LB24Afc3aR8Mnpjd2dZgM7tBAdW5
  encrypt-key: U0tvOUxCMjRBZmMzYVI4TW5wamQyZFpnTTd0QkFkVzU=
  
  # AI Models Configuration
  # OPENAI_API_KEY=********************************************************************************************************************************************************************
  openai-api-key: ************************************************************************************************************************************************************************************************************************
  # GOOGLE_API_KEY=AIzaSyCIjdlOt-Hs5QCO9B92NSJN351WvbOit7I
  google-api-key: QUl6YVN5Q0lqZGxPdC1IczVRQ085QjkyTlNKTjM1MVd2Yk9pdDdJ
  # OPENROUTER_API_KEY (placeholder - cần cập nhật với key thực)
  openrouter-api-key: c2stb3ItdjEtZTBiZGYzYzU4YzE0ZGQ2MDQ3MjI0ZDU0N2FlZDNiNWFlNjExZTZiOGEzMGZlNmVmMWNhZTkzZmIxODEzZWNjNA==
  
  # Lark Configuration IDs
  # LARK_ADMIN_USER_ID=ou_ff8be3b9e5f522e1a235e8ad866364c6
  lark-admin-user-id: b3VfZmY4YmUzYjllNWY1MjJlMWEyMzVlOGFkODY2MzY0YzY=
  # LARK_GROUP_RECEIVE_EVENT_ID=oc_6bf1caf4756761c6f04b159490c70bbc
  lark-group-receive-event-id: b2NfNmJmMWNhZjQ3NTY3NjFjNmYwNGIxNTk0OTBjNzBiYmM=
  # LARK_CF_GIT_ISSUE=3de23f49-44c3-4226-bb4f-491a10b4d689
  lark-cf-git-issue: M2RlMjNmNDktNDRjMy00MjI2LWJiNGYtNDkxYTEwYjRkNjg5
  # LARK_CF_VERSION=9c69d98c-1c71-4023-a462-74d6111d908f
  lark-cf-version: OWM2OWQ5OGMtMWM3MS00MDIzLWE0NjItNzRkNjExMWQ5MDhm
  # LARK_CF_PRIORITY=35b3f1de-fd90-4720-9756-007e1b1e7fe0
  lark-cf-priority: MzViM2YxZGUtZmQ5MC00NzIwLTk3NTYtMDA3ZTFiMWU3ZmUw
  # LARK_PRIORITY_MEDIUM=c3bc395e-1056-4978-9e4e-533fb680b719
  lark-priority-medium: YzNiYzM5NWUtMTA1Ni00OTc4LTllNGUtNTMzZmI2ODBiNzE5
  # LARK_CF_ROLE=2e6767f3-d82d-4a55-bb0b-12fad9691d72
  lark-cf-role: MmU2NzY3ZjMtZDgyZC00YTU1LWJiMGItMTJmYWQ5NjkxZDcy
  # LARK_CF_TASK_TYPE=717a1888-e8c1-4c96-904e-c529fb327335
  lark-cf-task-type: NzE3YTE4ODgtZThjMS00Yzk2LTkwNGUtYzUyOWZiMzI3MzM1
  # LARK_TYPE_TASK=495b9170-2268-44b7-97d3-5c4f590f05b6
  lark-type-task: NDk1YjkxNzAtMjI2OC00NGI3LTk3ZDMtNWM0ZjU5MGYwNWI2
  # LARK_TYPE_BUG=d03696aa-d0c6-421a-9f13-80b5618b5fc6
  lark-type-bug: ZDAzNjk2YWEtZDBjNi00MjFhLTlmMTMtODBiNTYxOGI1ZmM2
  # LARK_FIELD_ATTACHMENT_ID=fld0V0Hm5E
  lark-field-attachment-id: ZmxkMFYwSG01RQ==

  # Lark Approval Codes
  # LARK_APPROVAL_CODE_PROPOSAL=2FCC4B5A-C59E-4DF2-9A6A-F34970644D95
  lark-approval-code-proposal: MkZDQzRCNUEtQzU5RS00REYyLTlBNkEtRjM0OTcwNjQ0RDk1
  # LARK_APPROVAL_CODE_CORRECTION=2FCC4B5A-C59E-4DF2-9A6A-F34970644D95
  lark-approval-code-correction: MkZDQzRCNUEtQzU5RS00REYyLTlBNkEtRjM0OTcwNjQ0RDk1
  # LARK_APPROVAL_CODE_LEAVE=2FCC4B5A-C59E-4DF2-9A6A-F34970644D95
  lark-approval-code-leave: MkZDQzRCNUEtQzU5RS00REYyLTlBNkEtRjM0OTcwNjQ0RDk1

  # Email Configuration
  # SMTP_SERVER=smtp.gmail.com
  smtp-server: c210cC5nbWFpbC5jb20=
  # SMTP_PORT=587
  smtp-port: NTg3
  # EMAIL_SENDER=<EMAIL>
  email-sender: aHJAb3NwLmNvbS52bg==

  # GitHub Integration
  # GITHUB_OWNER=ospgroupvn
  github-owner: b3NwZ3JvdXB2bg==
  # GITHUB_TOKEN=****************************************
  github-token: ********************************************************
  
  # Lark Bitable Configuration
  # APP_CONFIG_TOKEN=KrwMbLC6NaWFHisXk3hl6Lv5g1f
  app-config-token: S3J3TWJMQzZOYVdGSGlzWGszaGw2THY1ZzFm
  # APP_CONFIG_ADMINISTRATION_TOKEN=Aww4bgwEdaDr0lsfGrglt4mpgwb
  app-config-administration-token: QXd3NGJnd0VkYURyMGxzZkdyZ2x0NG1wZ3di
  # APP_CONFIG_TABLE_PROJECT_ID=tblyucQThrTpRNno
  app-config-table-project-id: dGJseXVjUVRoclRwUk5ubw==
  # APP_CONFIG_TABLE_USER_ID=tbl6mi29VZrUKNIo
  app-config-table-user-id: dGJsNm1pMjlWWnJVS05Jbw==
  # APP_CONFIG_TABLE_CONFIG_ID=tblc6MYy3knGhmgW
  app-config-table-config-id: dGJsYzZNWXkza25HaG1nVw==
  # APP_CONFIG_TABLE_CANDIDATE_ID=tblZFMkz1q6dqE1b
  app-config-table-candidate-id: dGJsWkZNa3oxcTZkcUUxYg==

  # Security Configuration
  # SECRET_KEY (generated random key for JWT signing)
  secret-key: b3NwLWFnZW50LXNlY3JldC1rZXktZm9yLWp3dC1zaWduaW5nLTIwMjU=
