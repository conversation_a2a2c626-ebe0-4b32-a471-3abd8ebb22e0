apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: ospgroup-https-gateway
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/component: gateway
    app.kubernetes.io/name: traefik
    app.kubernetes.io/part-of: platform-services
spec:
  gatewayClassName: traefik
  listeners:
  - name: common-https
    port: 8443
    protocol: HTTPS
    hostname: common.ospgroup.vn
    tls:
      mode: Terminate
      certificateRefs:
      - name: common-ospgroup-tls
        namespace: platform-services
    allowedRoutes:
      namespaces:
        from: All
  - name: common-io-vn-https
    port: 8443
    protocol: HTTPS
    hostname: common.ospgroup.io.vn
    tls:
      mode: Terminate
      certificateRefs:
      - name: wildcard-ospgroup-io-vn-tls-secret
        namespace: bootstrap
    allowedRoutes:
      namespaces:
        from: All
  - name: dockerhub-https
    port: 8443
    protocol: HTTPS
    hostname: dockerhub.ospgroup.vn
    tls:
      mode: Terminate
      certificateRefs:
      - name: dockerhub-ospgroup-tls
        namespace: platform-services
    allowedRoutes:
      namespaces:
        from: All
  - name: wiki-https
    port: 8443
    protocol: HTTPS
    hostname: wiki.ospgroup.vn
    tls:
      mode: Terminate
      certificateRefs:
      - name: wiki-ospgroup-tls
        namespace: platform-services
    allowedRoutes:
      namespaces:
        from: All