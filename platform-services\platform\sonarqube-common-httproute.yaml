apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: sonarqube-common-route
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
    traefik.ingress.kubernetes.io/router.middlewares: platform-services-sonarqube-stripprefix@kubernetescrd
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    sectionName: websecure
  hostnames:
  - common.ospgroup.vn
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /sonarqube
    backendRefs:
    - name: sonarqube-sonarqube
      port: 9000
      weight: 100
