apiVersion: batch/v1
kind: Job
metadata:
  name: sonarqube-db-init
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  template:
    spec:
      containers:
      - name: init-db
        image: bitnami/postgresql:16
        env:
        - name: PGPASSWORD
          value: "postgres123"
        command: ["/bin/bash", "-c"]
        args:
          - |
            # Check if sonarqube database exists, create if not
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "SELECT 1 FROM pg_database WHERE datname = 'sonarqube';" | grep -q 1 || {
              echo "Creating sonarqube database..."
              psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "CREATE DATABASE sonarqube WITH OWNER = postgres ENCODING = 'UTF8' LC_COLLATE = 'en_US.utf8' LC_CTYPE = 'en_US.utf8' TABLESPACE = pg_default CONNECTION LIMIT = -1;"
            }
            
            # Check if sonarqube user exists, create if not
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "SELECT 1 FROM pg_roles WHERE rolname = 'sonarqube';" | grep -q 1 || {
              echo "Creating sonarqube user..."
              psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "CREATE USER sonarqube WITH PASSWORD '9UYfpwEupFFU3k6UyafM';"
            }
            
            # Grant database privileges
            echo "Granting database privileges..."
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE sonarqube TO sonarqube;"
            
            # Connect to sonarqube database and set schema permissions
            echo "Setting schema permissions..."
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d sonarqube -c "GRANT ALL ON SCHEMA public TO sonarqube;"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d sonarqube -c "GRANT CREATE ON SCHEMA public TO sonarqube;"
            
            # Set default privileges for future objects
            echo "Setting default privileges..."
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d sonarqube -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO sonarqube;"
            psql -h postgresql.platform-services.svc.cluster.local -U postgres -d sonarqube -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO sonarqube;"
            
            echo "SonarQube database and user setup completed successfully"
      restartPolicy: OnFailure