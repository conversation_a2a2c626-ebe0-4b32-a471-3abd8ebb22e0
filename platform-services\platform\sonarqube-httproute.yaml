apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: sonarqube-httproute
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
  hostnames:
  - sonarqube.local
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /sonarqube
    backendRefs:
    - name: sonarqube-sonarqube
      port: 9000
      weight: 100
  - matches:
    - path:
        type: Exact
        value: /
    filters:
    - type: RequestRedirect
      requestRedirect:
        path:
          type: ReplaceFullPath
          replaceFullPath: /sonarqube/
        statusCode: 302