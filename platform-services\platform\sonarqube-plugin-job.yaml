apiVersion: batch/v1
kind: Job
metadata:
  name: sonarqube-plugin-installer
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  template:
    spec:
      containers:
      - name: plugin-installer
        image: curlimages/curl:latest
        command:
        - sh
        - -c
        - |
          # Wait for SonarQube pod to be ready
          echo "Waiting for SonarQube pod..."
          until kubectl get pod sonarqube-sonarqube-0 -n platform-services; do
            echo "SonarQube pod not found, waiting..."
            sleep 10
          done

          # Wait for plugins directory to be available
          echo "Checking plugins directory..."
          kubectl exec sonarqube-sonarqube-0 -n platform-services -- mkdir -p /opt/sonarqube/extensions/plugins

          # Download plugin
          echo "Downloading Community Branch Plugin..."
          kubectl exec sonarqube-sonarqube-0 -n platform-services -- curl -L -o /opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-1.21.0.jar \
            "https://github.com/mc1arke/sonarqube-community-branch-plugin/releases/download/1.21.0/sonarqube-community-branch-plugin-1.21.0.jar"

          # Verify download
          kubectl exec sonarqube-sonarqube-0 -n platform-services -- ls -la /opt/sonarqube/extensions/plugins/

          echo "Plugin installation completed"
      restartPolicy: OnFailure
      serviceAccountName: sonarqube-plugin-installer
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: sonarqube-plugin-installer
  namespace: platform-services
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: sonarqube-plugin-installer
  namespace: platform-services
rules:
- apiGroups: [""]
  resources: ["pods", "pods/exec"]
  verbs: ["get", "list", "create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: sonarqube-plugin-installer
  namespace: platform-services
subjects:
- kind: ServiceAccount
  name: sonarqube-plugin-installer
  namespace: platform-services
roleRef:
  kind: Role
  name: sonarqube-plugin-installer
  apiGroup: rbac.authorization.k8s.io