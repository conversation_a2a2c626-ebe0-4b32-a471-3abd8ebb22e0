apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: vault-httproute
  namespace: platform-services
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
  - name: traefik-gateway
    namespace: bootstrap
    kind: Gateway
  hostnames:
  - vault.ospgroup.io.vn
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: vault
      port: 8200
      weight: 100
