# PostgreSQL 17 Helm Values cho migration step-by-step
# Deploy PostgreSQL 17 tr<PERSON><PERSON><PERSON>, sau đ<PERSON> upgrade lên 18
nameOverride: "postgresql-17"
fullnameOverride: "postgresql-17"

# PostgreSQL 17 image - stable version
image:
  registry: docker.io
  repository: bitnami/postgresql
  tag: "17.0.0-debian-12-r7"
  pullPolicy: IfNotPresent

# Authentication - giữ nguyên password để dễ migration
auth:
  enablePostgresUser: true
  postgresPassword: "postgres123"

# Primary instance configuration
primary:
  # Persistence với size lớn hơn cho safety
  persistence:
    enabled: true
    size: 20Gi
    accessModes:
      - ReadWriteOnce

  # Service configuration - port khác để tránh conflict
  service:
    type: ClusterIP
    ports:
      postgresql: 5433  # Port khác với 5432 của PostgreSQL 16.4

  # Resources tăng cường cho migration
  resources:
    limits:
      memory: "1Gi"
      cpu: "1"
    requests:
      memory: "512Mi"
      cpu: "500m"

  # Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001

  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false

  # Pod anti-affinity để tránh cùng node với PostgreSQL 16.4
  podAntiAffinityPreset: soft

  # Probes configuration
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6

# Metrics tắt để đơn giản
metrics:
  enabled: false

# Architecture
architecture: standalone

# Common labels cho migration tracking
commonLabels:
  app.kubernetes.io/part-of: "postgresql-migration"
  migration.version: "16-to-17-to-18"
  migration.phase: "step-1"

# Service account
serviceAccount:
  create: true
  automountServiceAccountToken: false

# RBAC
rbac:
  create: true

# Network Policy tắt để đơn giản migration
networkPolicy:
  enabled: false

# Volume Permissions
volumePermissions:
  enabled: false

# Shared memory volume
shmVolume:
  enabled: true
  sizeLimit: "1Gi"

# TLS tắt để đơn giản
tls:
  enabled: false

# Backup configuration tắt để tránh conflict
backup:
  enabled: false