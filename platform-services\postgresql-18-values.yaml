# PostgreSQL 18 Helm Values for Blue-Green Migration
# Đặt tên khác để tránh conflict với PostgreSQL 16.4
nameOverride: "postgresql-18"
fullnameOverride: "postgresql-18"

# PostgreSQL 18 image
image:
  registry: docker.io
  repository: bitnami/postgresql
  tag: "18.0.0-debian-12-r0"
  pullPolicy: IfNotPresent

# Authentication - giữ nguyên password để dễ migration
auth:
  enablePostgresUser: true
  postgresPassword: "postgres123"

# Primary instance configuration
primary:
  # Persistence với size lớn hơn cho safety
  persistence:
    enabled: true
    size: 20Gi
    accessModes:
      - ReadWriteOnce

  # Service configuration - port khác để tránh conflict
  service:
    type: ClusterIP
    ports:
      postgresql: 5433  # Port khác với 5432 của PostgreSQL 16.4

  # Resources tăng cường cho migration
  resources:
    limits:
      memory: "1Gi"
      cpu: "1"
    requests:
      memory: "512Mi"
      cpu: "500m"

  # Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001
    seccompProfile:
      type: RuntimeDefault

  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    capabilities:
      drop: ["ALL"]

  # Pod anti-affinity để tránh cùng node với PostgreSQL 16.4
  podAntiAffinityPreset: soft

  # Probes configuration
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6

# Metrics configuration - port khác để tránh conflict
metrics:
  enabled: true
  image:
    registry: docker.io
    repository: bitnami/postgres-exporter
    tag: 0.15.0-debian-12-r43

  service:
    ports:
      metrics: 9188  # Port khác với 9187 của PostgreSQL 16.4
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "9188"

  serviceMonitor:
    enabled: false
    interval: "30s"

  resources:
    limits:
      memory: "256Mi"
      cpu: "250m"
    requests:
      memory: "128Mi"
      cpu: "100m"

# Architecture
architecture: standalone

# Common labels cho migration tracking
commonLabels:
  app.kubernetes.io/part-of: "postgresql-migration"
  migration.version: "16-to-18"
  migration.phase: "blue-green"

# Service account
serviceAccount:
  create: true
  automountServiceAccountToken: false

# RBAC
rbac:
  create: true

# Network Policy tắt để đơn giản migration
networkPolicy:
  enabled: false

# Volume Permissions
volumePermissions:
  enabled: false

# Shared memory volume
shmVolume:
  enabled: true
  sizeLimit: "1Gi"

# TLS tắt để đơn giản
tls:
  enabled: false

# Backup configuration tắt để tránh conflict
backup:
  enabled: false