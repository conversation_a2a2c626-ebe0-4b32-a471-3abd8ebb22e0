# SonarQube Validation Scripts

This directory contains validation scripts for testing SonarQube deployment in the platform-services namespace.

## Overview

The validation scripts ensure that SonarQube is properly deployed and accessible with all required components working correctly.

## Scripts

### 1. `validate-sonarqube-deployment.sh` (<PERSON> Script)

Comprehensive validation script that runs all individual tests and provides a summary report.

**Usage:**
```bash
# Run all validation tests
./platform-services/scripts/validate-sonarqube-deployment.sh

# Show help
./platform-services/scripts/validate-sonarqube-deployment.sh --help

# Show troubleshooting information
./platform-services/scripts/validate-sonarqube-deployment.sh --info
```

**What it tests:**
- Database connectivity and configuration
- HTTP access via sonarqube.local domain
- Health checks and resource configuration
- Overall deployment status

### 2. `test-sonarqube-database.sh`

Tests database connectivity and configuration for SonarQube.

**Usage:**
```bash
./platform-services/scripts/test-sonarqube-database.sh
```

**What it tests:**
- PostgreSQL service availability
- Admin database connection
- SonarQube database existence
- SonarQube user existence and permissions
- Database connectivity from SonarQube pod
- Database secrets configuration

### 3. `test-sonarqube-http.sh`

Tests HTTP access to SonarQube via the sonarqube.local domain.

**Usage:**
```bash
./platform-services/scripts/test-sonarqube-http.sh
```

**What it tests:**
- DNS resolution for sonarqube.local
- Gateway and HTTPRoute configuration
- SonarQube service configuration
- Direct service connectivity
- HTTP access via domain
- SonarQube API endpoints
- Response headers and performance

### 4. `validate-sonarqube-health.sh`

Tests SonarQube health checks and resource configuration.

**Usage:**
```bash
./platform-services/scripts/validate-sonarqube-health.sh
```

**What it tests:**
- Pod status and readiness
- Resource requests and limits
- Liveness, readiness, and startup probes
- Health endpoint accessibility
- External access via Gateway
- Current resource usage

## Prerequisites

Before running the validation scripts, ensure you have:

1. **kubectl** - Kubernetes command-line tool
2. **curl** - HTTP client for testing web endpoints
3. **jq** - JSON processor for parsing kubectl output
4. **bc** - Calculator for performance measurements (optional)

**Installation on macOS:**
```bash
brew install kubectl curl jq bc
```

**Installation on Ubuntu:**
```bash
apt-get update
apt-get install kubectl curl jq bc
```

## Pre-validation Setup

1. **Ensure SonarQube is deployed:**
   ```bash
   kubectl get pods -n platform-services -l app.kubernetes.io/name=sonarqube
   ```

2. **Add sonarqube.local to hosts file:**
   ```bash
   ./platform-services/scripts/add-sonarqube-host.sh
   ```

3. **Verify ArgoCD sync status:**
   ```bash
   kubectl get applications sonarqube -n bootstrap
   ```

## Expected Results

### Successful Validation

When all tests pass, you should see:
```
🎉 ALL VALIDATION TESTS PASSED!
SonarQube deployment is fully functional and ready for use.

Next steps:
1. Access SonarQube at: http://sonarqube.local
2. Default credentials: admin/admin (change on first login)
3. Configure projects and quality gates as needed
```

### Failed Validation

If tests fail, the scripts will provide:
- Detailed error messages
- Troubleshooting steps
- Commands to investigate issues
- Links to relevant documentation

## Common Issues and Solutions

### 1. DNS Resolution Issues

**Problem:** `sonarqube.local` doesn't resolve to `*************`

**Solution:**
```bash
# Add to hosts file
./platform-services/scripts/add-sonarqube-host.sh

# Or manually add:
echo "************* sonarqube.local" | sudo tee -a /etc/hosts
```

### 2. Database Connection Issues

**Problem:** SonarQube cannot connect to PostgreSQL

**Solutions:**
```bash
# Check PostgreSQL status
kubectl get pods -n platform-services -l app.kubernetes.io/name=postgresql

# Check database initialization
kubectl get jobs -n platform-services

# Re-run database initialization
kubectl apply -f platform-services/platform/sonarqube-db-init.yaml

# Check database secrets
kubectl get secrets sonarqube-database -n platform-services
```

### 3. Pod Not Running

**Problem:** SonarQube pod is not in Running state

**Solutions:**
```bash
# Check pod status
kubectl get pods -n platform-services -l app.kubernetes.io/name=sonarqube

# Check pod logs
kubectl logs -l app.kubernetes.io/name=sonarqube -n platform-services

# Check pod events
kubectl describe pod -l app.kubernetes.io/name=sonarqube -n platform-services

# Check ArgoCD sync
kubectl get applications sonarqube -n bootstrap
```

### 4. Gateway/HTTPRoute Issues

**Problem:** Cannot access SonarQube via sonarqube.local

**Solutions:**
```bash
# Check Gateway status
kubectl get gateway traefik-gateway -n bootstrap

# Check HTTPRoute status
kubectl get httproute sonarqube-httproute -n platform-services

# Test direct service access
kubectl port-forward svc/sonarqube-sonarqube 9000:9000 -n platform-services
# Then access: http://localhost:9000
```

### 5. Resource Issues

**Problem:** SonarQube pod fails due to resource constraints

**Solutions:**
```bash
# Check node resources
kubectl describe nodes

# Check resource usage
kubectl top pods -n platform-services

# Adjust resource limits in values.yaml if needed
```

## Integration with CI/CD

These validation scripts can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Validate SonarQube Deployment
  run: |
    ./platform-services/scripts/validate-sonarqube-deployment.sh
```

```yaml
# Example ArgoCD PostSync Hook
apiVersion: batch/v1
kind: Job
metadata:
  name: sonarqube-validation
  annotations:
    argocd.argoproj.io/hook: PostSync
spec:
  template:
    spec:
      containers:
      - name: validator
        image: kubectl:latest
        command: ["/scripts/validate-sonarqube-deployment.sh"]
```

## Monitoring and Alerting

The validation scripts can be used for monitoring:

1. **Scheduled Health Checks:**
   ```bash
   # Add to crontab for regular validation
   0 */6 * * * /path/to/validate-sonarqube-deployment.sh
   ```

2. **Prometheus Integration:**
   - Export script results as metrics
   - Create alerts based on validation failures

3. **Log Aggregation:**
   - Send validation results to centralized logging
   - Create dashboards for deployment health

## Support

For issues with the validation scripts:

1. Check the troubleshooting section in each script
2. Review SonarQube and Kubernetes logs
3. Verify all prerequisites are installed
4. Ensure proper network connectivity
5. Check ArgoCD sync status and configuration

## Requirements Mapping

These validation scripts fulfill the following requirements:

- **Requirement 3.3:** HTTP access validation via sonarqube.local
- **Requirement 5.4:** Health checks and monitoring validation
- **Database connectivity:** Validates PostgreSQL integration
- **Gateway integration:** Validates HTTPRoute and Gateway configuration
- **Security:** Validates proper secret management and access controls