# HashiCorp Vault - Thông tin đăng nhập

## 🔐 Thông tin truy cập

**URL:** https://vault.ospgroup.io.vn

## 🔑 Unseal Keys (Cần 3/5 keys để unseal)

```
Unseal Key 1: grwUxhLc7Re0hnOOqZ74ZEaMWWrRPBGMwK8/TwKmGuGU
Unseal Key 2: hCUH/9jLqmFk8ai+7VG3/dweyJr9LcVjdBUxr858SZz3
Unseal Key 3: EOggWJTcU8iX+QhLHFllqCohWJJJVSjsZET1Z5AmDFpM
Unseal Key 4: 4PLI6GKnCFHM6LKGWZ0Xxvmi9MH+/+hkt49GTpQqhM61
Unseal Key 5: J+hM4QBcvELJL5wgGxwK8Yd6ARyj+uMtj8v0dOb+MxkM
```

## 🎫 Root Token

```
Initial Root Token: hvs.oeWnkBgXh12xqpXsyqB2kYC8
```

## 📋 Hướng dẫn sử dụng

### Đăng nhập vào Vault UI
1. Truy cập: https://vault.ospgroup.io.vn
2. Chọn phương thức đăng nhập: **Token**
3. Nhập Root Token: `hvs.oeWnkBgXh12xqpXsyqB2kYC8`
4. Click **Sign In**

### Unseal Vault (nếu cần thiết)
Nếu Vault bị sealed, sử dụng 3 trong 5 unseal keys:

```bash
kubectl exec vault-0 -n platform-services -- vault operator unseal grwUxhLc7Re0hnOOqZ74ZEaMWWrRPBGMwK8/TwKmGuGU
kubectl exec vault-0 -n platform-services -- vault operator unseal hCUH/9jLqmFk8ai+7VG3/dweyJr9LcVjdBUxr858SZz3
kubectl exec vault-0 -n platform-services -- vault operator unseal EOggWJTcU8iX+QhLHFllqCohWJJJVSjsZET1Z5AmDFpM
```

### Kiểm tra trạng thái Vault

```bash
# Kiểm tra health status
curl https://vault.ospgroup.io.vn/v1/sys/health

# Kiểm tra seal status
kubectl exec vault-0 -n platform-services -- vault status
```

## ⚠️ Lưu ý bảo mật

1. **Lưu trữ an toàn**: Unseal keys và root token phải được lưu trữ ở nơi an toàn
2. **Phân phối keys**: Nên phân phối 5 unseal keys cho 5 người khác nhau
3. **Rotate root token**: Nên tạo admin token mới và revoke root token sau khi setup xong
4. **Backup**: Thường xuyên backup dữ liệu Vault

## 🔧 Thông tin kỹ thuật

- **Namespace**: platform-services
- **Pod**: vault-0
- **Service**: vault (ClusterIP)
- **Storage**: PersistentVolume (local-path)
- **Mode**: Standalone (không HA)
- **Version**: Vault v1.15.2

## 📊 Monitoring

```bash
# Kiểm tra pod status
kubectl get pods -n platform-services -l app.kubernetes.io/name=vault

# Xem logs
kubectl logs vault-0 -n platform-services

# Port forward để test local
kubectl port-forward vault-0 8200:8200 -n platform-services
```
