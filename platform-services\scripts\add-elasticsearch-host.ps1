# Script để thêm elasticsearch.local vào file hosts
# Chạy script này vớ<PERSON> quyền Administrator

# Đ<PERSON><PERSON>ng dẫn file hosts
$hostsFile = "C:\Windows\System32\drivers\etc\hosts"

# IP của Traefik Gateway
$gatewayIP = "*************"

# Nội dung cần thêm
$hostsEntry = "$gatewayIP    elasticsearch.local"

# Kiểm tra xem entry đã tồn tại chưa
$content = Get-Content $hostsFile
if ($content -contains $hostsEntry) {
    Write-Host "elasticsearch.local đã tồn tại trong file hosts" -ForegroundColor Yellow
} else {
    # Thêm entry mới
    Add-Content -Path $hostsFile -Value $hostsEntry
    Write-Host "Đã thêm elasticsearch.local vào file hosts" -ForegroundColor Green
}

# Hiển thị nội dung file hosts
Write-Host "`nNội dung file hosts hiện tại:" -ForegroundColor Cyan
Get-Content $hostsFile | Where-Object { $_ -match "\.local" }
