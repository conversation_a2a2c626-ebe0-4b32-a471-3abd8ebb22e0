#!/bin/bash
# Script để thêm elasticsearch.local vào file hosts
# Chạy script này với quyền sudo

# Đường dẫn file hosts
HOSTS_FILE="/etc/hosts"

# IP của Traefik Gateway
GATEWAY_IP="*************"

# Nội dung cần thêm
HOSTS_ENTRY="$GATEWAY_IP    elasticsearch.local"

# Kiểm tra xem entry đã tồn tại chưa
if grep -q "elasticsearch.local" "$HOSTS_FILE"; then
    echo "elasticsearch.local đã tồn tại trong file hosts"
else
    # Thêm entry mới
    echo "$HOSTS_ENTRY" | sudo tee -a "$HOSTS_FILE" > /dev/null
    echo "Đã thêm elasticsearch.local vào file hosts"
fi

# Hiển thị nội dung file hosts
echo -e "\nNội dung file hosts hiện tại:"
grep "\.local" "$HOSTS_FILE" || echo "Không tìm thấy entry .local nào"
