#!/bin/bash

# Sc<PERSON>t để thêm keycloak.local vào file hosts
# Chạy script này với quyền sudo

# Đường dẫn file hosts
HOSTS_FILE="/etc/hosts"

# IP của Traefik Gateway
GATEWAY_IP="*************"

# Nội dung cần thêm
HOSTS_ENTRY="$GATEWAY_IP    keycloak.local"

# Kiểm tra quyền root
if [ "$EUID" -ne 0 ]; then
    echo "Vui lòng chạy script này với quyền sudo"
    echo "sudo $0"
    exit 1
fi

# Kiểm tra xem entry đã tồn tại chưa
if grep -q "keycloak.local" "$HOSTS_FILE"; then
    echo "keycloak.local đã tồn tại trong file hosts"
    echo "Nội dung hiện tại:"
    grep "keycloak.local" "$HOSTS_FILE"
else
    # Thêm entry mới
    echo "$HOSTS_ENTRY" >> "$HOSTS_FILE"
    echo "Đã thêm keycloak.local vào file hosts"
fi

# Hiển thị nội dung file hosts liên quan đến keycloak
echo ""
echo "Nội dung file hosts liên quan đến keycloak:"
grep "keycloak" "$HOSTS_FILE"
