-- Script khởi tạo database và user cho Label Studio
-- Tạo user labelstudio với mật khẩu mạnh
CREATE USER labelstudio WITH PASSWORD 'LS_2025_SecurePass_9x7!';

-- Tạo database labelstudio
CREATE DATABASE labelstudio OWNER labelstudio;

-- <PERSON><PERSON><PERSON> quyền cho user labelstudio
GRANT ALL PRIVILEGES ON DATABASE labelstudio TO labelstudio;

-- Kết nối đến database labelstudio để cấp quyền schema
\c labelstudio;

-- Cấp quyền trên schema public
GRANT ALL ON SCHEMA public TO labelstudio;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO labelstudio;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO labelstudio;

-- <PERSON><PERSON><PERSON> quyền mặc định cho các table và sequence sẽ được tạo trong tương lai
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO labelstudio;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO labelstudio;

-- <PERSON><PERSON><PERSON> thị thông tin xác nhận
SELECT 'Label Studio database and user created successfully!' as status;
