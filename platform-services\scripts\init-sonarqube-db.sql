-- SonarQube Database Initialization Script
-- This script creates the sonarqube database and user with proper permissions
-- Requirements: 2.1, 2.2, 2.3

-- Create the sonarqube database
CREATE DATABASE sonarqube
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.utf8'
    LC_CTYPE = 'en_US.utf8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create the sonarqube user
CREATE USER sonarqube WITH PASSWORD '9UYfpwEupFFU3k6UyafM';

-- Grant all privileges on the sonarqube database to the sonarqube user
GRANT ALL PRIVILEGES ON DATABASE sonarqube TO sonarqube;

-- Connect to the sonarqube database to set additional permissions
\c sonarqube;

-- Grant schema permissions to sonarqube user
GRANT ALL ON SCHEMA public TO sonarqube;

-- Grant default privileges for future tables and sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO sonarqube;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO sonarqube;

-- Ensure sonarqube user can create tables and other objects
GRANT CREATE ON SCHEMA public TO sonarqube;

-- Display confirmation
SELECT 'SonarQube database and user created successfully' AS status;