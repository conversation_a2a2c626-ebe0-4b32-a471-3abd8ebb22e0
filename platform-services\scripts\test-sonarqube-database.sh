#!/bin/bash

# SonarQube Database Connectivity Test Script
# This script tests database connectivity for SonarQube deployment

set -e

NAMESPACE="platform-services"
DB_HOST="postgresql.platform-services.svc.cluster.local"
DB_PORT="5432"
DB_NAME="sonarqube"
DB_USER="sonarqube"
ADMIN_USER="postgres"

echo "=== SonarQube Database Connectivity Test ==="
echo "Namespace: $NAMESPACE"
echo "Database Host: $DB_HOST"
echo "Database Port: $DB_PORT"
echo "Database Name: $DB_NAME"
echo "Database User: $DB_USER"
echo

# Function to test PostgreSQL service availability
test_postgresql_service() {
    echo "1. Testing PostgreSQL service availability..."
    
    # Check if PostgreSQL service exists
    if kubectl get svc postgresql -n $NAMESPACE > /dev/null 2>&1; then
        echo "✅ PostgreSQL service exists"
    else
        echo "❌ PostgreSQL service not found"
        return 1
    fi
    
    # Check if PostgreSQL pods are running
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -z "$POSTGRES_POD" ]; then
        echo "❌ No PostgreSQL pod found"
        return 1
    fi
    
    POD_STATUS=$(kubectl get pod $POSTGRES_POD -n $NAMESPACE -o jsonpath='{.status.phase}')
    echo "PostgreSQL Pod Status: $POD_STATUS"
    
    if [ "$POD_STATUS" != "Running" ]; then
        echo "❌ PostgreSQL pod is not running"
        return 1
    fi
    
    echo "✅ PostgreSQL service is available and running"
    return 0
}

# Function to test database connection with admin credentials
test_admin_connection() {
    echo
    echo "2. Testing admin database connection..."
    
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    
    # Test connection to PostgreSQL with admin credentials
    if kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d postgres -c "SELECT version();" > /dev/null 2>&1; then
        echo "✅ Admin connection to PostgreSQL successful"
    else
        echo "❌ Admin connection to PostgreSQL failed"
        return 1
    fi
}

# Function to check if SonarQube database exists
check_sonarqube_database() {
    echo
    echo "3. Checking SonarQube database existence..."
    
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    
    # Check if sonarqube database exists
    DB_EXISTS=$(kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" 2>/dev/null | tr -d '\r\n' || echo "")
    
    if [ "$DB_EXISTS" = "1" ]; then
        echo "✅ SonarQube database '$DB_NAME' exists"
    else
        echo "❌ SonarQube database '$DB_NAME' does not exist"
        echo "Run the database initialization script: kubectl apply -f platform-services/platform/sonarqube-db-init.yaml"
        return 1
    fi
}

# Function to check if SonarQube user exists
check_sonarqube_user() {
    echo
    echo "4. Checking SonarQube user existence..."
    
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    
    # Check if sonarqube user exists
    USER_EXISTS=$(kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d postgres -tAc "SELECT 1 FROM pg_user WHERE usename='$DB_USER';" 2>/dev/null | tr -d '\r\n' || echo "")
    
    if [ "$USER_EXISTS" = "1" ]; then
        echo "✅ SonarQube user '$DB_USER' exists"
    else
        echo "❌ SonarQube user '$DB_USER' does not exist"
        echo "Run the database initialization script: kubectl apply -f platform-services/platform/sonarqube-db-init.yaml"
        return 1
    fi
}

# Function to test SonarQube user connection
test_sonarqube_user_connection() {
    echo
    echo "5. Testing SonarQube user database connection..."
    
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    
    # Get SonarQube database password from secret
    if kubectl get secret sonarqube-database -n $NAMESPACE > /dev/null 2>&1; then
        echo "✅ SonarQube database secret exists"
        
        # Test connection with SonarQube user credentials
        # Note: We'll test this by checking if the user can connect to the database
        CONNECTION_TEST=$(kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d $DB_NAME -tAc "SELECT current_database();" 2>/dev/null | tr -d '\r\n' || echo "")
        
        if [ "$CONNECTION_TEST" = "$DB_NAME" ]; then
            echo "✅ Connection to SonarQube database successful"
        else
            echo "❌ Connection to SonarQube database failed"
            return 1
        fi
    else
        echo "❌ SonarQube database secret not found"
        echo "Create the secret: kubectl apply -f platform-services/platform/sonarqube-secrets.yaml"
        return 1
    fi
}

# Function to check database permissions
check_database_permissions() {
    echo
    echo "6. Checking SonarQube user database permissions..."
    
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    
    # Check if SonarQube user has proper permissions on the database
    PERMISSIONS=$(kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d postgres -tAc "SELECT string_agg(privilege_type, ',') FROM information_schema.role_table_grants WHERE grantee='$DB_USER' AND table_schema='public';" 2>/dev/null | tr -d '\r\n' || echo "")
    
    echo "SonarQube user permissions: $PERMISSIONS"
    
    # Check if user is owner of the database
    DB_OWNER=$(kubectl exec -it $POSTGRES_POD -n $NAMESPACE -- psql -U $ADMIN_USER -d postgres -tAc "SELECT pg_catalog.pg_get_userbyid(d.datdba) FROM pg_catalog.pg_database d WHERE d.datname = '$DB_NAME';" 2>/dev/null | tr -d '\r\n' || echo "")
    
    if [ "$DB_OWNER" = "$DB_USER" ]; then
        echo "✅ SonarQube user is owner of the database"
    else
        echo "❌ SonarQube user is not owner of the database (owner: $DB_OWNER)"
        return 1
    fi
}

# Function to test database connectivity from SonarQube pod
test_connectivity_from_sonarqube() {
    echo
    echo "7. Testing database connectivity from SonarQube pod..."
    
    # Check if SonarQube pod exists
    SONARQUBE_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -z "$SONARQUBE_POD" ]; then
        echo "⚠️  No SonarQube pod found - skipping connectivity test from SonarQube pod"
        return 0
    fi
    
    POD_STATUS=$(kubectl get pod $SONARQUBE_POD -n $NAMESPACE -o jsonpath='{.status.phase}')
    
    if [ "$POD_STATUS" != "Running" ]; then
        echo "⚠️  SonarQube pod is not running - skipping connectivity test"
        return 0
    fi
    
    # Test network connectivity to PostgreSQL from SonarQube pod
    if kubectl exec $SONARQUBE_POD -n $NAMESPACE -- nc -z $DB_HOST $DB_PORT > /dev/null 2>&1; then
        echo "✅ Network connectivity from SonarQube pod to PostgreSQL successful"
    else
        echo "❌ Network connectivity from SonarQube pod to PostgreSQL failed"
        return 1
    fi
    
    # Check SonarQube logs for database connection issues
    echo "Checking SonarQube logs for database connection status..."
    DB_LOG_CHECK=$(kubectl logs $SONARQUBE_POD -n $NAMESPACE --tail=50 | grep -i "database\|jdbc\|postgresql" | tail -3 || echo "No database-related logs found")
    echo "Recent database-related logs:"
    echo "$DB_LOG_CHECK"
}

# Main execution
main() {
    echo "Starting SonarQube database connectivity test..."
    echo
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Run all tests
    test_postgresql_service || exit 1
    test_admin_connection || exit 1
    check_sonarqube_database || exit 1
    check_sonarqube_user || exit 1
    test_sonarqube_user_connection || exit 1
    check_database_permissions || exit 1
    test_connectivity_from_sonarqube
    
    echo
    echo "🎉 All database connectivity tests passed successfully!"
    echo "SonarQube database is properly configured and accessible."
}

# Run main function
main "$@"