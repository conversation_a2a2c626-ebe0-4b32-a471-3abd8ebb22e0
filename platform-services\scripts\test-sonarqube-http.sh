#!/bin/bash

# SonarQube HTTP Access Test Script
# This script tests HTTP access to SonarQube via sonarqube.local domain

set -e

NAMESPACE="platform-services"
SERVICE_NAME="sonarqube-sonarqube"
SONARQUBE_URL="http://sonarqube.local"
GATEWAY_IP="*************"

echo "=== SonarQube HTTP Access Test ==="
echo "Namespace: $NAMESPACE"
echo "Service: $SERVICE_NAME"
echo "URL: $SONARQUBE_URL"
echo "Gateway IP: $GATEWAY_IP"
echo

# Function to check DNS resolution
check_dns_resolution() {
    echo "1. Checking DNS resolution for sonarqube.local..."
    
    # Check if sonarqube.local resolves to the gateway IP
    RESOLVED_IP=$(nslookup sonarqube.local 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}' || echo "")
    
    if [ "$RESOLVED_IP" = "$GATEWAY_IP" ]; then
        echo "✅ sonarqube.local resolves to $GATEWAY_IP"
    else
        echo "❌ sonarqube.local does not resolve to expected IP"
        echo "Expected: $GATEWAY_IP"
        echo "Actual: $RESOLVED_IP"
        echo
        echo "To fix this, add the following line to your /etc/hosts file:"
        echo "$GATEWAY_IP sonarqube.local"
        echo
        echo "Or run: platform-services/scripts/add-sonarqube-host.sh"
        return 1
    fi
}

# Function to check Gateway and HTTPRoute configuration
check_gateway_configuration() {
    echo
    echo "2. Checking Gateway and HTTPRoute configuration..."
    
    # Check if Traefik Gateway exists
    if kubectl get gateway traefik-gateway -n bootstrap > /dev/null 2>&1; then
        echo "✅ Traefik Gateway exists"
        
        # Check Gateway status
        GATEWAY_STATUS=$(kubectl get gateway traefik-gateway -n bootstrap -o jsonpath='{.status.conditions[0].status}' 2>/dev/null || echo "Unknown")
        echo "Gateway Status: $GATEWAY_STATUS"
        
        if [ "$GATEWAY_STATUS" = "True" ]; then
            echo "✅ Gateway is ready"
        else
            echo "❌ Gateway is not ready"
            kubectl describe gateway traefik-gateway -n bootstrap
            return 1
        fi
    else
        echo "❌ Traefik Gateway not found"
        return 1
    fi
    
    # Check if SonarQube HTTPRoute exists
    if kubectl get httproute sonarqube-httproute -n $NAMESPACE > /dev/null 2>&1; then
        echo "✅ SonarQube HTTPRoute exists"
        
        # Check HTTPRoute configuration
        HOSTNAMES=$(kubectl get httproute sonarqube-httproute -n $NAMESPACE -o jsonpath='{.spec.hostnames[*]}')
        BACKEND_SERVICE=$(kubectl get httproute sonarqube-httproute -n $NAMESPACE -o jsonpath='{.spec.rules[0].backendRefs[0].name}')
        BACKEND_PORT=$(kubectl get httproute sonarqube-httproute -n $NAMESPACE -o jsonpath='{.spec.rules[0].backendRefs[0].port}')
        
        echo "HTTPRoute Hostnames: $HOSTNAMES"
        echo "Backend Service: $BACKEND_SERVICE"
        echo "Backend Port: $BACKEND_PORT"
        
        if [[ "$HOSTNAMES" == *"sonarqube.local"* ]]; then
            echo "✅ HTTPRoute is configured for sonarqube.local"
        else
            echo "❌ HTTPRoute is not configured for sonarqube.local"
            return 1
        fi
        
        if [ "$BACKEND_SERVICE" = "$SERVICE_NAME" ]; then
            echo "✅ HTTPRoute points to correct service"
        else
            echo "❌ HTTPRoute points to wrong service (expected: $SERVICE_NAME, actual: $BACKEND_SERVICE)"
            return 1
        fi
        
    else
        echo "❌ SonarQube HTTPRoute not found"
        return 1
    fi
}

# Function to check SonarQube service
check_sonarqube_service() {
    echo
    echo "3. Checking SonarQube service..."
    
    # Check if SonarQube service exists
    if kubectl get svc $SERVICE_NAME -n $NAMESPACE > /dev/null 2>&1; then
        echo "✅ SonarQube service exists"
        
        # Get service details
        SERVICE_TYPE=$(kubectl get svc $SERVICE_NAME -n $NAMESPACE -o jsonpath='{.spec.type}')
        SERVICE_PORT=$(kubectl get svc $SERVICE_NAME -n $NAMESPACE -o jsonpath='{.spec.ports[0].port}')
        TARGET_PORT=$(kubectl get svc $SERVICE_NAME -n $NAMESPACE -o jsonpath='{.spec.ports[0].targetPort}')
        
        echo "Service Type: $SERVICE_TYPE"
        echo "Service Port: $SERVICE_PORT"
        echo "Target Port: $TARGET_PORT"
        
        if [ "$SERVICE_TYPE" = "ClusterIP" ]; then
            echo "✅ Service type is ClusterIP (internal only)"
        else
            echo "⚠️  Service type is not ClusterIP: $SERVICE_TYPE"
        fi
        
        if [ "$SERVICE_PORT" = "9000" ]; then
            echo "✅ Service port is correct (9000)"
        else
            echo "❌ Service port is incorrect (expected: 9000, actual: $SERVICE_PORT)"
            return 1
        fi
        
    else
        echo "❌ SonarQube service not found"
        return 1
    fi
}

# Function to test direct service connectivity
test_direct_service_connectivity() {
    echo
    echo "4. Testing direct service connectivity..."
    
    # Port forward to test service directly
    echo "Setting up port forward to test service directly..."
    kubectl port-forward svc/$SERVICE_NAME 9001:9000 -n $NAMESPACE &
    PF_PID=$!
    
    # Wait for port forward to be ready
    sleep 5
    
    # Test direct connection to service
    if curl -f -s -I http://localhost:9001/ > /dev/null 2>&1; then
        echo "✅ Direct service connectivity successful"
    else
        echo "❌ Direct service connectivity failed"
        kill $PF_PID 2>/dev/null || true
        return 1
    fi
    
    # Clean up port forward
    kill $PF_PID 2>/dev/null || true
}

# Function to test HTTP access via domain
test_http_access() {
    echo
    echo "5. Testing HTTP access via sonarqube.local..."
    
    # Test basic HTTP connectivity
    echo "Testing basic HTTP connectivity..."
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $SONARQUBE_URL/ 2>/dev/null || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ HTTP access successful (Status: $HTTP_STATUS)"
    else
        echo "❌ HTTP access failed (Status: $HTTP_STATUS)"
        
        # Try to get more details about the failure
        echo "Attempting to get more details..."
        curl -v $SONARQUBE_URL/ 2>&1 | head -20 || true
        return 1
    fi
    
    # Test if we get SonarQube content
    echo "Testing SonarQube content..."
    CONTENT_CHECK=$(curl -s $SONARQUBE_URL/ | grep -i "sonarqube\|login" | head -1 || echo "")
    
    if [ -n "$CONTENT_CHECK" ]; then
        echo "✅ SonarQube content detected"
        echo "Content sample: $CONTENT_CHECK"
    else
        echo "❌ SonarQube content not detected"
        return 1
    fi
}

# Function to test SonarQube API endpoints
test_api_endpoints() {
    echo
    echo "6. Testing SonarQube API endpoints..."
    
    # Test system status API
    echo "Testing /api/system/status endpoint..."
    STATUS_RESPONSE=$(curl -s $SONARQUBE_URL/api/system/status 2>/dev/null || echo "")
    
    if echo "$STATUS_RESPONSE" | grep -q "status"; then
        echo "✅ System status API accessible"
        echo "Status response: $STATUS_RESPONSE"
    else
        echo "❌ System status API not accessible"
        return 1
    fi
    
    # Test system info API (may require authentication)
    echo "Testing /api/system/info endpoint..."
    INFO_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $SONARQUBE_URL/api/system/info 2>/dev/null || echo "000")
    
    if [ "$INFO_STATUS" = "200" ] || [ "$INFO_STATUS" = "401" ]; then
        echo "✅ System info API endpoint exists (Status: $INFO_STATUS)"
        if [ "$INFO_STATUS" = "401" ]; then
            echo "Note: Authentication required for system info (expected)"
        fi
    else
        echo "❌ System info API endpoint not accessible (Status: $INFO_STATUS)"
        return 1
    fi
}

# Function to test response headers
test_response_headers() {
    echo
    echo "7. Testing response headers..."
    
    # Get response headers
    HEADERS=$(curl -s -I $SONARQUBE_URL/ 2>/dev/null || echo "")
    
    echo "Response headers:"
    echo "$HEADERS"
    
    # Check for SonarQube-specific headers
    if echo "$HEADERS" | grep -qi "server.*sonarqube\|x-sonar"; then
        echo "✅ SonarQube-specific headers detected"
    else
        echo "⚠️  No SonarQube-specific headers detected (may be normal)"
    fi
    
    # Check content type
    if echo "$HEADERS" | grep -qi "content-type.*text/html"; then
        echo "✅ Correct content type for web interface"
    else
        echo "⚠️  Unexpected content type"
    fi
}

# Function to test performance
test_performance() {
    echo
    echo "8. Testing response performance..."
    
    # Measure response time
    RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" $SONARQUBE_URL/ 2>/dev/null || echo "0")
    
    echo "Response time: ${RESPONSE_TIME}s"
    
    # Check if response time is reasonable (less than 10 seconds)
    if (( $(echo "$RESPONSE_TIME < 10" | bc -l) )); then
        echo "✅ Response time is acceptable"
    else
        echo "⚠️  Response time is slow (>10s)"
    fi
}

# Main execution
main() {
    echo "Starting SonarQube HTTP access test..."
    echo
    
    # Check if required tools are available
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Run all tests
    check_dns_resolution || exit 1
    check_gateway_configuration || exit 1
    check_sonarqube_service || exit 1
    test_direct_service_connectivity || exit 1
    test_http_access || exit 1
    test_api_endpoints || exit 1
    test_response_headers
    test_performance
    
    echo
    echo "🎉 All HTTP access tests passed successfully!"
    echo "SonarQube is accessible via $SONARQUBE_URL"
}

# Run main function
main "$@"