#!/bin/bash

# Test HashiCorp Vault deployment
echo "🔍 Testing HashiCorp Vault deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Check pod status
echo -e "\n${YELLOW}1. Checking pod status...${NC}"
kubectl get pods -n platform-services -l app.kubernetes.io/name=vault

# Test 2: Check service
echo -e "\n${YELLOW}2. Checking service...${NC}"
kubectl get service vault -n platform-services

# Test 3: Check HTTPRoute
echo -e "\n${YELLOW}3. Checking HTTPRoute...${NC}"
kubectl get httproute vault-httproute -n platform-services

# Test 4: Test internal connectivity
echo -e "\n${YELLOW}4. Testing internal connectivity...${NC}"
kubectl exec vault-0 -n platform-services -- vault status

# Test 5: Test external connectivity
echo -e "\n${YELLOW}5. Testing external connectivity...${NC}"
echo "Testing https://vault.ospgroup.io.vn/v1/sys/health"
curl -s https://vault.ospgroup.io.vn/v1/sys/health | jq .

# Test 6: Check if Vault is unsealed
echo -e "\n${YELLOW}6. Checking Vault seal status...${NC}"
SEAL_STATUS=$(curl -s https://vault.ospgroup.io.vn/v1/sys/health | jq -r '.sealed')
if [ "$SEAL_STATUS" = "false" ]; then
    echo -e "${GREEN}✅ Vault is unsealed and ready${NC}"
else
    echo -e "${RED}❌ Vault is sealed${NC}"
fi

# Test 7: Test UI accessibility
echo -e "\n${YELLOW}7. Testing UI accessibility...${NC}"
UI_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://vault.ospgroup.io.vn/ui/)
if [ "$UI_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ Vault UI is accessible at https://vault.ospgroup.io.vn${NC}"
else
    echo -e "${RED}❌ Vault UI is not accessible (HTTP $UI_STATUS)${NC}"
fi

echo -e "\n${GREEN}🎉 Vault testing completed!${NC}"
echo -e "\n${YELLOW}📋 Summary:${NC}"
echo "- Vault URL: https://vault.ospgroup.io.vn"
echo "- UI URL: https://vault.ospgroup.io.vn/ui/"
echo "- Login method: Token"
echo "- Root token: hvs.oeWnkBgXh12xqpXsyqB2kYC8"
echo ""
echo -e "${YELLOW}⚠️  Remember to:${NC}"
echo "1. Store unseal keys securely"
echo "2. Create admin policies and tokens"
echo "3. Revoke root token after setup"
echo "4. Setup regular backups"
