#!/bin/bash

# Script validation cho Label Studio deployment
# Kiểm tra tất cả các thành phần của Label Studio deployment

set -e

echo "🚀 Bắt đầu validation Label Studio deployment..."

# Set KUBECONFIG
export KUBECONFIG=$(pwd)/.kube/config

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# 1. Kiểm tra namespace
echo "1. Kiểm tra namespace platform-services..."
kubectl get namespace platform-services > /dev/null 2>&1
print_status $? "Namespace platform-services tồn tại"

# 2. <PERSON><PERSON><PERSON> tra secrets
echo "2. Kiểm tra secrets..."
kubectl get secret labelstudio-db-secret -n platform-services > /dev/null 2>&1
print_status $? "Secret labelstudio-db-secret tồn tại"

# 3. Kiểm tra database initialization job
echo "3. Kiểm tra database initialization..."
JOB_STATUS=$(kubectl get job labelstudio-db-init -n platform-services -o jsonpath='{.status.conditions[0].type}' 2>/dev/null || echo "NotFound")
if [ "$JOB_STATUS" = "Complete" ]; then
    print_status 0 "Database initialization job hoàn thành"
elif [ "$JOB_STATUS" = "NotFound" ]; then
    print_warning "Database initialization job chưa được tạo"
else
    print_warning "Database initialization job đang chạy hoặc failed: $JOB_STATUS"
fi

# 4. Kiểm tra ArgoCD application
echo "4. Kiểm tra ArgoCD application..."
kubectl get application labelstudio -n bootstrap > /dev/null 2>&1
print_status $? "ArgoCD application labelstudio tồn tại"

# Kiểm tra sync status
SYNC_STATUS=$(kubectl get application labelstudio -n bootstrap -o jsonpath='{.status.sync.status}' 2>/dev/null || echo "Unknown")
if [ "$SYNC_STATUS" = "Synced" ]; then
    print_status 0 "ArgoCD application đã sync thành công"
else
    print_warning "ArgoCD application sync status: $SYNC_STATUS"
fi

# 5. Kiểm tra Label Studio pods
echo "5. Kiểm tra Label Studio pods..."
PODS_READY=$(kubectl get pods -n platform-services -l app.kubernetes.io/name=label-studio --no-headers 2>/dev/null | wc -l)
if [ "$PODS_READY" -gt 0 ]; then
    print_status 0 "Label Studio pods đã được tạo ($PODS_READY pods)"
    
    # Kiểm tra pod status
    kubectl get pods -n platform-services -l app.kubernetes.io/name=label-studio --no-headers | while read line; do
        POD_NAME=$(echo $line | awk '{print $1}')
        POD_STATUS=$(echo $line | awk '{print $3}')
        if [ "$POD_STATUS" = "Running" ]; then
            print_status 0 "Pod $POD_NAME đang chạy"
        else
            print_warning "Pod $POD_NAME status: $POD_STATUS"
        fi
    done
else
    print_warning "Không tìm thấy Label Studio pods"
fi

# 6. Kiểm tra services
echo "6. Kiểm tra services..."
kubectl get svc -n platform-services -l app.kubernetes.io/name=label-studio > /dev/null 2>&1
print_status $? "Label Studio service tồn tại"

# 7. Kiểm tra middleware
echo "7. Kiểm tra Traefik middleware..."
kubectl get middleware labelstudio-stripprefix -n platform-services > /dev/null 2>&1
print_status $? "Traefik middleware labelstudio-stripprefix tồn tại"

# 8. Kiểm tra HTTPRoute
echo "8. Kiểm tra Gateway API HTTPRoute..."
kubectl get httproute labelstudio-common-route -n platform-services > /dev/null 2>&1
print_status $? "HTTPRoute labelstudio-common-route tồn tại"

# 9. Test database connection (nếu có pods running)
echo "9. Test database connection..."
RUNNING_POD=$(kubectl get pods -n platform-services -l app.kubernetes.io/name=label-studio --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$RUNNING_POD" ]; then
    print_info "Testing database connection từ pod $RUNNING_POD..."
    DB_TEST=$(kubectl exec $RUNNING_POD -n platform-services -- python -c "
import psycopg2
import os
try:
    conn = psycopg2.connect(
        host='postgresql.platform-services.svc.cluster.local',
        database='labelstudio',
        user='labelstudio',
        password='LS_2025_SecurePass_9x7!'
    )
    print('SUCCESS')
    conn.close()
except Exception as e:
    print(f'ERROR: {e}')
" 2>/dev/null || echo "ERROR")
    
    if [[ "$DB_TEST" == *"SUCCESS"* ]]; then
        print_status 0 "Database connection test thành công"
    else
        print_warning "Database connection test failed: $DB_TEST"
    fi
else
    print_warning "Không có pod nào đang chạy để test database connection"
fi

# 10. Test health endpoint (nếu có pods running)
echo "10. Test health endpoint..."
if [ -n "$RUNNING_POD" ]; then
    HEALTH_CHECK=$(kubectl exec $RUNNING_POD -n platform-services -- curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health 2>/dev/null || echo "000")
    if [ "$HEALTH_CHECK" = "200" ]; then
        print_status 0 "Health endpoint trả về 200 OK"
    else
        print_warning "Health endpoint trả về: $HEALTH_CHECK"
    fi
else
    print_warning "Không có pod nào đang chạy để test health endpoint"
fi

# 11. Test external access
echo "11. Test external access..."
print_info "Testing https://common.ospgroup.vn/label-studio/health..."
EXTERNAL_TEST=$(curl -k -s -o /dev/null -w "%{http_code}" https://common.ospgroup.vn/label-studio/health 2>/dev/null || echo "000")
if [ "$EXTERNAL_TEST" = "200" ]; then
    print_status 0 "External access test thành công"
else
    print_warning "External access test failed. HTTP code: $EXTERNAL_TEST"
    print_info "Có thể cần thời gian để DNS và routing được cập nhật"
fi

echo ""
echo "🏁 Validation hoàn thành!"
echo ""
echo "📋 Tóm tắt:"
echo "- Label Studio URL: https://common.ospgroup.vn/label-studio/"
echo "- Database: <EMAIL>:5432"
echo "- Redis: redis-master.platform-services.svc.cluster.local:6379/2"
echo ""
echo "📚 Để xem logs chi tiết:"
echo "kubectl logs -l app.kubernetes.io/name=label-studio -n platform-services"
echo ""
echo "🔧 Để troubleshoot:"
echo "kubectl describe pods -l app.kubernetes.io/name=label-studio -n platform-services"
