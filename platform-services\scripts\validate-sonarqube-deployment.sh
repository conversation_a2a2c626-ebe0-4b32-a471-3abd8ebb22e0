#!/bin/bash

# SonarQube Deployment Validation Script
# This script runs comprehensive validation tests for SonarQube deployment
# It combines database connectivity, HTTP access, and health check tests

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NAMESPACE="platform-services"

echo "=== SonarQube Deployment Validation Suite ==="
echo "Script Directory: $SCRIPT_DIR"
echo "Namespace: $NAMESPACE"
echo "Timestamp: $(date)"
echo

# Function to run a validation script and capture results
run_validation_script() {
    local script_name="$1"
    local script_path="$SCRIPT_DIR/$script_name"
    local description="$2"
    
    echo "=========================================="
    echo "Running: $description"
    echo "Script: $script_name"
    echo "=========================================="
    
    if [ -f "$script_path" ] && [ -x "$script_path" ]; then
        if "$script_path"; then
            echo "✅ $description - PASSED"
            return 0
        else
            echo "❌ $description - FAILED"
            return 1
        fi
    else
        echo "❌ Script not found or not executable: $script_path"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    echo "Checking prerequisites..."
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        echo "❌ jq is not installed or not in PATH"
        echo "Install jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
        exit 1
    fi
    
    # Check if bc is available (for performance calculations)
    if ! command -v bc &> /dev/null; then
        echo "⚠️  bc is not installed - performance calculations may not work"
        echo "Install bc: brew install bc (macOS) or apt-get install bc (Ubuntu)"
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace $NAMESPACE > /dev/null 2>&1; then
        echo "❌ Namespace '$NAMESPACE' does not exist"
        exit 1
    fi
    
    echo "✅ Prerequisites check passed"
    echo
}

# Function to display summary
display_summary() {
    local database_result="$1"
    local http_result="$2"
    local health_result="$3"
    
    echo
    echo "=========================================="
    echo "VALIDATION SUMMARY"
    echo "=========================================="
    
    if [ "$database_result" -eq 0 ]; then
        echo "✅ Database Connectivity Test: PASSED"
    else
        echo "❌ Database Connectivity Test: FAILED"
    fi
    
    if [ "$http_result" -eq 0 ]; then
        echo "✅ HTTP Access Test: PASSED"
    else
        echo "❌ HTTP Access Test: FAILED"
    fi
    
    if [ "$health_result" -eq 0 ]; then
        echo "✅ Health Check Test: PASSED"
    else
        echo "❌ Health Check Test: FAILED"
    fi
    
    echo
    
    if [ "$database_result" -eq 0 ] && [ "$http_result" -eq 0 ] && [ "$health_result" -eq 0 ]; then
        echo "🎉 ALL VALIDATION TESTS PASSED!"
        echo "SonarQube deployment is fully functional and ready for use."
        echo
        echo "Next steps:"
        echo "1. Access SonarQube at: http://sonarqube.local"
        echo "2. Default credentials: admin/admin (change on first login)"
        echo "3. Configure projects and quality gates as needed"
        return 0
    else
        echo "❌ SOME VALIDATION TESTS FAILED!"
        echo "Please review the failed tests and fix the issues before using SonarQube."
        echo
        echo "Common troubleshooting steps:"
        echo "1. Check if all pods are running: kubectl get pods -n $NAMESPACE"
        echo "2. Check ArgoCD sync status: kubectl get applications -n bootstrap"
        echo "3. Review pod logs: kubectl logs -l app.kubernetes.io/name=sonarqube -n $NAMESPACE"
        echo "4. Ensure hosts file is configured: platform-services/scripts/add-sonarqube-host.sh"
        return 1
    fi
}

# Function to provide troubleshooting information
provide_troubleshooting_info() {
    echo
    echo "=========================================="
    echo "TROUBLESHOOTING INFORMATION"
    echo "=========================================="
    
    echo "1. Check SonarQube pod status:"
    echo "   kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube"
    echo
    
    echo "2. Check SonarQube logs:"
    echo "   kubectl logs -l app.kubernetes.io/name=sonarqube -n $NAMESPACE --tail=50"
    echo
    
    echo "3. Check ArgoCD application status:"
    echo "   kubectl get applications sonarqube -n bootstrap"
    echo
    
    echo "4. Check HTTPRoute status:"
    echo "   kubectl get httproute sonarqube-httproute -n $NAMESPACE"
    echo
    
    echo "5. Check Gateway status:"
    echo "   kubectl get gateway traefik-gateway -n bootstrap"
    echo
    
    echo "6. Add sonarqube.local to hosts file:"
    echo "   $SCRIPT_DIR/add-sonarqube-host.sh"
    echo
    
    echo "7. Manual port forward test:"
    echo "   kubectl port-forward svc/sonarqube-sonarqube 9000:9000 -n $NAMESPACE"
    echo "   Then access: http://localhost:9000"
    echo
}

# Main execution
main() {
    echo "Starting comprehensive SonarQube deployment validation..."
    echo
    
    # Check prerequisites
    check_prerequisites
    
    # Initialize result variables
    database_result=1
    http_result=1
    health_result=1
    
    # Run database connectivity test
    if run_validation_script "test-sonarqube-database.sh" "Database Connectivity Test"; then
        database_result=0
    fi
    
    echo
    
    # Run HTTP access test
    if run_validation_script "test-sonarqube-http.sh" "HTTP Access Test"; then
        http_result=0
    fi
    
    echo
    
    # Run health check test
    if run_validation_script "validate-sonarqube-health.sh" "Health Check Test"; then
        health_result=0
    fi
    
    # Display summary
    display_summary $database_result $http_result $health_result
    
    # If any test failed, provide troubleshooting info
    if [ "$database_result" -ne 0 ] || [ "$http_result" -ne 0 ] || [ "$health_result" -ne 0 ]; then
        provide_troubleshooting_info
        exit 1
    fi
    
    exit 0
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo
        echo "This script runs comprehensive validation tests for SonarQube deployment."
        echo "It tests database connectivity, HTTP access, and health checks."
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --info, -i     Show troubleshooting information only"
        echo
        echo "Individual test scripts:"
        echo "  test-sonarqube-database.sh    - Test database connectivity"
        echo "  test-sonarqube-http.sh        - Test HTTP access via sonarqube.local"
        echo "  validate-sonarqube-health.sh  - Test health checks and resource configuration"
        echo
        exit 0
        ;;
    --info|-i)
        provide_troubleshooting_info
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac