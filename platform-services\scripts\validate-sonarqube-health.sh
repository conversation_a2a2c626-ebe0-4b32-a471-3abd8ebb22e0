#!/bin/bash

# SonarQube Health Check Validation Script
# This script validates SonarQube deployment health checks and resource configuration

set -e

NAMESPACE="platform-services"
SERVICE_NAME="sonarqube-sonarqube"
SONARQUBE_URL="http://sonarqube.local"

echo "=== SonarQube Health Check Validation ==="
echo "Namespace: $NAMESPACE"
echo "Service: $SERVICE_NAME"
echo "URL: $SONARQUBE_URL"
echo

# Function to check if pod is running
check_pod_status() {
    echo "1. Checking SonarQube pod status..."
    kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube
    
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -z "$POD_NAME" ]; then
        echo "❌ No SonarQube pod found"
        return 1
    fi
    
    POD_STATUS=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.status.phase}')
    echo "Pod Status: $POD_STATUS"
    
    if [ "$POD_STATUS" != "Running" ]; then
        echo "❌ Pod is not running"
        kubectl describe pod $POD_NAME -n $NAMESPACE
        return 1
    fi
    
    echo "✅ Pod is running"
    return 0
}

# Function to check resource configuration
check_resources() {
    echo
    echo "2. Checking resource configuration..."
    
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube -o jsonpath='{.items[0].metadata.name}')
    
    echo "Resource requests and limits:"
    kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].resources}' | jq '.'
    
    # Check if resources are within expected ranges
    MEMORY_REQUEST=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].resources.requests.memory}')
    MEMORY_LIMIT=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].resources.limits.memory}')
    CPU_REQUEST=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].resources.requests.cpu}')
    CPU_LIMIT=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].resources.limits.cpu}')
    
    echo "Memory Request: $MEMORY_REQUEST"
    echo "Memory Limit: $MEMORY_LIMIT"
    echo "CPU Request: $CPU_REQUEST"
    echo "CPU Limit: $CPU_LIMIT"
    
    # Validate minimum requirements
    if [[ "$MEMORY_REQUEST" == "2Gi" && "$MEMORY_LIMIT" == "6Gi" ]]; then
        echo "✅ Memory configuration is correct"
    else
        echo "❌ Memory configuration doesn't match expected values"
        return 1
    fi
    
    echo "✅ Resource configuration validated"
}

# Function to check probe configuration
check_probes() {
    echo
    echo "3. Checking probe configuration..."
    
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube -o jsonpath='{.items[0].metadata.name}')
    
    echo "Liveness Probe:"
    kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].livenessProbe}' | jq '.'
    
    echo
    echo "Readiness Probe:"
    kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].readinessProbe}' | jq '.'
    
    echo
    echo "Startup Probe:"
    kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].startupProbe}' | jq '.'
    
    # Check if probes are using correct endpoints
    LIVENESS_PATH=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].livenessProbe.httpGet.path}')
    READINESS_PATH=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].readinessProbe.httpGet.path}')
    STARTUP_PATH=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.spec.containers[0].startupProbe.httpGet.path}')
    
    if [[ "$LIVENESS_PATH" == "/api/system/status" && "$READINESS_PATH" == "/api/system/status" && "$STARTUP_PATH" == "/api/system/status" ]]; then
        echo "✅ Probe endpoints are correctly configured"
    else
        echo "❌ Probe endpoints are not correctly configured"
        echo "Expected: /api/system/status for all probes"
        echo "Actual - Liveness: $LIVENESS_PATH, Readiness: $READINESS_PATH, Startup: $STARTUP_PATH"
        return 1
    fi
}

# Function to test health endpoints directly
test_health_endpoints() {
    echo
    echo "4. Testing health endpoints..."
    
    # Port forward to test endpoints directly
    echo "Setting up port forward..."
    kubectl port-forward svc/$SERVICE_NAME 9000:9000 -n $NAMESPACE &
    PF_PID=$!
    
    # Wait for port forward to be ready
    sleep 5
    
    # Test system status endpoint
    echo "Testing /api/system/status endpoint..."
    if curl -f -s http://localhost:9000/api/system/status > /dev/null; then
        echo "✅ /api/system/status endpoint is accessible"
    else
        echo "❌ /api/system/status endpoint is not accessible"
        kill $PF_PID 2>/dev/null || true
        return 1
    fi
    
    # Test root endpoint
    echo "Testing root endpoint..."
    if curl -f -s http://localhost:9000/ > /dev/null; then
        echo "✅ Root endpoint is accessible"
    else
        echo "❌ Root endpoint is not accessible"
        kill $PF_PID 2>/dev/null || true
        return 1
    fi
    
    # Clean up port forward
    kill $PF_PID 2>/dev/null || true
    echo "✅ Health endpoints validated"
}

# Function to test external access
test_external_access() {
    echo
    echo "5. Testing external access via Gateway..."
    
    # Test if sonarqube.local resolves and is accessible
    if curl -f -s -I $SONARQUBE_URL > /dev/null; then
        echo "✅ SonarQube is accessible via $SONARQUBE_URL"
    else
        echo "❌ SonarQube is not accessible via $SONARQUBE_URL"
        echo "Note: Make sure sonarqube.local is added to your hosts file"
        echo "Run: platform-services/scripts/add-sonarqube-host.sh"
        return 1
    fi
}

# Function to check resource usage
check_resource_usage() {
    echo
    echo "6. Checking current resource usage..."
    
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=sonarqube -o jsonpath='{.items[0].metadata.name}')
    
    echo "Current resource usage:"
    kubectl top pod $POD_NAME -n $NAMESPACE 2>/dev/null || echo "Metrics server not available - skipping resource usage check"
}

# Main execution
main() {
    echo "Starting SonarQube health validation..."
    echo
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        echo "❌ jq is not installed or not in PATH"
        exit 1
    fi
    
    # Run all checks
    check_pod_status || exit 1
    check_resources || exit 1
    check_probes || exit 1
    test_health_endpoints || exit 1
    test_external_access || exit 1
    check_resource_usage
    
    echo
    echo "🎉 All health checks passed successfully!"
    echo "SonarQube is properly configured with health checks and resource limits."
}

# Run main function
main "$@"