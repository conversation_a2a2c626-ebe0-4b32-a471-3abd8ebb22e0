#!/bin/bash

# Script để thêm GitHub runner cho repository mới
# Usage: ./add-github-runner.sh OWNER REPO-NAME [REPLICAS] [CPU_LIMIT] [MEMORY_LIMIT] [STORAGE_SIZE]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required parameters are provided
if [ $# -lt 2 ]; then
    print_error "Usage: $0 OWNER REPO-NAME [REPLICAS] [CPU_LIMIT] [MEMORY_LIMIT] [STORAGE_SIZE]"
    print_info "Example: $0 ospgroupvn my-app 2 2 4Gi 10Gi"
    exit 1
fi

OWNER=$1
REPO_NAME=$2
REPLICAS=${3:-1}
CPU_LIMIT=${4:-2}
MEMORY_LIMIT=${5:-4Gi}
STORAGE_SIZE=${6:-5Gi}

# Validate repository name (lowercase, no special chars except hyphens)
SAFE_REPO_NAME=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g')

print_info "Adding GitHub runner for repository: $OWNER/$REPO_NAME"
print_info "Safe name: $SAFE_REPO_NAME"
print_info "Configuration: $REPLICAS replicas, $CPU_LIMIT CPU, $MEMORY_LIMIT memory, $STORAGE_SIZE storage"

# Check if files already exist
VALUES_FILE="github-runners/values-$SAFE_REPO_NAME.yaml"
APP_FILE="github-runners-apps/$SAFE_REPO_NAME-runner.yaml"

if [ -f "$VALUES_FILE" ] || [ -f "$APP_FILE" ]; then
    print_warning "Files already exist for this repository:"
    [ -f "$VALUES_FILE" ] && echo "  - $VALUES_FILE"
    [ -f "$APP_FILE" ] && echo "  - $APP_FILE"
    read -p "Do you want to overwrite? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Aborted."
        exit 0
    fi
fi

# Create values file from template
print_info "Creating values file: $VALUES_FILE"
cp github-runners/values-template.yaml "$VALUES_FILE"

# Replace placeholders in values file
sed -i "s|OWNER/REPO-NAME|$OWNER/$REPO_NAME|g" "$VALUES_FILE"
sed -i "s|REPO-NAME-runner|$SAFE_REPO_NAME-runner|g" "$VALUES_FILE"
sed -i "s|replicas: 1|replicas: $REPLICAS|g" "$VALUES_FILE"
sed -i "s|cpu: \"2\"|cpu: \"$CPU_LIMIT\"|g" "$VALUES_FILE"
sed -i "s|memory: \"4Gi\"|memory: \"$MEMORY_LIMIT\"|g" "$VALUES_FILE"
sed -i "s|size: 5Gi|size: $STORAGE_SIZE|g" "$VALUES_FILE"

print_success "Created $VALUES_FILE"

# Create ArgoCD application file from template
print_info "Creating ArgoCD application: $APP_FILE"
cp github-runners-apps/TEMPLATE-runner.yaml "$APP_FILE"

# Replace placeholders in application file
sed -i "s|REPO-NAME|$SAFE_REPO_NAME|g" "$APP_FILE"

print_success "Created $APP_FILE"

# Show summary
print_info "Summary of created files:"
echo "  - Values file: $VALUES_FILE"
echo "  - ArgoCD app: $APP_FILE"
echo ""
print_info "Next steps:"
echo "1. Review and customize the values file if needed"
echo "2. Commit and push the changes:"
echo "   git add $VALUES_FILE $APP_FILE"
echo "   git commit -m \"feat: Add GitHub runner for $OWNER/$REPO_NAME\""
echo "   git push origin main"
echo "3. ArgoCD will automatically sync and deploy the runner"
echo ""
print_success "GitHub runner setup completed for $OWNER/$REPO_NAME!"
