#!/bin/bash

# Script cải tiến để thêm ignoreDifferences cho tất cả ArgoCD Applications

# Directory chứa các ArgoCD apps
APP_DIR="platform-services/platform"
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🔧 Đang sửa metadata drift cho tất cả ArgoCD Applications..."

# ignoreDifferences config template
IGNORE_DIFF='  
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp'

# Tìm tất cả file app.yaml  
find "$BASE_DIR/$APP_DIR" -name "*-app.yaml" | while read -r app_file; do
    echo "📝 Đang xử lý: $(basename "$app_file")"
    
    # Kiểm tra nếu đã có ignoreDifferences
    if grep -q "ignoreDifferences:" "$app_file"; then
        echo "   ⚠️  Đã có ignoreDifferences, bỏ qua..."
        continue
    fi
    
    # Tìm dòng cuối cùng của file
    total_lines=$(wc -l < "$app_file")
    
    # Thêm ignoreDifferences vào cuối file
    echo "$IGNORE_DIFF" >> "$app_file"
    echo "   ✅ Đã thêm ignoreDifferences"
done

echo "🎉 Hoàn thành! Tất cả applications đã được cập nhật."