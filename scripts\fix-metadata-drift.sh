#!/bin/bash

# Script để thêm ignoreDifferences cho tất cả ArgoCD Applications
# để tránh out of sync do metadata drift

# Directory chứa các ArgoCD apps
APP_DIR="platform-services/platform"
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🔧 Đang sửa metadata drift cho các ArgoCD Applications..."

# ignoreDifferences config template
IGNORE_DIFF='
  ignoreDifferences:
    - group: "*"
      kind: "*"
      jsonPointers:
        - /metadata/creationTimestamp
        - /metadata/generation
        - /metadata/resourceVersion
        - /metadata/uid
        - /metadata/managedFields
    - group: ""
      kind: "PersistentVolumeClaim"
      jsonPointers:
        - /spec/volumeName
        - /status
    - group: "apps"
      kind: "StatefulSet"
      jsonPointers:
        - /spec/volumeClaimTemplates/*/metadata/creationTimestamp'

# Tìm tất cả file app.yaml
find "$BASE_DIR/$APP_DIR" -name "*-app.yaml" | while read -r app_file; do
    echo "📝 Đang xử lý: $(basename "$app_file")"
    
    # Kiểm tra nếu đã có ignoreDifferences
    if grep -q "ignoreDifferences:" "$app_file"; then
        echo "   ⚠️  Đã có ignoreDifferences, bỏ qua..."
        continue
    fi
    
    # Tìm dòng cuối cùng của syncPolicy
    if grep -q "syncPolicy:" "$app_file"; then
        # Thêm ignoreDifferences sau syncPolicy section
        # Tìm dòng sau syncPolicy section
        last_sync_line=$(grep -n "maxDuration:" "$app_file" | tail -1 | cut -d: -f1)
        
        if [ -n "$last_sync_line" ]; then
            # Tạo file tạm
            temp_file=$(mktemp)
            
            # Copy phần trước ignoreDifferences
            head -n "$last_sync_line" "$app_file" > "$temp_file"
            
            # Thêm ignoreDifferences
            echo "$IGNORE_DIFF" >> "$temp_file"
            
            # Copy phần còn lại (nếu có)
            tail_start=$((last_sync_line + 1))
            total_lines=$(wc -l < "$app_file")
            
            if [ "$tail_start" -le "$total_lines" ]; then
                tail -n +"$tail_start" "$app_file" >> "$temp_file"
            fi
            
            # Replace file gốc
            mv "$temp_file" "$app_file"
            echo "   ✅ Đã thêm ignoreDifferences"
        else
            echo "   ❌ Không tìm thấy maxDuration line"
        fi
    else
        echo "   ❌ Không tìm thấy syncPolicy section"
    fi
done

echo "🎉 Hoàn thành! Hãy commit và push changes."
echo ""
echo "💡 ignoreDifferences đã được thêm để bỏ qua:"
echo "   - metadata/creationTimestamp"  
echo "   - metadata/generation"
echo "   - metadata/resourceVersion"
echo "   - metadata/uid"
echo "   - metadata/managedFields"
echo "   - PVC volumeName và status"
echo "   - StatefulSet volumeClaimTemplates metadata"