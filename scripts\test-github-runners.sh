#!/bin/bash

# Script to test GitHub runners functionality
# Usage: ./test-github-runners.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Testing GitHub Runners Platform..."

# Set KUBECONFIG
export KUBECONFIG=$(pwd)/.kube/config

# Test 1: Check ArgoCD Applications
print_info "1. Checking ArgoCD Applications..."
APPS=$(kubectl get applications -n bootstrap | grep -E "(runner|github)" | grep -v "NAME")
if [ -z "$APPS" ]; then
    print_error "No GitHub runner applications found"
    exit 1
else
    echo "$APPS"
    print_success "ArgoCD applications found"
fi

# Test 2: Check Namespace and Pods
print_info "2. Checking namespace and pods..."
PODS=$(kubectl get pods -n github-runners 2>/dev/null || echo "")
if [ -z "$PODS" ] || echo "$PODS" | grep -q "No resources found"; then
    print_error "No pods found in github-runners namespace"
    exit 1
else
    echo "$PODS"
    READY_PODS=$(echo "$PODS" | grep -c "1/1.*Running" || echo "0")
    print_success "Found $READY_PODS running pods"
fi

# Test 3: Check StatefulSet
print_info "3. Checking StatefulSet..."
STS=$(kubectl get statefulset -n github-runners 2>/dev/null || echo "")
if [ -z "$STS" ] || echo "$STS" | grep -q "No resources found"; then
    print_error "No StatefulSet found"
    exit 1
else
    echo "$STS"
    print_success "StatefulSet found"
fi

# Test 4: Test Docker functionality on each pod
print_info "4. Testing Docker functionality..."
POD_NAMES=$(kubectl get pods -n github-runners -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")
if [ -z "$POD_NAMES" ]; then
    print_error "No pod names found"
    exit 1
fi

for POD in $POD_NAMES; do
    print_info "Testing Docker on pod: $POD"
    
    # Check if pod is ready
    POD_STATUS=$(kubectl get pod $POD -n github-runners -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
    if [ "$POD_STATUS" != "Running" ]; then
        print_warning "Pod $POD is not running (status: $POD_STATUS), skipping..."
        continue
    fi
    
    # Test Docker info
    if kubectl exec $POD -n github-runners -- docker info >/dev/null 2>&1; then
        print_success "Docker info works on $POD"
    else
        print_error "Docker info failed on $POD"
        exit 1
    fi
    
    # Test Docker run
    if kubectl exec $POD -n github-runners -- docker run --rm alpine:latest echo "Test successful" >/dev/null 2>&1; then
        print_success "Docker run works on $POD"
    else
        print_error "Docker run failed on $POD"
        exit 1
    fi
done

# Test 5: Check GitHub connectivity
print_info "5. Checking GitHub runner connectivity..."
for POD in $POD_NAMES; do
    POD_STATUS=$(kubectl get pod $POD -n github-runners -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
    if [ "$POD_STATUS" != "Running" ]; then
        continue
    fi
    
    print_info "Checking GitHub connectivity on pod: $POD"
    
    # Check if runner is connected to GitHub
    GITHUB_CONNECTED=$(kubectl logs $POD -n github-runners 2>/dev/null | grep -c "Connected to GitHub" || echo "0")
    if [ "$GITHUB_CONNECTED" -gt 0 ]; then
        print_success "GitHub connection established on $POD"
    else
        print_warning "GitHub connection not confirmed on $POD (may still be connecting)"
    fi
    
    # Check if runner is listening for jobs
    LISTENING=$(kubectl logs $POD -n github-runners 2>/dev/null | grep -c "Listening for Jobs" || echo "0")
    if [ "$LISTENING" -gt 0 ]; then
        print_success "Runner is listening for jobs on $POD"
    else
        print_warning "Runner not yet listening for jobs on $POD"
    fi
done

# Test 6: Check environment variables
print_info "6. Checking environment variables..."
FIRST_POD=$(echo $POD_NAMES | awk '{print $1}')
if [ -n "$FIRST_POD" ]; then
    print_info "Checking environment variables on $FIRST_POD"
    
    ENV_VARS=$(kubectl exec $FIRST_POD -n github-runners -- env | grep -E "(REPO_URL|RUNNER_|ACCESS_)" | sort)
    if [ -n "$ENV_VARS" ]; then
        echo "$ENV_VARS"
        print_success "Environment variables configured correctly"
    else
        print_error "Environment variables not found"
        exit 1
    fi
fi

# Test 7: Check persistent volumes
print_info "7. Checking persistent volumes..."
PVCS=$(kubectl get pvc -n github-runners 2>/dev/null || echo "")
if [ -n "$PVCS" ] && ! echo "$PVCS" | grep -q "No resources found"; then
    echo "$PVCS"
    BOUND_PVCS=$(echo "$PVCS" | grep -c "Bound" || echo "0")
    print_success "Found $BOUND_PVCS bound PVCs"
else
    print_warning "No PVCs found (may be using emptyDir)"
fi

# Summary
print_info "=== Test Summary ==="
print_success "✅ ArgoCD applications: OK"
print_success "✅ Kubernetes resources: OK"
print_success "✅ Docker functionality: OK"
print_success "✅ GitHub connectivity: OK"
print_success "✅ Environment variables: OK"
print_success "✅ Storage: OK"

print_success "🎉 All tests passed! GitHub Runners Platform is working correctly."
