#!/bin/bash

echo "🧪 Testing Kafdrop functionality with Kafka SASL..."

# Set KUBECONFIG
export KUBECONFIG=$(pwd)/.kube/config

echo "1. 📋 Checking Kafdrop pod status..."
kubectl get pods -n platform-services -l app=kafdrop

echo -e "\n2. 🔗 Testing Kafdrop UI accessibility..."
kubectl run test-kafdrop-ui --image=curlimages/curl --rm -it --restart=Never -n platform-services -- curl -s -o /dev/null -w "%{http_code}" http://kafdrop:9000

echo -e "\n3. 📝 Testing Kafka topic creation via kafka-topics command..."
kubectl run kafka-test-create-topic --image=bitnami/kafka:latest --rm -it --restart=Never -n platform-services -- \
  kafka-topics.sh --bootstrap-server kafka:9092 \
  --command-config /dev/stdin \
  --create --topic test-kafdrop-topic --partitions 3 --replication-factor 1 <<EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="user1" password="password1";
EOF

echo -e "\n4. 📨 Testing Kafka message production..."
kubectl run kafka-test-producer --image=bitnami/kafka:latest --rm -it --restart=Never -n platform-services -- \
  sh -c 'echo "Test message from Kafdrop validation" | kafka-console-producer.sh --bootstrap-server kafka:9092 --topic test-kafdrop-topic --producer.config /dev/stdin' <<EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="user1" password="password1";
EOF

echo -e "\n5. 📖 Testing Kafka message consumption..."
kubectl run kafka-test-consumer --image=bitnami/kafka:latest --rm -it --restart=Never -n platform-services -- \
  timeout 10s kafka-console-consumer.sh --bootstrap-server kafka:9092 --topic test-kafdrop-topic --from-beginning --consumer.config /dev/stdin <<EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="user1" password="password1";
EOF

echo -e "\n6. 📋 Listing all topics to verify..."
kubectl run kafka-test-list-topics --image=bitnami/kafka:latest --rm -it --restart=Never -n platform-services -- \
  kafka-topics.sh --bootstrap-server kafka:9092 --list --command-config /dev/stdin <<EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="user1" password="password1";
EOF

echo -e "\n7. 🧹 Cleanup test topic..."
kubectl run kafka-test-delete-topic --image=bitnami/kafka:latest --rm -it --restart=Never -n platform-services -- \
  kafka-topics.sh --bootstrap-server kafka:9092 --delete --topic test-kafdrop-topic --command-config /dev/stdin <<EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="user1" password="password1";
EOF

echo -e "\n✅ Kafdrop functionality test completed!"
echo "🌐 Access Kafdrop UI at: http://kafdrop.local"
echo "📋 You should be able to:"
echo "   - View topics and partitions"
echo "   - Browse messages"
echo "   - View consumer groups"
echo "   - Monitor broker information"
echo ""
echo "⚠️  Note: Kafdrop is READ-ONLY. To create topics/messages, use:"
echo "   - kafka-topics.sh command line tools"
echo "   - Application producers/consumers"
echo "   - Other admin tools"
