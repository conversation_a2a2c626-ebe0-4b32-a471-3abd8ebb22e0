#!/bin/bash

# <PERSON>ript test kết nối PostgreSQL qua Traefik TCP Gateway
# Đảm bảo rằng kết nối hoạt động và có failover handling

echo "=== Test PostgreSQL TCP Connection via Traefik Gateway ==="
echo "Target: *************:5432"
echo ""

# Kiểm tra nếu psql có sẵn
if ! command -v psql &> /dev/null; then
    echo "❌ psql không tìm thấy. Vui lòng cài đặt postgresql-client"
    echo "   Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "   RHEL/CentOS: sudo yum install postgresql"
    echo "   macOS: brew install postgresql"
    exit 1
fi

# Test 1: Ping connectivity
echo "🔍 Test 1: Kiểm tra network connectivity..."
if ping -c 1 ************* &> /dev/null; then
    echo "✅ Network connectivity OK"
else
    echo "❌ Không thể ping đến *************"
    exit 1
fi

# Test 2: Port connectivity
echo ""
echo "🔍 Test 2: Kiểm tra port 5432 accessibility..."
if nc -z -w5 ************* 5432 2>/dev/null; then
    echo "✅ Port 5432 có thể truy cập"
else
    echo "❌ Port 5432 không thể truy cập"
    echo "   Có thể Traefik Gateway chưa được cấu hình hoặc PostgreSQL không chạy"
    exit 1
fi

# Test 3: PostgreSQL connection
echo ""
echo "🔍 Test 3: Kiểm tra PostgreSQL authentication..."
export PGPASSWORD="postgres123"

# Test connection với timeout
if timeout 10s psql -h ************* -p 5432 -U postgres -d postgres -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ PostgreSQL connection thành công"
    
    # Hiển thị thông tin version
    echo ""
    echo "📊 PostgreSQL Information:"
    psql -h ************* -p 5432 -U postgres -d postgres -c "SELECT version();"
    
    # Test query performance
    echo ""
    echo "⏱️  Performance Test:"
    time psql -h ************* -p 5432 -U postgres -d postgres -c "SELECT COUNT(*) FROM pg_stat_activity;"
    
else
    echo "❌ Không thể kết nối PostgreSQL"
    echo "   Kiểm tra:"
    echo "   - PostgreSQL service đang chạy trong cluster"
    echo "   - Mật khẩu đúng (hiện tại: postgres123)"
    echo "   - TCPRoute đã được apply"
    echo "   - Gateway listener đã active"
    exit 1
fi

# Test 4: Stress test - multiple connections
echo ""
echo "🔍 Test 4: Stress test với multiple connections..."
success_count=0
total_tests=5

for i in $(seq 1 $total_tests); do
    if timeout 5s psql -h ************* -p 5432 -U postgres -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
        success_count=$((success_count + 1))
        echo "  Connection $i/5: ✅"
    else
        echo "  Connection $i/5: ❌"
    fi
done

echo ""
echo "📈 Stress Test Results: $success_count/$total_tests connections thành công"

if [ $success_count -eq $total_tests ]; then
    echo "🎉 Tất cả tests PASSED! PostgreSQL TCP Gateway hoạt động tốt"
    exit 0
else
    echo "⚠️  Một số connections thất bại. Kiểm tra cấu hình và stability"
    exit 1
fi