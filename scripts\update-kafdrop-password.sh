#!/bin/bash

# Script to update Kafdrop ConfigMap with current Kafka password
# This should be run whenever Kafka passwords change

echo "🔄 Updating Kafdrop ConfigMap with current Kafka password..."

# Get current password from Kafka secret
CURRENT_PASSWORD=$(kubectl get secret kafka-user-passwords -n platform-services -o jsonpath='{.data.system-user-password}' | base64 -d)

if [ -z "$CURRENT_PASSWORD" ]; then
    echo "❌ Failed to get password from Kafka secret"
    exit 1
fi

echo "✅ Retrieved password from Kafka secret"

# Update ConfigMap
kubectl patch configmap kafka-properties -n platform-services --patch "
data:
  kafka.properties: |
    security.protocol=SASL_PLAINTEXT
    sasl.mechanism=PLAIN
    sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username=\"user1\" password=\"$CURRENT_PASSWORD\";
"

if [ $? -eq 0 ]; then
    echo "✅ ConfigMap updated successfully"
    
    # Restart Kafdrop deployment to pick up new config
    echo "🔄 Restarting Kafdrop deployment..."
    kubectl rollout restart deployment/kafdrop -n platform-services
    
    echo "⏳ Waiting for rollout to complete..."
    kubectl rollout status deployment/kafdrop -n platform-services --timeout=120s
    
    if [ $? -eq 0 ]; then
        echo "✅ Kafdrop updated successfully!"
        echo "🌐 Access Kafdrop at: http://kafka-ui.local"
    else
        echo "❌ Failed to restart Kafdrop"
        exit 1
    fi
else
    echo "❌ Failed to update ConfigMap"
    exit 1
fi