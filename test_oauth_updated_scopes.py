#!/usr/bin/env python3
"""
Script để test OAuth với scopes đã được cập nhật (loại bỏ im:message:send).
"""

import requests
import json
import time
from urllib.parse import parse_qs, urlparse

def test_oauth_endpoint():
    """Test OAuth authorization endpoint."""
    
    print('🧪 TESTING OAUTH WITH UPDATED SCOPES')
    print('=' * 60)
    
    base_url = "https://common.ospgroup.io.vn/osp-agent"
    
    try:
        print('📡 Testing OAuth authorize endpoint...')
        response = requests.get(f"{base_url}/oauth/authorize", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print('✅ OAuth endpoint is working!')
            print(f'📋 Response data:')
            print(f'  Authorization URL: {data.get("authorization_url", "N/A")}')
            print(f'  Callback URI: {data.get("callback_uri", "N/A")}')
            print(f'  Scopes: {data.get("scopes", "N/A")}')
            print()
            
            # Parse and analyze the authorization URL
            auth_url = data.get("authorization_url", "")
            if auth_url:
                parsed = urlparse(auth_url)
                params = parse_qs(parsed.query)
                
                if 'scope' in params:
                    scopes = params['scope'][0].replace('+', ' ')
                    scope_list = scopes.split(' ')
                    
                    print('🔍 SCOPE ANALYSIS:')
                    print('-' * 30)
                    print(f'Total scopes: {len(scope_list)}')
                    
                    # Check for problematic scope
                    if 'im:message:send' in scope_list:
                        print('❌ PROBLEM: im:message:send is still present!')
                        return False
                    else:
                        print('✅ GOOD: im:message:send has been removed')
                    
                    # Check for required messaging scopes
                    required = ['im:message:send_as_bot', 'im:message', 'im:message:readonly']
                    missing = []
                    
                    for req_scope in required:
                        if req_scope in scope_list:
                            print(f'✅ {req_scope}: Present')
                        else:
                            print(f'❌ {req_scope}: Missing')
                            missing.append(req_scope)
                    
                    if not missing:
                        print('✅ All required messaging scopes are present!')
                        return True
                    else:
                        print(f'❌ Missing scopes: {missing}')
                        return False
                        
            return True
            
        else:
            print(f'❌ OAuth endpoint error: HTTP {response.status_code}')
            print(f'Response: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error testing OAuth endpoint: {e}')
        return False

def test_health_endpoint():
    """Test health endpoint."""
    
    print('\n🏥 TESTING HEALTH ENDPOINT')
    print('=' * 60)
    
    base_url = "https://common.ospgroup.io.vn/osp-agent"
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        
        if response.status_code == 200:
            print('✅ Health endpoint is working!')
            print(f'Response: {response.text}')
            return True
        else:
            print(f'❌ Health endpoint error: HTTP {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Error testing health endpoint: {e}')
        return False

def test_oauth_setup_page():
    """Test OAuth setup page."""
    
    print('\n🔧 TESTING OAUTH SETUP PAGE')
    print('=' * 60)
    
    base_url = "https://common.ospgroup.io.vn/osp-agent"
    
    try:
        response = requests.get(f"{base_url}/oauth/setup", timeout=10)
        
        if response.status_code == 200:
            print('✅ OAuth setup page is accessible!')
            print(f'Content length: {len(response.text)} characters')
            
            # Check if the page contains expected content
            if 'OAuth Setup' in response.text:
                print('✅ Setup page contains expected content')
                return True
            else:
                print('⚠️  Setup page accessible but content may be different')
                return True
        else:
            print(f'❌ Setup page error: HTTP {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Error testing setup page: {e}')
        return False

def generate_test_oauth_url():
    """Generate a test OAuth URL for manual testing."""
    
    print('\n🔗 MANUAL OAUTH TESTING URL')
    print('=' * 60)
    
    base_url = "https://common.ospgroup.io.vn/osp-agent"
    
    print('📋 For manual testing, use this URL:')
    print(f'{base_url}/oauth/authorize')
    print()
    print('📝 Expected behavior:')
    print('1. Should return JSON with authorization_url')
    print('2. authorization_url should NOT contain im:message:send')
    print('3. authorization_url should contain im:message:send_as_bot, im:message, im:message:readonly')
    print()
    print('🌐 You can also test the setup page:')
    print(f'{base_url}/oauth/setup')
    
    return True

def main():
    """Main function."""
    
    print('🚀 OSP-AGENT OAUTH TESTING WITH UPDATED SCOPES')
    print('=' * 70)
    print('Testing deployment with removed im:message:send scope')
    print()
    
    # Test health endpoint first
    health_ok = test_health_endpoint()
    
    # Test OAuth endpoint
    oauth_ok = test_oauth_endpoint()
    
    # Test setup page
    setup_ok = test_oauth_setup_page()
    
    # Generate manual test URL
    generate_test_oauth_url()
    
    print('\n📋 TEST SUMMARY')
    print('=' * 70)
    
    if health_ok and oauth_ok and setup_ok:
        print('✅ ALL TESTS PASSED!')
        print('✅ Deployment with updated scopes is working correctly')
        print('✅ im:message:send has been successfully removed')
        print('✅ Required messaging scopes are present')
        print()
        print('🎯 NEXT STEPS:')
        print('1. Test OAuth authorization flow manually')
        print('2. Verify messaging functionality works')
        print('3. Monitor for any authorization errors')
        print('4. Check application logs for any issues')
        
        return True
    else:
        print('❌ SOME TESTS FAILED!')
        print('   Please check the issues above and fix them')
        
        if not health_ok:
            print('   - Health endpoint is not working')
        if not oauth_ok:
            print('   - OAuth endpoint has issues')
        if not setup_ok:
            print('   - Setup page is not accessible')
            
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
